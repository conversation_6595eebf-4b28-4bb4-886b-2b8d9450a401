ehs:
  metadata_keywords:
    claim:
      claimNumber:
        - "patient no"
        - "subscriber no"
        - "claim number"
        - "claim#"
        - "claim #"
        - "claim no."
        - "policy/group#"
        - " caim"
        - "policy number"
        - "cl #"
        - "ciaim #"
        - "claim"
      dateOfInjuryFrom:
        - "date of injury:"
        - "date of injury"
        - "dateofinjury:"
        - "dateofinjury"
        - "d.o.i:"
        - "d.o.i"
        - "doi:"
        - "doi"
    claimant:
      claimantName:
        - "claimant name:"
        - "claimant name"
        - "patient name:"
        - "patient name"
        - "claimantname:"
        - "claimantname"
        - "patientname:"
        - "patientname"
        - "patient information:"
        - "patient information"
        - "patient's name:"
        - "patient's name"
        - "injured worker:"
        - "injured worker"
        - "claimant:"
        - "claimant"
        - "patient:"
        - "patient"
        - "name:"
        - "name"
        - "re:"
        - "re ..:"
        - "re..:"
        - "re"
      dateOfBirth:
        - "date of birth:"
        - "date of birth"
        - "dateofbirth:"
        - "dateofbirth"
        - "dob:"
        - "dob"
        - "d.o.b:"
        - "d.o.b"

    physician:
      state:
        - "state:"
        - "state"
      facilityName:
        - "facility name:"
        - "facility name"
        - "provider facility:"
        - "provider facility"
        - "facilityname:"
        - "facilityname"
        - "providerfacility:"
        - "providerfacility"
        - "facility:"
        - "facility"
        - "practice:"
        - "practice"
      physicianName:
        - "physician name:"
        - "physician name"
        - "physicianname:"
        - "physicianname"
        - "provider name:"
        - "provider name"
        - "ordering physician:"
        - "ordering physician"
        - "treating physician:"
        - "treating physician"
        - "requesting physician:"
        - "requesting physician"
        - "referring physician:"
        - "referring physician"
        - "requesting provider:"
        - "requesting provider"
        - "ordering provider:"
        - "ordering provider"
        - "requesting provider name:"
        - "requesting provider name"
        - "referring physician name:"
        - "referring physician name"
        - "requesting doctor:"
        - "requesting doctor"
        - "treating doctor:"
        - "treating doctor"
        - "requestingphysician:"
        - "requestingphysician"
        - "referringphysicianname:"
        - "referringphysicianname"
        - "providername:"
        - "providername"
        - "treatingphysician:"
        - "treatingphysician"
        - "referringphysician:"
        - "referringphysician"
        - "orderingphysician:"
        - "orderingphysician"
        - "requestingprovider:"
        - "requestingprovider"
        - "requestingprovidername:"
        - "requestingprovidername"
        - "treatingdoctor:"
        - "treatingdoctor"
        - "requestingdoctor:"
        - "requestingdoctor"
        - "orderingprovider:"
        - "orderingprovider"
        - "provider:"
        - "provider"
        - "treatingdr:"
        - "treatingdr"
        - "requestingdr:"
        - "requestingdr"
        - "requesting dr."
        - "requesting dr:"
        - "requesting dr"
        - "treating dr."
        - "treating dr:"
        - "treating dr"
        - "name:"
        - "name"
        - "from:"
        - "from"
      npi:
        - "npi number:"
        - "npi number"
        - "npinumber:"
        - "npinumber"
        - "npi#:"
        - "npi#"
        - "npi:"
        - "npi #:"
        - "npi #"
        - "npi"
      keyContactName:
        - "key contact name:"
        - "key contact name"
        - "contact name:"
        - "contact name"
        - "keycontactname:"
        - "keycontactname"
        - "contactname:"
        - "contactname"
        - "contact person:"
        - "contact person"
        - "company contact:"
        - "company contact"
        - "company contacts:"
        - "company contacts"
        - "companycontact:"
        - "companycontact"
        - "companycontacts:"
        - "companycontacts"
        - "contactperson:"
        - "contactperson"
        - "provider contact person:"
        - "provider contact person"
        - "providercontactperson:"
        - "providercontactperson"
        - "contact:"
        - "contact"
      specialty:
        - "specialty:"
        - "specialty"
      emailAddress:
        - "email address:"
        - "email address"
        - "emailaddress:"
        - "emailaddress"
        - "email:"
        - "email"
      phoneNumber:
        - "phone number#:"
        - "phone number#"
        - "phone number #:"
        - "phone number #"
        - "phone number:"
        - "phone number"
        - "phonenumber#"
        - "phonenumber#:"
        - "phonenumber #"
        - "phonenumber #:"
        - "phonenumber:"
        - "phonenumber"
        - "company phone:"
        - "company phone"
        - "companyphone:"
        - "companyphone"
        - "phone #:"
        - "phone #"
        - "phone#:"
        - "phone#"
        - "phone:"
        - "phone"
        - "direct tel:"
        - "direct tel"
        - "directtel:"
        - "directtel"
        - "tel:"
        - "tel"
        - "p:"
        - "p"
      faxNumber:
        - "fax number#:"
        - "fax number#"
        - "fax number:"
        - "fax number"
        - "faxnumber#:"
        - "faxnumber#"
        - "faxnumber:"
        - "faxnumber"
        - "fax number #:"
        - "fax number #"
        - "company fax:"
        - "company fax"
        - "companyfax:"
        - "companyfax"
        - "direct fax:"
        - "direct fax"
        - "directfax:"
        - "directfax"
        - "fax #:"
        - "fax #"
        - "fax#:"
        - "fax#"
        - "fax:"
        - "fax"
        - "f"
        - "f:"
      address:
        - "address:"
        - "address"
      city:
        - "city:"
        - "city"
      zip:
        - "zip code:"
        - "zip code"
        - "zipcode:"
        - "zipcode"
        - "zip:"
        - "zip"
    employer:
      name:
        - "employer name:"
        - "employer name"
        - "employers name:"
        - "employers name"
        - "employersname:"
        - "employersname"
        - "employer:"
        - "employer#:"
        - "employer#"
        - "employer #:"
        - "employer #"
        - "employer :"
        - "employer"
        - "name:"
        - "name"
        - "EMP:"
        - "EMP"
    adjuster:
      company:
        - "company:"
        - "company"
      address:
        - "address:"
        - "address"
      city:
        - "city:"
        - "city"
      state:
        - "state:"
        - "state"
      zip:
        - "zip code:"
        - "zip code"
        - "zipcode:"
        - "zipcode"
        - "zip:"
        - "zip"
      phoneNumber:
        - "phone number#:"
        - "phone number#"
        - "phone number #:"
        - "phone number #"
        - "phone number:"
        - "phone number"
        - "phonenumber#"
        - "phonenumber#:"
        - "phonenumber #"
        - "phonenumber #:"
        - "phonenumber:"
        - "phonenumber"
        - "company phone:"
        - "company phone"
        - "companyphone:"
        - "companyphone"
        - "phone #:"
        - "phone #"
        - "phone#:"
        - "phone#"
        - "phone:"
        - "phone"
        - "direct tel:"
        - "direct tel"
        - "directtel:"
        - "directtel"
        - "tel:"
        - "tel"
        - "p:"
        - "p"
      faxNumber:
        - "fax number#:"
        - "fax number#"
        - "fax number:"
        - "fax number"
        - "faxnumber#:"
        - "faxnumber#"
        - "faxnumber:"
        - "faxnumber"
        - "fax number #:"
        - "fax number #"
        - "company fax:"
        - "company fax"
        - "companyfax:"
        - "companyfax"
        - "direct fax:"
        - "direct fax"
        - "directfax:"
        - "directfax"
        - "fax#:"
        - "fax#"
        - "fax:"
        - "fax #:"
        - "fax #"
        - "fax"
        - "f"
        - "f:"
      emailAddress:
        - "email address:"
        - "email address"
        - "emailaddress:"
        - "emailaddress"
        - "email:"
        - "email"
      adjusterName:
        - "adjuster name:"
        - "adjuster name"
        - "adjustername:"
        - "adjustername"
        - "adjuster:"
        - "adjuster"
        - "name:"
        - "name"
    documentDate:
      - "request date:"
      - "request date"
      - "requestdate:"
      - "requestdate"
      - "date:"
      - "date"
    receiptDate:
      - "date:"
      - "date"
    senderName:
      - "from:"
      - "from"
    rushFlag:
    months:
      jan: "01"
      feb: "02"
      mar: "03"
      apr: "04"
      may: "05"
      jun: "06"
      jul: "07"
      aug: "08"
      sep: "09"
      oct: "10"
      nov: "11"
      dec: "12"


  qa_results:
    namingData:
      claimNumber: null
      patientName: null
      receiptDate: null
      senderName: null
      documentDate: null
      documentState: null
      providerOrFacilityName: null
    docName: null
    docLink: null
    docType: "RFA"
    docConfidence: null
    pageRefStart: null
    pageRefEnd: null
    claimNumber: null
    patientName: null
    metaData:
      docDate: null
      docReceivedDate: null
      expeditedFlag: null
      rushFlag: null
      jurisdiction: null
      senderName: null
      newSubmission: null
      resubmission: null
      writtenConfirmPriorOralRequest: null
      physician:
        type: null
        npi: null
        firstName: null
        lastName: null
        physicianName: null
        facilityName: null
        keyContactName: null
        specialty: null
        credentials: null
        emailAddress: null
        phoneNumber: null
        faxNumber: null
        address:
          address1: null
          address2: null
          city: null
          state: null
          zip: null
      adjuster:
        adjusterId: null
        company: null
        keyContact: null
        firstName: null
        lastName: null
        middleName: null
        suffix: null
        address:
          address1: null
          address2: null
          city: null
          state: null
          zip: null
        phoneNumber: null
        phoneExt: null
        faxNumber: null
        emailAddress: null
      attorney:
        type: null
        company: null
        firstName: null
        lastName: null
        middleName: null
        suffix: null
        address:
          address1: null
          address2: null
          city: null
          state: null
          zip: null
        phoneNumber: null
        phoneExt: null
        faxNumber: null
        emailAddress: null
      employer:
        name: null
        phoneNumber: null
        faxNumber: null
        contactEmail: null
        taxId: null
        address:
          address1: null
          address2: null
          city: null
          state: null
          zip: null
      claim:
        claimNumber: null
        dateOfInjuryFrom: null
        jurisState: null
      claimant:
        firstName: null
        lastName: null
        middleName: null
        claimantName: null
        suffix: null
        dateOfBirth: null
        gender: null
        ssn: null
        address:
          address1: null
          address2: null
          city: null
          state: null
          zip: null
        phoneNumber: null
        emailAddress: null
        employeeId: null
      treatments: [ ]

  prompts:
    prompt_beginning: "You are data extractor, your task extract all data related to patient, physician and adjuster. All variables are equal among themselves. There can be a lot of missing values, you don't need to make up your own. Try to split street adress into street and space(suite, flat or office) where necessary.\n physician_type can be only Requesting, Treating, Referring, Ordering, Secondary, Primary.\n Adjuster variables refer to information about insurance.\n  Extract place_of_work variable only if there is any keyword like employer near.\n \n Raw input text: "
    example1_text: 'athena 03-26-2024 1:34 PM ET 614-211386057 pq 2 of 44 THE SAN ANTONIO ORTHOPAEDIC GROUP. L.L.P. : 19138 US Hwy 281 North Suite 100, SAN ANTONIO TX 78258-4988 MARTINEZ, Sophia L (id #546449, dob: 24/03/1959) THE SAN ANTONIO ORTHOPAEDIC GROUP, L.L.P. Surgery Request ** STAT SURGERY REQUEST ** Date 03/26/2024 ** STAT SURGERY REQUEST ** Submitted: Attention: Pre-Authorization Ph ************ F: ************ Surgeon: Daniel Anderson, M.D. 2553 Babcock Road Ste 500 San Antonio, TX 78228. License# T2753 NPI#********** PEER REVIEW CALL: ************ Assistant Surgeon: Patient: Sophia L Martinez 24/03/1959 1504 Lockhill Selma Rd San Antonio, TX, 78213-2243 Home: (************* Work: \ss#: EmP: doi: 12/15/2023 I nsurance TRISTAR RISK MANAGEMENT Carrier: Adjuster: CHRISTINE AVILA claim #: 234446865 Procedure Code: ARTHRODESIS, WRIST (SURG) - cpt 25810 THE SAN ANTONIO ORTHOPAEDIC GROUP, L.L.P. Bonewitz, Jane M (ID: 744832), DOB: 24/03/1959 athena 03-26-2024 1:34 PM ET 614-211386057 pq 3 of 44 THE SAN ANTONIO ORTHOPAEDIC GROUP. L.L.P. : 19138 US Hwy 281 North Suite 100, SAN ANTONIO TX 78258-4988 BONEWITZ, Jane M (id #744832, dob: 24/03/1959) I Clinica! Jane is a 74yo F who is sp R DR ORIF on 1/3, developed drainage from the wound, sp 2/7. Infomation: HRW removal, I&D, wound culture culture grew staph a, antibx sensitive - off antibx ! we reviewed her xrays in serial progression and discussed her clinical setting. she tried 1 wk in a splint where she did gentle activties w developement of pain, swelling at the wrist. worsened pain and erythemia. now w painful non union volar splint continue light duty non weight bearing to extremity will prophylactically treat w antibx to ensure not residual infection we had a long discussion about her wrist state and reconstruction options.. unfortunately she has developed a painful non union, given the lack of reconstructable radiocarpal joint, will plan for wrist fusion as long as no concern for infection on labs. and imaging to OR for R wrist fusion w L lliac crest bone graft 1. Closed fracture of distal end of radius - Right S52.501K: Unspecified fracture of the lower end of right radius, subsequent encounter for closed fracture with nonunion ARTHRODESIS, WRIST (SURG) - Priority: STAT Note to Provider: 04/03/2024 0SC R wrist arthrodesis with left iliac crest bone graft 25810 S52.501K, T81.42XS 2 HOURS MAC/Ax Block Allergies- iodine, demerol, codeine, meperidine Wrist fusion. plate, dorsal spanning plate, mini.c-arm, mini.frag.set. Duration of Surgery (hours): 2 HOURS Admission Status: Outpatient Side: RIGHT Performing Provider: Dr. Michaelia Sunderland MD Surgical Assistant (text): Lio. Anesthesia: MAC Type of Second Anesthesia: Axillary Medical Clearance: N/A I Special Instruments / Equipment: wrist fusion plate, I dorsal spanning plate, mini c-arm, mini frag-set. Date of surgery/procedure: 04/03/2024 Place of service: AMBULATORY SURGICAL CENTER Procedure code: 25810 Authorization: TRISTAR RISK MANAGEMENT 2. Deep incisional surgical site infection T81.42xs: Infection following a procedure, deep incisional surgical site, sequela 3. Osteoporosis 4. Essential hypertension 110: Essential (primary) hypertension. Return to Office. David Espinoza, MD for TEAM ONLY/BLOCKED at RIDGEWOOD ORTHOPAEDIC OFFICE on 03/28/2024 at 03:30 pm Date of 4/3/2024 ** STAT SURGERY REQUEST ** Procedure: Facility: Orthopaedic Surgery Center of San Antonio #400 Concord Plaza Drive, Suite 200 THE SAN ANTONIO ORTHOPAEDIC GROUP, L.L.P. MARTINEZ, Sophia L (ID: 744832), DOB: 24/03/1959'
    example2_text: ' May 30 24, 09:11a dr lewis ********** p.1 FAX COVER SHEET Alexander B. Lewis, M.D. Practice of Psychiatry & Psychoanalysis 1308 West Causeway Approach, Suite D Mandeville, LA 70471 Phone ************ Fax ************ Nicolette Boud -Harris FAX: 2|4-S9y-S140 To: Pre Cert RE:Dnillams S|3024 DATE: FROM:OdISOn Number of Pages including cover sheet: 0 Please email <NAME_EMAIL> 10I0 re0Rst caim #: 73q099 This fax contains private information and is intended for the recipient only, If you received this by mistake please call ************ May 30 24, 09:11a dr macgregor ********** p.2 ALEXANDER B. LEWIS, M.D. A Professional Corporation 1308 w. Causeway Approach, Suite D Mandeville, LA 70471 Office: ************ Fax: ************ March 29, 2024 Via fax: (************* Mr. Jesse Pham American Contractors P.O. Box 2805 Clinton, IA 52733 Re: Benjamin Harrison Claim No. 13434788 Dear Mr. Pham: I am writing to request authorization for eight additional psychiatric treatment sessions at a frequency of every third week for Mr. Benjamin Harrison. Pursuant to this request, I am including a completed Form 1010, his original evaluative summary, and copies of his six last psychiatric treatment session notes. As you can see from his notes, Mr. Harrison made very good progress in his psychiatric treatment. When I first saw him, he was so withdrawn that he secluded himself in his bedroom day and night for weeks and months on end. When he came to me for evaluation, he could barely speak because he was so withdrawn. He is now significantly more functional but not symptom free. Some of his symptoms of PTsD are persisting even though they, too, are markedly improved. with continued treatment, we would aim for full symptomatic remission and return to function (hopefully to the point of returning to work) If you need further information, please do not hesitate to contact me.. Alexander B. Lewis, M.D. JRM:rm Enclosures May 30 24, 09:11a dr lewis ********** p.3 LWC FORM 101O - REQUEST OF AUTHORIZATION/CARRIER OR SELF iNSURED EMPLOYER RESPONSE PLEASE PRINT OR TYPE SECTION 1. IDENTIFYiNG INFORMATION - To Be Filled Out By Health Care Provider Last Name: First: Middle: Street Address, City, State, Zip:. Benjamin Harrison 320 Bowman St. Morgan City, LA 70380 Last 4 Digits of Social Security Number:. Date of Birth: Phone Number: Date of Injury 03/11/1998 (************* 12/06/2022 Employers Name: Street Address, City, State, Zip: Phone Number: Name: Adjuster: Claim Number (if known) American Contractors Jesse Pham 23901788 Street Address, City, State Zip: Email Address: Phone Number: Fax Number: PO Box 2805 Clinton, IA 52733 (************* (************* Requesting Health Care Provider: Phone Number: Fax Number: Alexander B. Lewis, MD ************ ************ Street Address, City, State Zip: Email: 1308 W. Causeway Approach, Suite D, Mandeville, LA 70471 <EMAIL> Diagnosis: CPT/DRG Code: ICD/DSM Code: Major Depressive, PTSD, Panic Disorder, Somatic w/ Pain. 90807 F32.2, F43.10, F41.0, F45.1 Requested Treatment or Testing (Attach Supplement if Needed): 8 psychiatric treatment sessions at a frequency of once every 3 weeks. Reason for Treatment or Testing (Attach Supplement If Needed):. Please See attached (Following is the required minimum information for Request of Authorization (LAC 40:2715 (C)) 0 History provided to the level of condition and as provided by Medical Treatment Schedule Physical Findings/Clinical Tests P x0>-w Documented functional improvements from prior treatment Test/imaging results Treatment Plan including services being requested along with the frequency and duration Faxed to the Carrier/Self Insured Employer on this the. I hereby certify that this completed form and above required information was. 7024 day of Emailed (day) (month) (year) Signature of Health Care Provider:. Printed Name: John Macgregor, MD SECTION 3. RESPONSE OF CARRIER/SELF INSURED EMPLOYER FOR AUTHORIZATION The requested Treatment or Testing is approved. The requested Treatment or Testing is approved with modifications (Attach summary of reasons and explanation of any modifications). The requested Treatment or Testing is denied because Nat in accordance with Medical Treatment Schedule or R.S.23:1203.t(D) (Attach summary of reasons) 0 The request, or a portion thereof, is not related to the on-the-job injury The claim is being denied as non-compensable. Other (Attach brief explanation) C Faxed to the Health Care Provider (and to the Attorney of A Claimant if one exists, if denied or approved with. R I hereby certify that this response of Carrier/Self Irsured Employer for Authorizat on was. modification) on this the R day of Emailed (day) (month) (year) R Signature of Carrier/$elf Insured Employer or Utilization Review Company:. [Printed Name: The prior denied or approved with modification request is now approved to the Health Care Provider and Attorney of Claimant Faxed if one exists nn this thg I heraby certify that this response of Carrier/Self Insured Employer for Authorization was. day of (day) (month) (year) Emailed Signature of Carrier/Self Insured Employer or Utilization Review Company: Printed Name:'
    example3_text: "From: Mariana Jimenez Fax: 17138804400 To: Fax: (************* Page: 2 ot 8 05/29/2024 7:14 AM Nova Medical Centers Alodical Centers Centralized Biling Office ova 550 Club Drive, Suite 244 Texas TDI Certified Montgomery TX 77316 HCN Provider Fropriet ary Nors Sottware. Ph:************ Fax: ************ Centralized Scheduling Department: 88-333-5018a PT/OT Pre-Authorization Request Form. Patient Name: Olivia Grace Thompson SSN# *********** DOB 6/30/1985 Address: 125 w Saint Charles Brownsville, TX 78520 DOI: 8/11/2024 Sprain of ligaments of lumbar spine, subsequent encounter s33.5xxd. DX: strain of muscle, fascia and tendon of lower back, subsequent encounter s39.012d Employer: Brownsville ISD -Facilities/Maintenance 972/912 Facility Brownsville (2952 Boca Chica Blvd.BrownsvilleTX 78521)/Ph: ************/Fax************ Tax ID: 84-2131917 NPI:********** Treating Dr: Mark Balley, NP AP120564TX Therapist: Claim#: Ins. Carrier: TRI-STAR Adjuster. Number of Visits Requested: 6(3 times a week for 2 weeks) Number of Visits Authorized: Beginning Authorization Total PT/OT Visits to Date: DBate: Ending Authorization Dato: Approved by: Approval Date: Written Approval Required: ************ Yes Fax or Email to: Authorization Number: Authorization Number Obtained 5/29/2024 Request Dato: By: Due to unknown future progression of this patient, any combination of CPT codes below may be warranted for this prescription. Any procedures and units performed are provided to reach MMl at the earliestpossiblo date CPT 97112 97535 97530 97140 From: Mariana Jimenez. Fax: 17138804400 To: Fax: (************* Page: 3 of 8 05/29/2024 7:14 AM NicCest Occufle? Easy-Script Powered By Froprietary Nove Satt ware Company: Brownsville I$D -Facillties/Maintenance 972/912 Claim Number: Company Phono: 956-548-8061e Date: 05/28/2024 Patient Name: Olivia Grace Thompson Date of Injury: 04/12/2024 Patient Phone: ************ Diagnosis: Sprain of unspecified site of RIGHT knee, subsequent encounter S83.91XD. Subjective Complaints (what pationt states): Patient prosents for follow up injury to right kneo ps 9/10 only at night and with movement ps 5/10. Improvement is 20 % since dol.ac.. Knee: Patient presents to the clinic for a: Right knee complaint. Right: Reviewed Family, Past Medical, Social History, and Review of Systems from 4/15/2024 and there has been no change. Exam/Results: Physical: Blood pressure 114 / 78. Pulse 82. Resplratory rate 17. Helght (Inches) 65. Weight (Ibs) 156. BMl: 26, BSA (m?): 1.8. Age 67. General: Alert and oriented to time, place, and person: Yes. Affect normal. Gait antalgic. Distress no apparent. Patient appears anxious: No. Well developed: Yes.. Well nutritioned: Yes. Employer #: BRO72 SUBSCRIBER Emp Ins Code: 03ARV Ins/TPA: TRI-STAR PO Box 2805 Clinton, IA 52733-2805 Phone: ************/ Fax: Ins Code: TRIEV Emp Guarantor#: XBRO6898 Price Code: Z Continue Physical Therapy. TIW (three times week) for 2 weeks / 6 visits Goals: Essential Functions, Functional Improvement, Home Exercise Program. Diagnosis: Sprain of unspecified site of RIGHT knee, subsequent encounter S83.91XD. Weekly froquoncy and prescription duration may vary to allow for complotion of the total prescribod visits.. Statement of Medical Necessity: I deom that the above prescribed treatment is medically necessary.. Physiclan's Note: Reason for continuing Physical therapy: functional improvement. Mark Bailey, NP for Danielle Coultor, MD"

  jsons:
    example1_json: >
      {
              "physician": {
                  "first_name": "Daniel",
                  "last_name": "Anderson",
                  "credentials": "MD",
                  "facility_name": "Orthopaedic Surgery Center",
                  "address": {
                      "street": "2553 Babcock Road",
                      "space": "Ste 500",
                      "city": "San Antonio",
                      "state": "TX",
                      "zip_code": "78228"
                  },
                  "phone_number": "************",
      	    "phone_ext": "",
                  "fax_number": "",
                  "key_contact_name": "",
                  "email_address": "",
      	    "physician_type": "requesting"
              },
              "claimant": {
                  "first_name": "Sophia",
                  "last_name": "Martinez",
                  "middle_name": "L",
                  "suffix": "",
                  "gender": "",
                  "claim_number": "13434788",
                  "date_of_birth": "03/11/1998",
                  "date_of_injury": "12/15/2023",
                  "ssn": "",
                  "address": {
                      "street": "1504 Lockhill Selma Rd",
                      "space": "",
                      "city": "SAN ANTONIO",
                      "state": "TX",
                      "zip_code": "78213-2243"
                  },
                  "phone_number": "(*************",
                  "email_address": "",
      	    "place_of_work": ""
              },
              "adjuster": {
                  "company": "Tristar Risk Management",
                  "contact_name": "Christine Avila",
                  "first_name": "Christine",
                  "last_name": "Avila",
                  "address": {
                      "street": "",
                      "space": "",
                      "city": "",
                      "state": "",
                      "zip_code": ""
                  },
                  "phone_number": "",
                  "fax_number": "",
                  "email_address": ""
              }
            }
    example2_json: >
      {
                    "physician": {
                        "first_name": "Alexander B.",
                        "last_name": "Lewis",
                        "credentials": "MD",
                        "facility_name": "A Professional Corporation",
                        "address": {
                            "street": "1308 W. Causeway Approach",
                            "space": "Suite D",
                            "city": "Mandeville",
                            "state": "LA",
                            "zip_code": "700471"
                        },
                        "phone_number": "************",
                        "fax_number": "************",
                        "key_contact_name": "",
                        "email_address": "<EMAIL>",
      		            "physician_type": "Requesting"
                    },
                    "claimant": {
                        "first_name": "Benjamin",
                        "last_name": "Harrison",
                        "middle_name": "",
                        "suffix": "",
                        "gender": "",
                        "claim_number": "13434788",
                        "date_of_birth": "03/11/1998",
                        "date_of_injury": "12/06/2022",
                        "ssn": "",
                        "address": {
                            "street": "320 Bowman St.",
                            "space": "",
                            "city": "Morgan City",
                            "state": "LA",
                            "zip_code": "70380"
                        },
                        "phone_number": "(*************",
                        "email_address": ""
                    },
                    "adjuster": {
                        "company": "American Contractors",
                        "contact_name": "Jesse Pham",
                        "first_name": "Jesse",
                        "last_name": "Pham",
                        "address": {
                            "street": "PO Box 2805",
                            "space": "",
                            "city": "Clinton",
                            "state": "IA",
                            "zip_code": "52733"
                        },
                        "phone_number": "(*************",
                        "fax_number": "(*************",
                        "email_address": ""
                    }      
                  }
    example3_json: >
      {
                    "physician": {
                        "first_name": "Mark",
                        "last_name": "Balley",
                        "credentials": "NP",
                        "facility_name": "Brownsville",
                        "address": {
                            "street": "2952 Boca Chica Blvd.",
                            "space": "",
                            "city": "Mandeville",
                            "state": "LA",
                            "zip_code": "700471"
                        },
                        "phone_number": "9562438888",
                        "fax_number": "2818669405",
                        "key_contact_name": "",
                        "email_address": "",
      		  "physician_type": "Treating"
                    },
                    "claimant": {
                        "first_name": "Olivia",
                        "last_name": "Grace Thompson",
                        "middle_name": "",
                        "suffix": "",
                        "gender": "",
      		  "claim_number": "",
      		  "date_of_birth": "6/30/1985",
      		  "date_of_injury": "8/11/2024",
                        "ssn": "***********",
                        "address": {
                            "street": "123 W Saint Charles",
                            "space": "",
                            "city": "Brownsville",
                            "state": "TX",
                            "zip_code": "78520"
                        },
                        "phone_number": "9565895194",
                        "email_address": "",
      		  "place_of_work": "Brownsville ISD-Facilities/Maintenance 972/912"
                    },
                    "adjuster": {
                        "company": "Tristar",
                        "contact_name": "",
                        "first_name": "",
                        "last_name": "",
                        "address": {
                            "street": "",
                            "space": "",
                            "city": "",
                            "state": "",
                            "zip_code": ""
                        },
                        "phone_number": "",
                        "fax_number": "",
                        "email_address": ""
                    }      
                  }
ca_keywords:
  - "california"
  - "callfornla"
  - "californla"
  - "calfornia"
  - "statoorcallfomia"
  - "callfornls"
  - "caltornia"
  - "califorma"
  - "callfornia"
  - "oallfomla"
  - "cajifornia"
  - "californa"
  - "califorina"
  - "cailfornia"
  - "califonria"
  - "californa"
  - "calofornia"
  - "caliifronia"
  - "caliiforna"
  - "caflifornia"
  - "caliifornia"
  - "califorinia"
  - "califrnai"
  - "callfornia"
  - "californai"
  - "califorina"
  - "californiya"
  - "callfomria"
  - "californi"
  - "califorina"
  - "califorina"
  - "califorina"
  - "callofornia"
  - "calliforna"
  - "califorina"
  - "califorina"
  - "califorina"
  - "californiya"
  - "caliifronia"
  - "calforna"
  - "cailfornina"
  - "cafornia"
  - "caliifronia"
  - "callifornia"
  - "califonrnia"
  - "califorrnia"
  - "calirfornia"
  - "caliiforniya"
  - "caliifona"
  - "caliifonia"
  - "caliifornnia"