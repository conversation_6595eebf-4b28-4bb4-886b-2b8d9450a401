# Docker manual for metadata_extractor

## Building a Docker for metadata_extractor module

1. Start build by
```docker build -t dx-metadata-extractor .```

## Using a Docker for metadata_extractor module
1. Mount weights for running module by adding `-v` option to docker in docker run command (name is example and is configured in `config.yml`)
```-v ./{local_path}/rfa_expedited_model.pt:/app/rfa_expedited_model.pt```
2. Mount local configuration and secret keys for external services
```-v ./{local_path}/config.yml:/app/config.yml```
3. Mount pgSQL models configuration
```-v ./{local_path}/models/:/models/```
4. Pass ports required for external services using `-p` option or use `--network host` to use host network
```-p 5438:5438 -p 5682:5682 -p ...```

5. Run `docker run {options ... } --gpus all dx-metadata-extractor ` with all required mounts and options

## Model training

Use [YOLO documentation](https://docs.ultralytics.com/modes/train/) (CLI commands) to train models


## Code documentation

### Non-Californian RFA extraction
non_ca_rfa_extractor.py

<summary>Inputs:</summary>

- ocred_text - raw text from RFA document (used to extract metadata from text)
- splitted_document_file_stream - bitwise representation of the file (used to find handwritten documents, checkboxes, etc.)

<summary>Output:</summary>

- qa_results - finished json file with standardized fields (names, dates, streets, etc.)

<summary>Code logic:</summary>

- Checking whether the data has been received into the function in the correct format
- Data processing and json file creation
- Sending requests to the LLM to pull blocks of information
- Filling the final json with data received from LLM
- Adding additional fields and treatments to the final json

![Shorted block diagram](documentation/RFA.drawio.svg "Shorted block diagram")
