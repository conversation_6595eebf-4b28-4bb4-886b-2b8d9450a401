import re

from dateutil import parser


def remove_substrings(value: str | None) -> str | None:
    """
    Remove specific substrings from the string, ignoring case.
    """
    if value is None or value == "":
        return ""

    substrings_to_remove = [
        "information",
        "name",
        "e-mail",
        "administrator",
        "email",
        "what",
        "address",
        "adjuster",
        "number",
        "npi",
        "practice",
        "date",
        "state",
        "claim",
        "fax",
        "specialty",
        "date",
        "city",
        "claimant",
        "adjuster",
        "about",
        "physician",
        "employer",
        "number",
        "present",
        "contact",
        "zipcode",
        "phone",
        "injury",
        "doi",
        "yyyy",
        "employee",
    ]
    patterns = []
    for sub in substrings_to_remove:
        pattern = "".join([f"{char}[^a-zA-Z]*" for char in sub])
        patterns.append(pattern)

    pattern = re.compile("|".join(patterns), re.IGNORECASE)

    return pattern.sub("", value)


def remove_substrings_ignore_numbers(value: str | None) -> str | None:
    """
    Remove specific substrings from the string, ignoring case, without removing numbers.
    """
    if value is None or value == "":
        return ""

    substrings_to_remove = [
        "information",
        "name",
        "e-mail",
        "administrator",
        "email",
        "what",
        "address",
        "adjuster",
        "number",
        "npi",
        "practice",
        "date",
        "state",
        "claim",
        "fax",
        "specialty",
        "date",
        "city",
        "claimant",
        "adjuster",
        "about",
        "physician",
        "employer",
        "number",
        "present",
        "contact",
        "zipcode",
        "phone",
        "injury",
        "doi",
        "yyyy",
        "employee",
    ]

    patterns = [r"\b" + re.escape(sub) + r"\b" for sub in substrings_to_remove]

    pattern = re.compile("|".join(patterns), re.IGNORECASE)

    return pattern.sub("", value)


def clean_spaces(value: str | None) -> str:
    if value is None or value == "":
        return ""

    return re.sub(r"\s+", " ", value)


def truncate_string(value: str | None) -> str:
    """
    Truncate the string to a maximum of 128 characters.
    """
    if value is None or value == "":
        return ""
    return value[:128]


def replace_none(value: str | bool | None) -> None | str:
    if isinstance(value, str):
        if value == "":
            return ""

        count = sum(c.isalnum() for c in value)
        if count < 2:
            return ""
        else:
            return value


def clean_field(value: str | bool | None) -> None | str:
    """
    Recursively remove specific characters from string fields.
    """
    if value is None or value == "":
        return ""
    if isinstance(value, str):
        return re.sub(r"[#%&{}\\:<>*?/$!'+`|=]", "", value)


def clean_numbers(value: str | None) -> str | None:
    """
    Remove all characters except digits, hyphens (-), and spaces from the string.
    """
    if value is None:
        return ""
    return re.sub(r"[^\d\s-]", "", value)


def email_validator(value: str | None) -> str:
    if value is None or value == "":
        return ""
    if "@" in value:
        return value
    else:
        return ""


def clean_date_string(date_str):
    return re.sub(r"(?<=\d{4})\d+", "", date_str)


def convert_date_to_yyyymmdd(date_str):
    if date_str is not None and date_str != "":
        cleaned_date_str = clean_date_string(date_str)
        try:
            dt = parser.parse(cleaned_date_str, fuzzy=True)
            return dt.strftime("%Y%m%d")
        except ValueError:
            return ""
    else:
        return ""


def check_digits_more_than_letters(value):
    if value is None or value == "":
        return ""
    digits_count = sum(c.isdigit() for c in value)
    letters_count = sum(c.isalpha() for c in value)

    if digits_count > letters_count:
        return value
    else:
        return ""


def remove_extra_characters_from_phone_number(value):
    if value is None or value == "":
        return ""
    return re.sub(r"\D", "", value)


def format_json(json_data):
    json_data["receiptDate"] = convert_date_to_yyyymmdd(json_data["receiptDate"])
    json_data["documentDate"] = convert_date_to_yyyymmdd(json_data["documentDate"])
    json_data["senderName"] = replace_none(
        truncate_string(clean_spaces(remove_substrings(json_data["senderName"])))
    )

    json_data["physician"]["physicianName"] = replace_none(
        truncate_string(
            clean_spaces(remove_substrings(json_data["physician"]["physicianName"]))
        )
    )

    json_data["physician"]["facilityName"] = replace_none(
        truncate_string(
            clean_spaces(remove_substrings(json_data["physician"]["facilityName"]))
        )
    )

    json_data["physician"]["keyContactName"] = replace_none(
        truncate_string(
            clean_spaces(remove_substrings(json_data["physician"]["keyContactName"]))
        )
    )

    json_data["physician"]["address"] = replace_none(
        truncate_string(
            clean_spaces(
                clean_field(
                    remove_substrings_ignore_numbers(json_data["physician"]["address"])
                )
            )
        )
    )

    json_data["physician"]["city"] = replace_none(
        truncate_string(clean_spaces(remove_substrings(json_data["physician"]["city"])))
    )

    json_data["physician"]["state"] = replace_none(
        truncate_string(
            clean_spaces(remove_substrings(json_data["physician"]["state"]))
        )
    )

    json_data["physician"]["zip"] = replace_none(
        truncate_string(
            clean_spaces(clean_field(clean_numbers(json_data["physician"]["zip"])))
        )
    )

    json_data["physician"]["phoneNumber"] = remove_extra_characters_from_phone_number(
        replace_none(
            truncate_string(
                clean_spaces(
                    clean_field(clean_numbers(json_data["physician"]["phoneNumber"]))
                )
            )
        )
    )

    json_data["physician"]["faxNumber"] = remove_extra_characters_from_phone_number(
        replace_none(
            truncate_string(
                clean_spaces(
                    clean_numbers(clean_field(json_data["physician"]["faxNumber"]))
                )
            )
        )
    )

    json_data["physician"]["specialty"] = replace_none(
        truncate_string(
            clean_spaces(remove_substrings(json_data["physician"]["specialty"]))
        )
    )

    json_data["physician"]["npi"] = replace_none(
        truncate_string(
            clean_spaces(clean_numbers(clean_field(json_data["physician"]["npi"])))
        )
    )

    json_data["physician"]["emailAddress"] = email_validator(
        replace_none(
            truncate_string(
                clean_field(
                    clean_spaces(
                        remove_substrings_ignore_numbers(
                            json_data["physician"]["emailAddress"]
                        )
                    )
                )
            )
        )
    )

    json_data["adjuster"]["company"] = replace_none(
        truncate_string(
            clean_spaces(remove_substrings(json_data["adjuster"]["company"]))
        )
    )

    json_data["adjuster"]["adjusterName"] = replace_none(
        truncate_string(
            clean_spaces(remove_substrings(json_data["adjuster"]["adjusterName"]))
        )
    )

    json_data["adjuster"]["city"] = replace_none(
        truncate_string(clean_spaces(remove_substrings(json_data["adjuster"]["city"])))
    )

    json_data["adjuster"]["address"] = replace_none(
        truncate_string(
            clean_spaces(
                remove_substrings_ignore_numbers(json_data["adjuster"]["address"])
            )
        )
    )

    json_data["adjuster"]["state"] = replace_none(
        truncate_string(clean_spaces(remove_substrings(json_data["adjuster"]["state"])))
    )

    json_data["adjuster"]["zip"] = replace_none(
        truncate_string(
            clean_spaces(clean_numbers(clean_field(json_data["adjuster"]["zip"])))
        )
    )

    json_data["adjuster"]["phoneNumber"] = remove_extra_characters_from_phone_number(
        replace_none(
            truncate_string(
                clean_spaces(
                    clean_numbers(clean_field(json_data["adjuster"]["phoneNumber"]))
                )
            )
        )
    )

    json_data["adjuster"]["faxNumber"] = remove_extra_characters_from_phone_number(
        replace_none(
            truncate_string(
                clean_spaces(
                    clean_numbers(clean_field(json_data["adjuster"]["faxNumber"]))
                )
            )
        )
    )

    json_data["adjuster"]["emailAddress"] = email_validator(
        replace_none(
            truncate_string(
                clean_spaces(
                    remove_substrings_ignore_numbers(
                        json_data["adjuster"]["emailAddress"]
                    )
                )
            )
        )
    )

    json_data["claimant"]["claimantName"] = replace_none(
        truncate_string(
            clean_spaces(remove_substrings(json_data["claimant"]["claimantName"]))
        )
    )
    json_data["claimant"]["dateOfBirth"] = convert_date_to_yyyymmdd(
        json_data["claimant"]["dateOfBirth"]
    )
    json_data["employer"]["name"] = replace_none(
        truncate_string(clean_spaces(remove_substrings(json_data["employer"]["name"])))
    )

    json_data["claim"]["claimNumber"] = check_digits_more_than_letters(
        replace_none(
            truncate_string(
                clean_spaces(
                    remove_substrings_ignore_numbers(json_data["claim"]["claimNumber"])
                )
            )
        )
    )
    json_data["claim"]["dateOfInjuryFrom"] = convert_date_to_yyyymmdd(
        json_data["claim"].get("dateOfInjuryFrom", "")
    )
    json_data["claim"]["dateOfInjuryThrough"] = convert_date_to_yyyymmdd(
        json_data["claim"].get("dateOfInjuryThrough", "")
    )

    if (
        "attorney" in json_data
        and "phoneNumber" in json_data["attorney"]
        and json_data["attorney"]["phoneNumber"] is not None
    ):
        json_data["attorney"]["phoneNumber"] = (
            remove_extra_characters_from_phone_number(
                replace_none(
                    truncate_string(
                        clean_spaces(
                            clean_numbers(
                                clean_field(json_data["attorney"]["phoneNumber"])
                            )
                        )
                    )
                )
            )
        )

    if (
        "employer" in json_data
        and "phoneNumber" in json_data["employer"]
        and json_data["employer"]["phoneNumber"] is not None
    ):
        json_data["employer"]["phoneNumber"] = (
            remove_extra_characters_from_phone_number(
                replace_none(
                    truncate_string(
                        clean_spaces(
                            clean_numbers(
                                clean_field(json_data["employer"]["phoneNumber"])
                            )
                        )
                    )
                )
            )
        )

    if (
        "claimant" in json_data
        and "phoneNumber" in json_data["claimant"]
        and json_data["claimant"]["phoneNumber"] is not None
    ):
        json_data["claimant"]["phoneNumber"] = (
            remove_extra_characters_from_phone_number(
                replace_none(
                    truncate_string(
                        clean_spaces(
                            clean_numbers(
                                clean_field(json_data["claimant"]["phoneNumber"])
                            )
                        )
                    )
                )
            )
        )
    return json_data
