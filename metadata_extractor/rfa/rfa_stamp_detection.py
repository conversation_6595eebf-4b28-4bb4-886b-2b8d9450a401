import cv2
import fitz  # PyMuPDF
import numpy as np
from ultralytics import YOLO
import os
# Initialize the YOLO model for stamp detection

STAMP_IS_RUSH_MODEL_PATH = os.getenv(
    "STAMP_IS_RUSH_MODEL_PATH", "/models/stamp_is_rush_model.pt"
)

model = YOLO(STAMP_IS_RUSH_MODEL_PATH)


def process_pdf_page(page):
    """
    Converts a PDF page into an image and returns it as a numpy array.
    """
    try:
        pix = page.get_pixmap()
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
            pix.height, pix.width, 3
        )
        return cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
    except Exception as e:
        print(f"Error processing page {page.number}: {e}")
        return None


def detect_stamps_and_urgency(image):
    """
    Detects stamps and urgency in the given image using YOLO model.
    """
    img_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)  # Convert BGR to RGB
    results = model(img_rgb)  # Perform inference

    # Assuming results are stored in results[0].boxes if using the ultralytics YOLOv5/v8
    boxes = results[0].boxes

    # Filter detections based on class labels
    has_stamp = any(
        box.cls == 0 for box in boxes
    )  # Assuming index 0 is the class label for stamps
    is_urgent = any(
        box.cls == 1 for box in boxes
    )  # Assuming index 1 is the class label for urgency

    return has_stamp, is_urgent


def run_pdf_processing(file_stream):
    """
    Process each page of the PDF for stamp detection and OCR.
    """
    try:
        doc = fitz.open(stream=file_stream, filetype="pdf")
        for page in doc:
            image = process_pdf_page(page)
            if image is not None:
                has_stamp, is_urgent = detect_stamps_and_urgency(image)
                if has_stamp or is_urgent:
                    return True
        return False
    finally:
        doc.close()


def run_rfa_stamp_detection(file_stream):
    return run_pdf_processing(file_stream)
