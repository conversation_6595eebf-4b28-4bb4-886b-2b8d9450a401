import base64
import os
import re
from datetime import datetime
from typing import Any, Iterable

import nltk
import spacy
import torch
from nltk import word_tokenize
from ollama import Client
from pydantic import BaseModel, Field
from rfa.rfa_checkbox import run_tick_detection
from rfa.rfa_formatter import (
    clean_spaces,
    format_json,
    remove_substrings,
    replace_none,
    truncate_string,
)
from rfa.rfa_stamp_detection import run_rfa_stamp_detection
from rfa.rfa_table_extraction import run_rfa_table_extraction
from rfa.rfa_utils_and_constants import (
    categorized_questions,
    initialize_qa_results,
    physician_specialty_list,
    transform_qa_results,
)
from transformers import AutoModelForQuestionAnswering, AutoTokenizer

nltk.download("punkt_tab")
nlp = spacy.load("en_core_web_sm")

device: torch.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

tokenizer: AutoTokenizer = AutoTokenizer.from_pretrained(
    "bert-large-uncased-whole-word-masking-finetuned-squad"
)
model: AutoModelForQuestionAnswering = AutoModelForQuestionAnswering.from_pretrained(
    "bert-large-uncased-whole-word-masking-finetuned-squad"
).to(device)

# BASE_AUTH_USER_AND_PASSWORD = os.environ["BASE_AUTH_USER_AND_PASSWORD"]
SERVER_HOST = os.environ["SERVER_HOST"]
SERVER_PORT = os.environ["SERVER_PORT"]

# BASE64_STRING = base64.b64encode(BASE_AUTH_USER_AND_PASSWORD.encode()).decode("ascii")
# AUTH_HEADER = {"Authorization": f"Basic {BASE64_STRING}"}

OLLAMA_CLIENT = Client(
    host=f"{SERVER_HOST}:{SERVER_PORT}",
    # headers=AUTH_HEADER,
    timeout=360.0,
    verify=False,
)

OLLAMA_OPTIONS = {
    "num_predict": 960,
    "temperature": 0,
}

MODEL_NAME = "mistral-nemo:12b-instruct-2407-q5_K_M_medical_assistant"


class AddressLine(BaseModel):
    """Model for address information."""

    street: str = Field(
        required=False, description="Street address mentioned in the address section"
    )
    space: str = Field(
        required=False, description="Suite number mentioned in the address section"
    )
    city: str = Field(
        required=False, description="City mentioned in the address section"
    )
    state: str = Field(
        required=False, description="State mentioned in the address section"
    )
    zip_code: str = Field(
        required=False, description="Zip code mentioned in the address section"
    )


class Claimant(BaseModel):
    """Model for claimant information."""

    full_name: str = Field(required=False, description="Full name of the claimant")
    first_name: str = Field(required=False, description="First name of the claimant")
    last_name: str = Field(required=False, description="Last name of the claimant")
    middle_name: str = Field(
        required=False, description="Middle name of the claimant (if available)"
    )
    date_of_birth: str = Field(
        required=False, description="Date of birth of the claimant"
    )
    employer_name: str = Field(required=False)


class Adjuster(BaseModel):
    """Model for adjuster information."""

    company: str = Field(
        required=False, description="Company name mentioned in the adjuster section"
    )
    adjuster_name: str = Field(
        required=False, description="Adjuster name or the key contact name"
    )
    first_name: str = Field(
        required=False, description="First name of the adjuster or the key contact name"
    )
    last_name: str = Field(
        required=False, description="Last name of the adjuster or the key contact name"
    )
    middle_name: str = Field(
        required=False, description="Middle name of the adjuster (if available)"
    )
    suffix: str = Field(
        required=False, description="Suffix of the adjuster name (if available)"
    )
    address: AddressLine = Field(
        required=False, description="The address mentioned in the adjuster section"
    )
    phone_number: str = Field(
        required=False, description="Phone number of the adjuster"
    )
    phone_extension: str = Field(
        required=False,
        description="Phone extension of the adjuster's phone number (if available)",
    )
    fax_number: str = Field(required=False, description="Fax number of the adjuster")
    email_address: str = Field(
        required=False, description="Email address of the adjuster"
    )


class Physician(BaseModel):
    """Model for physician information."""

    first_name: str = Field(required=False, description="First name of the physician")
    last_name: str = Field(required=False, description="Last name of the physician")
    credentials: str = Field(
        required=False, description="Credentials of the physician (MD, DO, etc.)"
    )
    npi: str = Field(required=False, description="NPI number of the physician")
    specialty: str = Field(
        required=False,
        description="Specialty of the physician mentioned in the Physician section",
    )
    facility_name: str = Field(
        required=False,
        description="Practice name or facility name where the physician works mentioned in the Physician section",
    )
    address: AddressLine = Field(
        required=False, description="The address mentioned in the physician section"
    )
    email_address: str = Field(
        required=False, description="Email address of the physician"
    )
    phone_number: str = Field(
        required=False, description="Phone number of the physician"
    )
    fax_number: str = Field(required=False, description="Fax number of the physician")
    key_contact_name: str = Field(
        required=False,
        description="Key contact name mentioned in the physician section",
    )


class ClaimMetadata(BaseModel):
    """Model for claim metadata."""

    claim_number: str = Field(
        required=False, description="Claim number mentioned in the claim section"
    )
    date_of_injury_from: str = Field(
        required=False,
        description="If the date of injury text mentions a date range before the CT keyword, this field represents the start date of the injury. If the claim section mentions a single date, this field represents that date. Make sure that this date is earlier than the `date_of_injury_to` field. Otherwise, this field is empty.",
    )
    date_of_injury_to: str = Field(
        required=False,
        description="If the date of injury text mentions a date range before the CT keyword, this field represents the end date of the injury. Otherwise, this field is empty. Make sure that this date is later than the `date_of_injury_from` field.",
    )


class Metadata(BaseModel):
    claimant: Claimant
    physician: Physician
    adjuster: Adjuster
    claim: ClaimMetadata


PROMPT_TEMPLATE = """
You are a text procesing agent specialized in processing medical documents. You have been tasked with extracting the following information from a medical document transcript:
- Claimant Information
- Physician Information
- Adjuster Information

Follow the instructions below to extract the information:
1. Extract the information as accurately as possible. If there is any information that is not available, please leave it blank.
2. Make sure to respond with a valid JSON object.
3. If any of those sections are not present in the document, leave them blank in the response.
4. Make sure to not include "Unknown" or "N/A" in the response for each of the missing fields.
5. If there is a typo in the original text, don't mention it in the response. Just extract the information as is.
6. Do not include irrelevant information for each of the sections. For example, if there is a phone number mentioned in the requesting physician section, do not include it in the claimant or adjuster fields for phone number.
7. If the information is not present in the required section (e.g. claimant information, or physician information, or adjuster information), do not include it in the response.
8. If the address is absent for any of the sections, leave the address related fields (such as street, city, state, space, zip code) blank and do not populate them with values such as "Unknown", "N/A", or "Not Available". Instead, just leave them as "".
For example, if the address is not present in the claimant section, the address field should be left as "" and not "Unknown". The same applies to other address fields, such as street, city, state, space, zip code.
9. The `jurisState` field should be extracted from the document in the format of two letter abbreviation (e.g. CA, NY, TX, etc.). If the jurisdiction state is not present in the document, leave it blank in the response. Never include the full name of the state in the `jurisState` field.
10. If there are multiple physician names present in the document, extract the physician name which is followed by the MD credentials. If there are multiple physician names with MD credentials, extract the first one.
11. If the address street contains a suite number, extract the suite number into the space field. For example, if the address is "123 Main St Suite 100", the street field should be "123 Main St" and the space field should be "Suite 100".
12. Do not include email addresses in the output if it does not contain the "@" symbol. If the email address does not contain the "@" symbol, leave the email address field blank.
13. Do not include phone extension in `phone_number` field. If the phone number contains an extension, extract the phone number only and populate the `phone_extension` field with the extension number instead.
14. Never use "John Doe" or "Jane Doe" as a placeholder for any of the names. Extract the names as they are mentioned in the document. You are not allowed to use any placeholder names.
15. In every `Key Contact` field, only extract the name of the key contact (first name and last name). Do not include any other information such as the title or the department of the key contact.
16. When extracting the `date_of_injury_from` and `date_of_injury_to` fields, remember that they can be located both after "Date of Injury (DD/MM/YYYY)" and "CT" keywords. The CT keyword stands for Cumulative Trauma and means that the injury is not a single event but a result of repetitive stress or strain, therefore it features a range of dates. You have to make sure that the extracted `date_of_injury_from` is earlier than the `date_of_injury_to` date.
The `date_of_injury_from` and `date_of_injury_to` fields should be extracted from the Claimant Information section only.

You will be provided with text segments relevant to each of the sections. Extract the information as accurately as possible. If there is any information that is not available, please leave it blank.
Make sure to extract the information for each JSON section only from the relevant text segment provided. Do not combine information from different text segments.
For example, the `physician` section should only be extracted from the `physician_information_text` segment and not from the `claimant_information_text` or `adjuster_information_text` segments.
In the same way, `claimant` information should only be extracted from the `claimant_information_text` segment, `adjuster` information should only be extracted from the `adjuster_information_text` segment, and `claim_metadata` information should only be extracted from the `claim_information` segment.
If you see that there is no information regarding this particular field in the text (for instance, if there is no information regarding the physician's specialty after the physician's name, and instead there is the next keyword, such as "Facility Name"; or there is no info about Claim Number field because it is directly followed by the Employer keyword), please leave this field blank in the response.

Physician Information:
{physician_information_text}

Claimant Information:
{claimant_information_text}

Adjuster Information:
{adjuster_information_text}

Claim Information:
{claim_information}

Extraction results:
"""


def process_rfa_llm(
    physician_information_text: str,
    claimant_information_text: str,
    adjuster_information_text: str,
    claim_information: str,
) -> str:
    input_prompt = PROMPT_TEMPLATE.format(
        physician_information_text=physician_information_text,
        claimant_information_text=claimant_information_text,
        adjuster_information_text=adjuster_information_text,
        claim_information=claim_information,
    )
    response = OLLAMA_CLIENT.generate(
        model=MODEL_NAME,
        prompt=input_prompt,
        options=OLLAMA_OPTIONS,
        format=Metadata.model_json_schema(),
    )
    response = Metadata.model_validate_json(response["response"].replace("\n", " "))
    return response


def reformat_date(text_with_date):
    if not text_with_date:
        return ""
    # Adjusted date patterns to include comprehensive coverage of date formats
    date_patterns_adjusted = [
        r"\b(0?[1-9]|1[012])[-/](?:0?[1-9]|[12][0-9]|3[01])[-/](\d{2,4})\b",  # MM/DD/YYYY or MM-DD-YYYY
        r"\b(\d{4})[-/](0?[1-9]|1[012])[-/](?:0?[1-9]|[12][0-9]|3[01])\b",  # YYYY/MM/DD or YYYY-MM-DD
        r"\b\d{4}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(?:0?[1-9]|[12][0-9]|3[01])\b",
        # YYYY Month DD
        r"\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(?:0?[1-9]|[12][0-9]|3[01])\s+\d{4}\b",
        # Month DD YYYY
    ]

    for pattern in date_patterns_adjusted:
        match = re.search(pattern, text_with_date)
        if match:
            date_str = match.group(0).replace("/", "-")

            if len(date_str.split("-")[-1]) == 2:
                if date_str.split("-")[-1] < "26":
                    date_str = date_str[:6] + "20" + date_str[6:]
                else:
                    date_str = date_str[:6] + "19" + date_str[6:]

            date_formats = [
                "%m-%d-%Y",
                "%Y-%m-%d",
                "%Y %b %d",
                "%Y %B %d",
                # Formats for matching "Month DD YYYY" and "Month D YYYY"
                "%b %d %Y",
                "%B %d %Y",
            ]

            for format in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, format)
                    return parsed_date.strftime("%Y%m%d")
                except ValueError:
                    continue
            return ""
    return ""


def transform_phone_number(phone_number: str) -> str:
    """
    Transforms a phone number by removing non-digit characters and extracting the main number.

    Args:
        phone_number (str): The phone number to transform.

    Returns:
        str: The transformed phone number containing only digits.
    """
    if not phone_number:
        return ""

    phone_number_parts = re.split(r"\s*(ext|x)\s*", phone_number, flags=re.IGNORECASE)
    main_phone_number = re.sub(r"\D", "", phone_number_parts[0])
    return main_phone_number


def transform_phone_extension(phone_extension: str) -> str:
    """
    Transforms a phone extension by removing non-digit characters.

    Args:
        phone_extension (str): The phone extension to transform.

    Returns:
        str: The transformed phone extension containing only digits.
    """
    if not phone_extension:
        return ""
    return re.sub(r"\D", "", phone_extension)


def transform_credentials(credentials: str) -> str:
    """
    Transforms the credentials of a physician by removing non-alphabetic characters.

    Args:
        credentials (str): The credentials of the physician to transform.

    Returns:
        str: The transformed credentials containing only alphabetic characters.
    """
    if not credentials:
        return ""
    return re.sub(r"[^a-zA-Z]", "", credentials)


def extract_values_between_markers(
    data: list, start_markers: list, end_markers: list
) -> list:
    results = []
    between_flags = False
    current_values = []
    for item in data:
        if not between_flags and any(
            start_marker.lower() in item.lower() for start_marker in start_markers
        ):
            current_values.append(item)
            between_flags = True
        elif between_flags and any(
            end_marker.lower() in item.lower() for end_marker in end_markers
        ):
            current_values.append(item)
            results.append(current_values)
            break
        elif between_flags:
            current_values.append(item)
    if between_flags and not results:
        results.append(current_values)
    return results


def extract_marker_surroundings(
    data: list[str], markers: list[str], grip_size=50, start_grip=True
) -> list[str]:
    if start_grip:
        grip_size_b = grip_size
    else:
        grip_size_b = 0
    results = []
    combined_data = " ".join(data)

    first_occurrence_index = None
    first_marker = ""
    for marker in markers:
        index = combined_data.find(marker)
        if index != -1:
            if first_occurrence_index is None or index < first_occurrence_index:
                first_occurrence_index = index
                first_marker = marker

    if first_occurrence_index is not None:
        marker_length = len(first_marker)
        start = max(0, first_occurrence_index - grip_size_b)
        end = min(
            len(combined_data), first_occurrence_index + marker_length + grip_size
        )
        results.append(combined_data[start:end])

    return results


def find_datetime_strings(data: Any) -> str | None:
    if not isinstance(data, (str, bytes)):
        return ""
    datetime_pattern = (
        r"(\b\d{4}-\d{2}-\d{2} \d{2}:\d{2}(?::\d{2})? (?:PDT|PST)?\b)|"
        r"(\b\d{2}/\d{2}/\d{4} \d{1,2}:\d{2}(?: (?:AM|PM))?\b)|"
        r"(\b\d{4}-\d{2}-\d{2} \d{2}:\d{2}\b)|"
        r"(\b\d{2}/\d{2}/\d{4}\b)|"
        r"(\b\d{2}/\d{2}/\d{4})\b|"
        r"(\bDate: \d{2}/\d{2}/\d{4}\b)|"
        r"(\bJanuary|February|March|April|May|June|July|August|September|October|November|December) \d{1,2}, \d{4} at \d{1,2}:\d{2}:\d{2} (?:AM|PM)\b|"
        r"(\b\d{1,2}/\d{1,2}/\d{4} \d{1,2}:\d{2}:\d{2} (?:AM|PM)\b)|"
        r"(\b\d{1,2}/\d{1,2}/\d{4} \d{2}:\d{2}\b)|"
        r"(\bDate: \d{2}/\d{2}/\d{4}\b)|"
        r"(\bDate: \d{1,2}/\d{1,2}/\d{4}\b)|"
        r"(\b\d{1,2} (January|February|March|April|May|June|July|August|September|October|November|December) \d{4} \d{1,2}:\d{2}:\d{2} (AM|PM)\b)|"
        r"(\bJanuary|February|March|April|May|June|July|August|September|October|November|December)\.\d{1,2}\.\d{4}\b|"
        r"(\b\d{2}-\d{1,2}-\d{2}\b)|"
        r"(\b\d{1,2}/\d{1,2}/\d{4}\b)|"
        r"(\b(?:Date)?\d{1,2}/\d{1,2}/\d{4}\b)|"
        r"(\bDate:\d{2}/\d{2}/\d{4}\b)|"
        r"(\b\d{2}/\d{2}/\d{2}\b)|"
        r"(\b\d{2} (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{4}\b)|"
        r"(\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\.\d{2}\.\d{4}\b)|"
        r"(\b\d{1,2}-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{4}\b)|"
        r"(\b\d{1,2}(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\d{4}\b)|"
        r"(\b\d{1,2} Ju1 \d{4}\b)|"
        r"(\b\d{2}-\d{2}-\d{4}\b)|"
        r"(\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\. \d{1,2}\. \d{4}\b)|"
        r"(\b(January|February|March|April|May|June|July|August|September|October|November|December) \d{1,2}, \d{4}\b)|"
        r"(\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)/\d{1,2}/\d{4}\b)|"
        r"(\b\d{2}/\d{2}/\d{4})\d+|"
        r"(\b[A-Z]{3}/\d{2}/\d{4})\b|"
        r"(\b[A-Z]{3}/\d{2}/\d{4})/[A-Z]{3}\b\|"
        r"(\b\d{4}-\d{2}-\d{2})\d+|"
        r"(\b[A-Z]{3}\.\d{1,2}\.\d{4})[^\\n]*|"
        r"\b0?(\d{2}-\d{2}-\d{2})\b|"
        r"(\b\d{2})/(\d{2})(\d{4})\b|"
        r"(\d{2}/\d{2})(\d{4})|"
        r"(\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\.\d{1,2}\.\d{4})\b|"
        r"(\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{1,2}, \d{4}\b)|"
        r"(\b[A-Z]{3}\.\s?\d{1,2}\.\d{4})\b|"
        r"(\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s\d{1,2},\s\d{4})\b"
    )
    match = re.search(datetime_pattern, data)
    if match:
        return match.group()
    return ""


def flatten_list(text):
    flat_context = [
        " ".join(item) if isinstance(item, list) else item
        for sublist in text
        for item in sublist
    ]
    return " ".join(flat_context)


def tokenize_text(text_lines: list[str]) -> str:
    all_tokens = []
    for line in text_lines:
        tokens = word_tokenize(line)
        all_tokens.extend(tokens)
    return " ".join(all_tokens)


def find_first_person_name_spacy(context_parts):
    for text in context_parts:
        doc = nlp(text)
        for ent in doc.ents:
            if ent.label_ == "PERSON":
                if "REMOTE" not in ent.text:
                    return ent.text
    return None


def find_names_joined(flattened_list):
    name_pattern = re.compile(r"Name: (.+)")
    names = []
    for item in flattened_list:
        if "Practice" in item or "Contact" in item:
            continue
        match = name_pattern.search(item)
        if match:
            names.append(match.group(1))
    return ", ".join(names)


def answer_question(question: str, context_parts: list[str]) -> str | None:
    model.eval()
    max_score = float("-inf")
    best_answer = None
    for context in context_parts:
        inputs = tokenizer.encode_plus(
            question,
            context,
            add_special_tokens=True,
            return_tensors="pt",
            truncation=True,
            max_length=512,
        )
        input_ids = inputs["input_ids"].to(device)
        attention_mask = inputs["attention_mask"].to(device)

        with torch.no_grad():
            outputs = model(input_ids, attention_mask=attention_mask)
            answer_start_scores, answer_end_scores = (
                outputs["start_logits"],
                outputs["end_logits"],
            )

        answer_start = torch.argmax(answer_start_scores)
        answer_end = torch.argmax(answer_end_scores) + 1

        if answer_end > answer_start:
            answer_tokens = input_ids[0, answer_start:answer_end].tolist()
            answer = tokenizer.decode(answer_tokens, skip_special_tokens=True)
            if answer and not re.match(r"^\s*$", answer):
                total_score = (
                    answer_start_scores[0, answer_start].item()
                    + answer_end_scores[0, answer_end - 1].item()
                )
                if total_score > max_score:
                    max_score = total_score
                    best_answer = answer
    return refine_answer(best_answer)


def find_physician_name(flattened_list):
    name_pattern = re.compile(r"\bName:?\s?(.+)")
    names = []
    add_next_item = False

    for i, item in enumerate(flattened_list):
        if not isinstance(item, str):
            # Skip non-string items
            continue

        if add_next_item:
            if isinstance(item, str):
                names.append(item)
            add_next_item = False
            continue

        if "Practice" in item or "Contact" in item:
            continue

        if re.search(r"[a-zA-Z]Name", item):
            continue

        if re.fullmatch(r"Name[^a-zA-Z]*", item) and (i + 1) < len(flattened_list):
            add_next_item = True
            continue

        match = name_pattern.search(item)
        if match:
            names.append(match.group(1))

    return ", ".join(names)


def contains_letters(s):
    return any(c.isalpha() for c in s)


def find_patient_name(flattened_list):
    middle_name_pattern = re.compile(r"\s*Middle\s?(.+)")
    name_pattern = re.compile(r"\bName:?\s?(.+)")
    names = []
    add_next_item = False
    for i, item in enumerate(flattened_list):
        if add_next_item:
            names.append(item)
            add_next_item = False
            continue
        if re.search(r".+Middle[^a-zA-Z]*$", item):
            add_next_item = True
            continue
        if "Employer" in item or re.search(r"[a-zA-Z]Name", item):
            continue
        match = middle_name_pattern.search(item)
        if match:
            names.append(match.group(1))
        else:
            match = name_pattern.search(item)
            if match:
                names.append(match.group(1))
    result = ", ".join(names)
    result = re.sub(r"[#%&{}()\\:<>*?/$!'+`|=]", "", result)
    return result


def glue_words_together(answer):
    if answer is None or answer == "":
        return None
    return answer.replace(" ", "")


def refine_answer(answer: str | None) -> str | None:
    return answer.strip().capitalize() if answer else None


def clustering_of_page_text(all_pages_text):
    (
        top_page_info,
        patient_info,
        physician_info,
        adjuster_info,
        without_top_info,
        request_info,
        doc_date_info,
        claim_info,
    ) = ([], [], [], [], [], [], [], [])
    for i, pages_list in enumerate(all_pages_text):
        if i == 0:
            for txt in pages_list:
                if (
                    "State of California" in txt
                    or "REQUEST FOR AUTHORIZATION" in txt
                    or "DWC Form" in txt
                ):
                    break
                top_page_info.append(txt)
            found = False

            for txt in pages_list:
                if (
                    "State of California" in txt
                    or "REQUEST FOR AUTHORIZATION" in txt
                    or "DWC Form" in txt
                    or "New Request" in txt
                    or "box if request" in txt
                    or "Attach th" in txt
                ):
                    found = True
                if found:
                    without_top_info.append(txt)
            request_info.append(
                extract_values_between_markers(
                    without_top_info,
                    ["New Request"],
                    ["oral request.", "oral request", "Employee Information"],
                )
            )
            patient_info.append(
                extract_values_between_markers(
                    without_top_info,
                    [
                        "Employee Information",
                        "Employee Information:",
                        "oral request.",
                        "Name (Last, First, Middle)",
                        "a written conf",
                        "Last",
                    ],
                    [
                        "Employer",
                        "Employer:",
                        "sting Phys",
                        "Requesting Physician",
                        "Providor Information",
                        "Provider Information",
                        "Requesting",
                        "ng Physic",
                        "Physician Info",
                    ],
                )
            )
            patient_info_str = str(patient_info)

            if "Name:" in patient_info_str:
                physician_info.append(
                    extract_values_between_markers(
                        without_top_info,
                        [
                            "Requesting Physician",
                            "Provider Information",
                            "sting Phys",
                            "Providor Information",
                            "ian Infor",
                            "Practice Name",
                        ],
                        [
                            "E-mail Address:",
                            "mail Address",
                            "Claims Administrator Information",
                            "Claims",
                            "Claim",
                            "Company",
                            "Company Name",
                            "l Addr",
                        ],
                    )
                )
            else:
                physician_info.append(
                    extract_values_between_markers(
                        without_top_info,
                        [
                            "Requesting Physician",
                            "Provider Information",
                            "sting Phys",
                            "Providor Information",
                            "Name:",
                            "ian Infor",
                            "Practice Name",
                        ],
                        [
                            "E-mail Address:",
                            "mail Address",
                            "l Addr",
                            "Claims Administrator Information",
                            "ator info",
                            "Claims",
                            "Claim",
                            "Company",
                            "Company Name",
                            "Company Name:",
                        ],
                    )
                )
            adjuster_info.append(
                extract_values_between_markers(
                    without_top_info,
                    [
                        "Claims Administrator Information",
                        "Claim Administrator",
                        "Claims",
                        "aims Admin",
                        "trator Info",
                        "tor Inform",
                        "aim Admin",
                        "Company Name:",
                        "Company Name",
                        "any Name:",
                        "Policy Ho",
                        "Company.Na",
                        "CompanyNam",
                    ],
                    [
                        "mail Address",
                        "Requested",
                        "mail",
                        "Requested treatment",
                        "requ",
                        "treatm",
                        "Provider Information",
                    ],
                )
            )
            if adjuster_info == [[]] or adjuster_info == []:
                adjuster_info = []
                adjuster_info.append(
                    extract_values_between_markers(
                        without_top_info,
                        ["mail Add", "mail", "l Addr"],
                        [
                            "mail Address",
                            "Requested",
                            "mail",
                            "Requested treatment",
                            "requ",
                            "treatm",
                            "Provider Information",
                        ],
                    )
                )
            claim_info = extract_marker_surroundings(
                pages_list,
                [
                    "Case Description",
                    "Claim Number",
                    "Claim#",
                    "Claim #",
                    "Clain Number",
                    "Claim Num",
                    "Clalm Number",
                    "ClaimNumber",
                    "Clalm number",
                ],
                20,
            )
        doc_date_info.append(
            extract_marker_surroundings(
                pages_list,
                [
                    "Requesting Physician Signature",
                    "ting Physician Sig",
                    "cian Sign",
                    "cian Sig",
                    "an Signat",
                    "cian's Signat",
                ],
            )
        )
        doc_date = flatten_list(doc_date_info)
        if flatten_list(doc_date):
            doc_date = find_datetime_strings(doc_date)

    return (
        top_page_info,
        patient_info,
        physician_info,
        doc_date,
        adjuster_info,
        request_info,
        claim_info,
        without_top_info,
    )


def flatten_to_string_list(nested_list):
    flattened_list = []
    for item in nested_list:
        if isinstance(item, list):
            flattened_list.extend(flatten_to_string_list(item))
        else:
            flattened_list.append(str(item))
    return flattened_list


def get_sender_name(sender_info):
    if not sender_info:
        return ""

    for txt in sender_info:
        lower_txt = txt.lower()
        if "from" in lower_txt and "rfa" not in lower_txt:
            result = txt.lower().replace("from", "", 1).strip()
            result = txt[len(txt) - len(result) :].strip()
            return result
        elif "letter by" in lower_txt:
            result = txt.lower().replace("letter by", "", 1).strip()
            result = txt[len(txt) - len(result) :].strip()
            return result
    return ""


def find_first_claim_number(text):
    # Regex pattern to match claim numbers
    pattern = r"\b((\d{3}-\d{2}-\d{4}[A-Za-z]?)|([A-Za-z]{1,3}\d{9,12})|(\d{5}-\d{5}-[A-Za-z]{3})|(\d{10})|([A-Za-z]{2}\d{9}))\b"
    match = re.search(pattern, text)
    if match:
        return match.group(0)
    else:
        return ""


def find_last_date(value):
    if value is not None:
        pattern = r"\b\d{1,2}/\d{1,2}/\d{4}\b"
        matches = re.findall(pattern, value)
        return matches[-1] if matches else None


def attorney_metadata(txt):
    substring = "App attorney"
    if "Defense attorney" in txt[0][-1]:
        index = next((i for i, element in enumerate(txt) if substring in element), -1)
        index = index - 1
        return txt[0][index]
    else:
        index = next((i for i, element in enumerate(txt) if substring in element), -1)
        index = index + 1
        return txt[0][index]


def is_attorney(all_pages_text):
    for i, pages_list in enumerate(all_pages_text):
        if i == 0:
            if "Request for Authorization Form" in pages_list[0]:
                return True
            else:
                return False


def is_older_than_18(date_str):
    try:
        birth_date = datetime.strptime(date_str, "%Y%m%d")
    except ValueError:
        try:
            birth_date = datetime.strptime(date_str, "%m%d%Y")
        except ValueError:
            return ""

    return date_str if (datetime.now() - birth_date).days >= 18 * 365 else ""


def physician_name_extraction(flattened_physician_info, without_top_info):
    answer = find_physician_name(flattened_physician_info)
    answer = replace_none(truncate_string(clean_spaces(remove_substrings(answer))))
    if answer is None or answer == "":
        physician_name_info = str(
            extract_marker_surroundings(without_top_info, ["Practice Name"], 20)
        )
        if physician_name_info:
            answer = physician_name_info.split("Practice")[0].strip()
            answer = answer.replace("[", "")
            answer = answer.replace("'", "")
            answer = answer.replace(":", "")
    return answer


def patient_name_extraction(patient_info, without_top_info, question):
    del_markers = ["First", "First,", "Last", "Middle", "Date", "of", ",", ".", ":"]
    flattened_patient_info = [
        item for sublist in patient_info for sublist2 in sublist for item in sublist2
    ]

    answer = find_patient_name(flattened_patient_info)
    if answer:
        for del_marker in del_markers:
            escaped_marker = re.escape(del_marker)
            answer = re.sub(escaped_marker, "", answer, flags=re.IGNORECASE)

    if not contains_letters(answer):
        patient_markers = [
            "Name (Last, First, Middle)",
            "First, Middle",
            "Employee Name",
            "(Last, First,",
            "Name (Last,",
        ]
        answer = str(
            extract_marker_surroundings(without_top_info, patient_markers, 20, False)
        )
        if not answer or answer == "[]":
            answer = answer_question(question, flattened_patient_info)
        if answer:
            for patient_marker in patient_markers:
                escaped_marker = re.escape(patient_marker)
                answer = re.sub(escaped_marker, "", answer, flags=re.IGNORECASE)
            for del_marker in del_markers:
                escaped_marker = re.escape(del_marker)
                answer = re.sub(escaped_marker, "", answer, flags=re.IGNORECASE)
        if answer:
            answer = re.sub(r"[#%&{}().,\\:<>*?/$!'+`|=]", "", answer)
    if answer is None:
        answer = ""
    return answer


def physician_specialty_extraction(physician_info, question, combined_context):
    if not isinstance(physician_info, str):
        raise TypeError("physician_info must be a string")

    for specialty in physician_specialty_list:
        if re.search(specialty, physician_info, flags=re.IGNORECASE):
            return specialty

    return answer_question(question, [combined_context])


def adjuster_name_extraction(adjuster_info, question, combined_context):
    answer = answer_question(question, [combined_context])
    if answer:
        if "address" in answer or "Address" in answer:
            answer = ""

    answer = replace_none(truncate_string(clean_spaces(remove_substrings(answer))))

    if not answer or answer == "[]" or answer == "":
        return find_adjuster_name(adjuster_info)
    return answer


def find_adjuster_name(adjuster_info):
    def flatten_list(nested_list):
        """Utility function to completely flatten a nested list"""
        for item in nested_list:
            if isinstance(item, Iterable) and not isinstance(item, str):
                yield from flatten_list(item)
            else:
                yield item

    # Completely flatten the nested list
    flattened_list_adjuster = list(flatten_list(adjuster_info))

    names = []
    add_next_item = False
    pattern = re.compile(
        r"(Adjuster Name|Contact Name|Adjuster):\s*(.*)", re.IGNORECASE
    )
    for i, item in enumerate(flattened_list_adjuster):
        if not isinstance(item, str):
            continue
        match = pattern.search(item)
        if match:
            name = match.group(2).strip()
            if name:
                names.append(name)
                continue
        if add_next_item:
            names.append(item)
            add_next_item = False
            continue
        if pattern.search(item) and (i + 1) < len(flattened_list_adjuster):
            add_next_item = True
    answer = " ".join(names)
    if "address" in answer or "Address" in answer:
        return ""
    return answer


def find_metadata_and_save_info(
    qa_results,
    top_page_info,
    patient_info,
    physician_info,
    doc_date,
    adjuster_info,
    claim_info,
    without_top_info,
):
    for category, questions in categorized_questions.items():
        if category not in qa_results:
            qa_results[category] = {}

        if category == "documentDate":
            combined_context = tokenize_text(top_page_info)
            receipt_data = find_datetime_strings(combined_context)
            qa_results["receiptDate"] = receipt_data
            if doc_date is None or doc_date == "":
                doc_date = receipt_data
            qa_results["documentDate"] = doc_date

        elif category == "physician":
            context_parts = physician_info
        elif category == "adjuster":
            context_parts = adjuster_info
        elif category == "claimant" or "employer" or "claim":
            context_parts = patient_info
        if category != "documentDate":
            flat_context = [
                " ".join(item) if isinstance(item, list) else item
                for sublist in context_parts
                for item in sublist
            ]
            combined_context = " ".join(flat_context)
            combined_context = combined_context.replace("'", "")
            combined_context = combined_context.replace(",", "")
            for question, question_label in questions.items():
                # physician_name extraction
                if question == "What is Name:?":
                    answer = physician_name_extraction(physician_info, without_top_info)

                # physician specialty extraction
                elif question == "What is Specialty: ?":
                    answer = physician_specialty_extraction(
                        str(physician_info), question, combined_context
                    )

                # claimant_name extraction
                elif question == "What is Name (Last, First, Middle):?":
                    answer = patient_name_extraction(
                        patient_info, without_top_info, question
                    )

                # claim number extraction
                elif question == "What is Claim Number:?" and claim_info != []:
                    combined_context_claim_info = flatten_to_string_list(claim_info)
                    answer = answer_question(question, combined_context_claim_info)
                    if not answer or answer == "[]":
                        answer = answer_question(question, [combined_context])
                    if answer and "emp" in answer:
                        answer = answer.replace("emplo", "")
                        answer = answer.replace("empl", "")
                        answer = answer.replace("emp", "")
                    if answer:
                        answer = re.sub(r"[#%&{}().,\\:<>*?/$!'+`|=]", "", answer)
                # adjuster name extraction
                elif question == "what is Contact Name:?":
                    answer = adjuster_name_extraction(
                        adjuster_info, question, combined_context
                    )

                else:
                    answer = answer_question(question, [combined_context])
                    if "E-mail" in question:
                        answer = glue_words_together(answer)
                if answer:
                    answer = answer[:32]
                qa_results[category][question_label] = answer
        else:
            sender_info = flatten_to_string_list(top_page_info)
            answer = find_first_person_name_spacy(sender_info)
            if not answer or "Page" in answer or "page" in answer:
                answer = get_sender_name(sender_info)
            if answer:
                answer = "".join(filter(lambda x: not x.isdigit(), answer))
                answer = answer.replace("page", "")
                answer = answer.replace("Page", "")
                answer = answer.replace("on", "")
                answer = answer.replace("/", "")

            qa_results["senderName"] = answer
    qa_results = format_json(qa_results)

    # additional check for availability
    if (
        qa_results["claim"]["claimNumber"] is None
        or qa_results["claim"]["claimNumber"] == ""
    ):
        qa_results["claim"]["claimNumber"] = find_first_claim_number(
            str(without_top_info)
        )
    if (
        qa_results["claim"]["dateOfInjuryFrom"] is None
        or qa_results["claim"]["dateOfInjuryFrom"] == ""
    ):
        date_of_injury = find_datetime_strings(str(patient_info))
        if date_of_injury:
            qa_results["claim"]["dateOfInjuryFrom"] = date_of_injury.replace("/", "")
    if (
        qa_results["claimant"]["dateOfBirth"] is None
        or qa_results["claimant"]["dateOfBirth"] == ""
    ):
        date_of_birth = find_last_date(str(patient_info))
        if date_of_birth:
            qa_results["claimant"]["dateOfBirth"] = date_of_birth.replace("/", "")
    if (
        qa_results["claimant"]["dateOfBirth"] is not None
        and qa_results["claimant"]["dateOfBirth"] != ""
    ):
        qa_results["claimant"]["dateOfBirth"] = is_older_than_18(
            qa_results["claimant"]["dateOfBirth"]
        )
    if (
        qa_results["adjuster"]["adjusterName"] == qa_results["adjuster"]["company"]
        and qa_results["adjuster"]["adjusterName"] != ""
        and qa_results["adjuster"]["adjusterName"] is not None
    ):
        qa_results["adjuster"]["adjusterName"] = find_adjuster_name(adjuster_info)
    return qa_results


def is_vertical_bbox(bbox, threshold=1.5):
    x_coords = [point[0] for point in bbox]
    y_coords = [point[1] for point in bbox]

    width = max(x_coords) - min(x_coords)
    height = max(y_coords) - min(y_coords)

    aspect_ratio = height / width if width != 0 else float("inf")
    return aspect_ratio > threshold


def get_all_pages_text(ocred_text):
    all_pages_text = []

    for page in ocred_text:
        page_strings = []
        for string_data in page:
            if is_vertical_bbox(string_data[0]):
                continue
            string = string_data[-1][0]
            page_strings.append(string)
        all_pages_text.append(page_strings)

    return all_pages_text


def concatenate_page_texts(result):
    all_strings = []
    for page in result:
        if not page:
            continue
        for string_data in page:
            string = string_data[-1][0]
            all_strings.append(string)

    text = " ".join(all_strings)

    return text


def set_empty_strings_recursively(d):
    for key, value in d.items():
        if isinstance(value, dict):
            set_empty_strings_recursively(value)
        else:
            if not any(
                val in key for val in ["receiptDate", "documentDate", "senderName"]
            ):
                d[key] = ""
    return d


def update_qa_results_with_llm_response(qa_results: dict, qa_results_llm: dict) -> dict:
    """
    Updates the results extracted by BERT with the response from LLM in a structured JSON format.

    Args:
        qa_results (dict): Metadata extracted by BERT.
        qa_results_llm (dict): Metadata extracted by LLM.

    Returns:
        dict: Updated `qa_results` object with the metadata extracted by LLM.
    """
    if not qa_results_llm:
        return qa_results

    qa_results_llm = qa_results_llm.model_dump()

    if qa_results_llm.get("claimant"):
        if qa_results_llm["claimant"].get("full_name"):
            qa_results["claimant"]["claimantName"] = qa_results_llm["claimant"][
                "full_name"
            ]
        if qa_results_llm["claimant"].get("first_name"):
            qa_results["claimant"]["firstName"] = qa_results_llm["claimant"][
                "first_name"
            ]
        if qa_results_llm["claimant"].get("last_name"):
            qa_results["claimant"]["lastName"] = qa_results_llm["claimant"]["last_name"]
        if qa_results_llm["claimant"].get("middle_name"):
            qa_results["claimant"]["middleName"] = qa_results_llm["claimant"][
                "middle_name"
            ]
        if qa_results_llm["claimant"].get("suffix"):
            qa_results["claimant"]["suffix"] = qa_results_llm["claimant"]["suffix"]
        if qa_results_llm["claimant"].get("employer_name"):
            qa_results["employer"]["name"] = qa_results_llm["claimant"]["employer_name"]
        if qa_results_llm["claimant"].get("date_of_birth"):
            qa_results["claimant"]["dateOfBirth"] = reformat_date(
                qa_results_llm["claimant"]["date_of_birth"]
            )

    if qa_results_llm.get("physician"):
        if qa_results_llm["physician"].get("full_name"):
            qa_results["physician"]["physicianName"] = qa_results_llm["physician"][
                "full_name"
            ]
        if qa_results_llm["physician"].get("first_name"):
            qa_results["physician"]["firstName"] = qa_results_llm["physician"].get(
                "first_name", ""
            )
        if qa_results_llm["physician"].get("last_name"):
            qa_results["physician"]["lastName"] = qa_results_llm["physician"].get(
                "last_name", ""
            )
        if qa_results_llm["physician"].get("credentials"):
            qa_results["physician"]["credentials"] = transform_credentials(
                qa_results_llm["physician"].get("credentials", "")
            )
        if qa_results_llm["physician"].get("npi"):
            qa_results["physician"]["npi"] = qa_results_llm["physician"]["npi"]
        if qa_results_llm["physician"].get("specialty"):
            qa_results["physician"]["specialty"] = qa_results_llm["physician"][
                "specialty"
            ]
        if qa_results_llm["physician"].get("facility_name"):
            qa_results["physician"]["facilityName"] = qa_results_llm["physician"][
                "facility_name"
            ]
        if qa_results_llm["physician"].get("email_address"):
            qa_results["physician"]["emailAddress"] = qa_results_llm["physician"][
                "email_address"
            ]
        if qa_results_llm["physician"].get("phone_number"):
            qa_results["physician"]["phoneNumber"] = transform_phone_number(
                qa_results_llm["physician"]["phone_number"]
            )
        if qa_results_llm["physician"].get("fax_number"):
            qa_results["physician"]["faxNumber"] = transform_phone_number(
                qa_results_llm["physician"]["fax_number"]
            )
        if qa_results_llm["physician"].get("key_contact_name"):
            qa_results["physician"]["keyContactName"] = qa_results_llm["physician"][
                "key_contact_name"
            ]
        if qa_results_llm["physician"].get("address"):
            if qa_results_llm["physician"]["address"].get("street"):
                qa_results["physician"]["address"] = qa_results_llm["physician"][
                    "address"
                ]["street"]
            if qa_results_llm["physician"]["address"].get("space"):
                qa_results["physician"]["space"] = qa_results_llm["physician"][
                    "address"
                ]["space"]
            if qa_results_llm["physician"]["address"].get("city"):
                qa_results["physician"]["city"] = qa_results_llm["physician"][
                    "address"
                ]["city"]
            if qa_results_llm["physician"]["address"].get("state"):
                qa_results["physician"]["state"] = qa_results_llm["physician"][
                    "address"
                ]["state"]
            if qa_results_llm["physician"]["address"].get("zip_code"):
                qa_results["physician"]["zip"] = qa_results_llm["physician"]["address"][
                    "zip_code"
                ]

    if qa_results_llm.get("claim"):
        if qa_results_llm["claim"].get("claim_number"):
            qa_results["claim"]["claimNumber"] = qa_results_llm["claim"]["claim_number"]
        if qa_results_llm["claim"].get("date_of_injury_from"):
            qa_results["claim"]["dateOfInjuryFrom"] = reformat_date(
                qa_results_llm["claim"]["date_of_injury_from"]
            )
        if qa_results_llm["claim"].get("date_of_injury_to"):
            qa_results["claim"]["dateOfInjuryThrough"] = reformat_date(
                qa_results_llm["claim"]["date_of_injury_to"]
            )
            if (
                qa_results["claim"]["dateOfInjuryThrough"]
                == qa_results["claim"]["dateOfInjuryFrom"]
            ):
                qa_results["claim"]["dateOfInjuryThrough"] = ""

    if qa_results_llm.get("adjuster"):
        if qa_results_llm["adjuster"].get("adjuster_name"):
            qa_results["adjuster"]["adjusterName"] = qa_results_llm["adjuster"][
                "adjuster_name"
            ]
        if qa_results_llm["adjuster"].get("first_name"):
            qa_results["adjuster"]["firstName"] = qa_results_llm["adjuster"][
                "first_name"
            ]
        if qa_results_llm["adjuster"].get("last_name"):
            qa_results["adjuster"]["lastName"] = qa_results_llm["adjuster"]["last_name"]
        if qa_results_llm["adjuster"].get("middle_name"):
            qa_results["adjuster"]["middleName"] = qa_results_llm["adjuster"][
                "middle_name"
            ]
        if qa_results_llm["adjuster"].get("suffix"):
            qa_results["adjuster"]["suffix"] = qa_results_llm["adjuster"]["suffix"]
        if qa_results_llm["adjuster"].get("key_contact_name"):
            qa_results["adjuster"]["keyContact"] = qa_results_llm["adjuster"][
                "key_contact_name"
            ]
        if qa_results_llm["adjuster"].get("company"):
            qa_results["adjuster"]["company"] = qa_results_llm["adjuster"]["company"]
        if qa_results_llm["adjuster"].get("phone_number"):
            qa_results["adjuster"]["phoneNumber"] = transform_phone_number(
                qa_results_llm["adjuster"]["phone_number"]
            )
        if qa_results_llm["adjuster"].get("phone_extension"):
            qa_results["adjuster"]["phoneExt"] = qa_results_llm["adjuster"][
                "phone_extension"
            ]
        if qa_results_llm["adjuster"].get("fax_number"):
            qa_results["adjuster"]["faxNumber"] = transform_phone_number(
                qa_results_llm["adjuster"]["fax_number"]
            )
        if qa_results_llm["adjuster"].get("email_address"):
            qa_results["adjuster"]["emailAddress"] = qa_results_llm["adjuster"][
                "email_address"
            ]
        if qa_results_llm["adjuster"].get("address"):
            if qa_results_llm["adjuster"]["address"].get("street"):
                qa_results["adjuster"]["address"] = qa_results_llm["adjuster"][
                    "address"
                ]["street"]
            if qa_results_llm["adjuster"]["address"].get("space"):
                qa_results["adjuster"]["space"] = qa_results_llm["adjuster"]["address"][
                    "space"
                ]
            if qa_results_llm["adjuster"]["address"].get("city"):
                qa_results["adjuster"]["city"] = qa_results_llm["adjuster"]["address"][
                    "city"
                ]
            if qa_results_llm["adjuster"]["address"].get("state"):
                qa_results["adjuster"]["state"] = qa_results_llm["adjuster"]["address"][
                    "state"
                ]
            if qa_results_llm["adjuster"]["address"].get("zip_code"):
                qa_results["adjuster"]["zip"] = qa_results_llm["adjuster"]["address"][
                    "zip_code"
                ]

    return qa_results


def extract_metadata_from_rfa(ocred_text, splitted_document_file_stream):
    attorney = None
    all_pages_text = get_all_pages_text(ocred_text)
    qa_results = initialize_qa_results()
    if is_attorney(all_pages_text):
        attorney = attorney_metadata(all_pages_text)
        qa_results = transform_qa_results(qa_results)
        qa_results["metaData"]["attorney"]["company"] = attorney
    else:
        (
            top_page_info,
            patient_info,
            physician_info,
            doc_date,
            adjuster_info,
            request_info,
            claim_info,
            without_top_info,
        ) = clustering_of_page_text(all_pages_text)

        patient_info_for_llm = " ".join(flatten_to_string_list(patient_info))
        physician_info_for_llm = " ".join(flatten_to_string_list(physician_info))
        adjuster_info_for_llm = " ".join(flatten_to_string_list(adjuster_info))
        claim_info_for_llm = " ".join(flatten_to_string_list(claim_info))

        qa_results_llm = process_rfa_llm(
            physician_information_text=physician_info_for_llm,
            claimant_information_text=patient_info_for_llm,
            adjuster_information_text=adjuster_info_for_llm,
            claim_information=claim_info_for_llm,
        )

        qa_results = find_metadata_and_save_info(
            qa_results,
            top_page_info,
            patient_info,
            physician_info,
            doc_date,
            adjuster_info,
            claim_info,
            without_top_info,
        )

        qa_results = set_empty_strings_recursively(qa_results)

        qa_results = update_qa_results_with_llm_response(qa_results, qa_results_llm)

        qa_results["rushFlag"] = run_rfa_stamp_detection(splitted_document_file_stream)
        qa_results["rushFlag"] = (
            "Y" if run_rfa_stamp_detection(splitted_document_file_stream) else "N"
        )
        qa_results = transform_qa_results(qa_results)
        existing_data = run_tick_detection(splitted_document_file_stream)
        if existing_data:
            qa_results["metaData"]["expeditedFlag"] = (
                "Y" if existing_data["expeditedFlag"] else "N"
            )
        else:
            qa_results["metaData"]["expeditedFlag"] = "N"
        table_data = run_rfa_table_extraction(ocred_text, splitted_document_file_stream)
        qa_results["metaData"]["treatments"] = table_data if table_data else []
    qa_results.pop("patientName", None)
    return qa_results
