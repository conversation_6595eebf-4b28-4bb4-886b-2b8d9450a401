import traceback

import fitz
import numpy as np
import torch
from paddleocr import <PERSON><PERSON>OC<PERSON>
from ultralytics import YOLO
from ultralytics.utils.plotting import Annotator
import os

CUSTOM_OCR_PIPELINE = PaddleOCR(
    use_angle_cls=True, lang="en", show_log=False, use_gpu=True
)

DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

RFA_EXPEDITED_CHECKBOX_MODEL_PATH = os.getenv(
    "RFA_EXPEDITED_CHECKBOX_MODEL_PATH", "/models/rfa_expedited_checkbox_model.pt"
)

YOLO_MODEL = YOLO(RFA_EXPEDITED_CHECKBOX_MODEL_PATH).to(DEVICE)

FLAG_KEYWORDS = {
    "expeditedFlag": [
        "Expedited Review",
        "d Revi",
        "Expedited",
        "Exped",
        "faces an im",
    ],
    "newSubmission": ["New Request", "Ncw Rcqucst", "n Ret", "New Requost"],
    "resubmission": ["Resubmission", "Resabaissisn"],
}


def save_first_page_as_square_image(file_stream: str) -> np.array:
    """
    Converts the first page of PDF in the specified directory to a square image.
    """
    try:
        doc = fitz.open(stream=file_stream, filetype="pdf")
        page = doc.load_page(0)
        rect = page.rect
        square_rect = fitz.Rect(
            0, 0, min(rect.width, rect.height), min(rect.width, rect.height)
        )
        pix = page.get_pixmap(clip=square_rect)
        img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
            pix.height, pix.width, pix.n
        )
    except Exception as e:
        print(f"Error processing {Exception}: {e}")
    finally:
        doc.close()
    return img


def box_coordinates(img: np.array) -> dict:
    """
    Extracts the bounding boxes of the checkboxes from the image.
    """
    bounding_boxes = {flag_name: [] for flag_name in FLAG_KEYWORDS.keys()}
    if img is not None:
        result = CUSTOM_OCR_PIPELINE.ocr(img)
        result = result[0]

        for flag_name, keywords in FLAG_KEYWORDS.items():
            for line in result:
                box, txt = line[0], line[1][0]

                if any(keyword in txt for keyword in keywords):
                    saved_box = box
                    modified_box = [
                        [max(saved_box[0][0] - 40, 0), saved_box[0][1]],
                        [saved_box[1][0], saved_box[1][1]],
                        [saved_box[2][0], saved_box[2][1]],
                        [max(saved_box[3][0] - 40, 0), saved_box[3][1]],
                    ]

                    x_min = min(point[0] for point in modified_box)
                    x_max = max(point[0] for point in modified_box)
                    y_min = min(point[1] for point in modified_box)
                    y_max = max(point[1] for point in modified_box)

                    if x_min < x_max and y_min < y_max:
                        bounding_boxes[flag_name].append([x_min, x_max, y_min, y_max])
                    else:
                        print("Warning: Invalid bounding box. Skipping.")
    else:
        print("Failed to load image")

    return bounding_boxes


def ocr_bbox2yolo_bbox(bbox: list[int]) -> list[int]:
    """
    Converts a PaddleOCR bounding box to a YOLO bounding box.
    """
    return [bbox[0], bbox[2], bbox[1], bbox[3]]


def bbox_overlap_percentage(checkbox_bbox: list[int], text_bbox: list[int]) -> float:
    """
    Calculates the percentage of the YOLO bounding box that overlaps with the PaddleOCR bounding box.
    """

    x1_intersect = max(checkbox_bbox[0], text_bbox[0])
    y1_intersect = max(checkbox_bbox[1], text_bbox[1])
    x2_intersect = min(checkbox_bbox[2], text_bbox[2])
    y2_intersect = min(checkbox_bbox[3], text_bbox[3])

    if x1_intersect >= x2_intersect or y1_intersect >= y2_intersect:
        return 0.0

    intersection_area = (x2_intersect - x1_intersect) * (y2_intersect - y1_intersect)
    yolo_area = (checkbox_bbox[2] - checkbox_bbox[0]) * (
        checkbox_bbox[3] - checkbox_bbox[1]
    )
    overlap_percentage = intersection_area / yolo_area

    return overlap_percentage


def process_images_and_save_info(img: np.array, model: YOLO) -> list[dict]:
    """
    Detects 'unchecked' boxes in images and saves the detections.
    """
    detections_info = []

    results = model.predict(img, conf=0.5, verbose=False)
    image_detections = {"detections": []}
    for r in results:
        annotator = Annotator(img, line_width=2, example=str(model.names))
        for box in r.boxes:
            if model.names[int(box.cls)] == "unchecked":
                annotator.box_label(
                    box.xyxy[0],
                    f"unchecked {round(box.conf.item(), 2)}",
                    color=(10, 90, 10),
                )
                image_detections["detections"].append(
                    {
                        "class_name": "unchecked",
                        "confidence": round(box.conf.item(), 2),
                        "bbox": box.xyxy[0].tolist(),
                    }
                )
    if image_detections["detections"]:
        detections_info.append(image_detections)

    return detections_info


def check_for_unchecked_in_bbox(bbox_info: dict, detections_info: list[dict]) -> dict:
    """
    Checks if any of the checkboxes are unchecked inside the bounding box with a flag.
    """
    results = {}
    for flag_name, bboxes in bbox_info.items():
        if bbox_info[flag_name]:
            current_flag = True

            for bbox_coords in bboxes:
                average_bbox_y = (bbox_coords[2] + bbox_coords[3]) / 2

                for detection in detections_info:
                    for det in detection["detections"]:
                        if det["class_name"] == "unchecked":
                            ocr_text_bbox = bbox_coords
                            checkbox_bbox = det["bbox"]

                            converted_ocr_text_bbox = ocr_bbox2yolo_bbox(ocr_text_bbox)

                            overlap = bbox_overlap_percentage(
                                checkbox_bbox, converted_ocr_text_bbox
                            )

                            if overlap > 0.5:
                                current_flag = False
                                break
                if not current_flag:
                    break

            results[flag_name] = current_flag
        else:
            results[flag_name] = False

    return results


def run_tick_detection(file_stream: str) -> dict:
    try:
        img = save_first_page_as_square_image(file_stream)
        bounding_boxes = box_coordinates(img)
        detections_info = process_images_and_save_info(img, YOLO_MODEL)
        results = check_for_unchecked_in_bbox(bounding_boxes, detections_info)

        return results

    except Exception as e:
        print(f"An error occurred during processing: {e}")
        print(traceback.format_exc())
