import datetime
import json
import os
import re
import sys
from typing import Any, List, Optional

import yaml
from rfa.class_based_json import Metadata
from rfa.connect_to_mistral import chat_with_ollama
from rfa.rfa_checkbox import run_tick_detection
from rfa.rfa_stamp_detection import run_rfa_stamp_detection
from rfa.rfa_table_extraction import run_rfa_table_extraction

from pipeline_utils import JSONFormat

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)


def load_yaml():
    """Load the YAML file and return the data."""
    with open("keywords_config.yaml", "r") as yaml_file:
        return yaml.safe_load(yaml_file)


# Load the YAML config
yaml_data = load_yaml()

# ================== Markers variables ==================
doi_markers = yaml_data["ehs"]["metadata_keywords"]["claim"]["dateOfInjuryFrom"]
claim_number_markers = yaml_data["ehs"]["metadata_keywords"]["claim"]["claimNumber"]
dob_markers = yaml_data["ehs"]["metadata_keywords"]["claimant"]["dateOfBirth"]
patient_markers = yaml_data["ehs"]["metadata_keywords"]["claimant"]["claimantName"]
npi_markers = yaml_data["ehs"]["metadata_keywords"]["physician"]["npi"]
months = yaml_data["ehs"]["metadata_keywords"]["months"]
# ================== end of Markers variables ==================
# ================== Prompts variables ==================
prompt = yaml_data["ehs"]["prompts"]["prompt_beginning"]
example1_text = yaml_data["ehs"]["prompts"]["example1_text"]
example2_text = yaml_data["ehs"]["prompts"]["example2_text"]
example3_text = yaml_data["ehs"]["prompts"]["example3_text"]
example1_json = json.loads(yaml_data["ehs"]["jsons"]["example1_json"])
example2_json = json.loads(yaml_data["ehs"]["jsons"]["example2_json"])
example3_json = json.loads(yaml_data["ehs"]["jsons"]["example3_json"])
# ================== end of Prompts variables ==================
# ================== Regex patterns ==================
# Regex pattern to match all punctuation
remove_punctuation_and_digits_pattern = r"[^\w\s]|\d"
remove_punctuation_pattern = r"[^\w\r/]"
remove_trailing_white_spaces = r"^\s+|\s+$"
# ================== end of Regex patterns ==================

current_year = (
    datetime.datetime.now().year % 100
)  # Get the last two digits of the current year


def is_list_of_lists(variable):
    """
    Checks if the given variable is a list of lists.

    Parameters
    ----------
    variable : any
        The variable to check.

    Returns
    -------
    bool
        True if the variable is a list of lists, False otherwise.
    """
    return isinstance(variable, list) and all(
        isinstance(item, list) for item in variable
    )


def extract_marker_surroundings(
    data: str, markers: List[str], grip_size=50, start_grip=True
) -> str:
    """
    Extract text around the first occurrence of the marker in the given data.

    Parameters
    ----------
    data : str
        Data to search for the marker.
    markers : List[str]
        List of markers to search for.
    grip_size : int, optional
        Number of characters to extract before and after the marker, by default 50.
    start_grip : bool, optional
        If True, extract text before the marker, otherwise after the marker, by default True.

    Returns
    -------
    str
        Extracted text around the first occurrence of the marker.
    """
    if start_grip:
        grip_size_b = grip_size
    else:
        grip_size_b = 0
    results = ""

    first_occurrence_index = None
    # Find the first occurrence of the marker
    for marker in markers:
        index = data.find(marker)
        if index != -1:
            first_occurrence_index = index
            break

    # If the marker found the text around it
    if first_occurrence_index is not None:
        start = max(0, first_occurrence_index - grip_size_b)
        text_before_marker = data[start:first_occurrence_index]
        end = min(len(data), first_occurrence_index + grip_size)
        text_after_marker = data[first_occurrence_index:end]
        # first priority to the text after marker
        results = text_after_marker + text_before_marker

    return results


def find_datetime_strings(data: Any) -> Optional[str]:
    """
    Find datetime strings in the given data.

    Parameters
    ----------
    data : Any
        Data to search for datetime strings.

    Returns
    -------
    Optional[str]
        Datetime string if found, otherwise None.
    """
    if data:
        data = data.replace(",", "")
        datetime_pattern = (
            r"(\b\d{1,2}\.\d{1,2}\.\d{2}\b)|"
            r"(\b\d{1,2}/\d{1,2}/\d{4}\b)|"
            r"(\b\d{4}-\d{2}-\d{2} \d{2}:\d{2}(?::\d{2})? (?:PDT|PST)?\b)|"
            r"(\b\d{2}/\d{2}/\d{4} \d{1,2}:\d{2}(?: (?:AM|PM))?\b)|"
            r"(\b\d{4}-\d{2}-\d{2} \d{2}:\d{2}\b)|"
            r"(\b\d{2}/\d{2}/\d{4}\b)|"
            r"(\d{1,2}/\d{1,2}/\d{2})|"
            r"(\b\d{1,2}/\d{1,2}/\d{2}\b)|"
            r"(\b\d{2}/\d{2}/\d{4})\b|"
            r"(\bDate: \d{2}/\d{2}/\d{4}\b)|"
            r"(\bJanuary|February|March|April|May|June|July|August|September|October|November|December) \d{1,2}, \d{4} at \d{1,2}:\d{2}:\d{2} (?:AM|PM)\b|"
            r"(\b\d{1,2}/\d{1,2}/\d{4} \d{1,2}:\d{2}:\d{2} (?:AM|PM)\b)|"
            r"(\b\d{1,2}/\d{1,2}/\d{4} \d{2}:\d{2}\b)|"
            r"(\bDate: \d{2}/\d{2}/\d{4}\b)|"
            r"(\bDate: \d{1,2}/\d{1,2}/\d{4}\b)|"
            r"(\b\d{1,2} (January|February|March|April|May|June|July|August|September|October|November|December) \d{4} \d{1,2}:\d{2}:\d{2} (AM|PM)\b)|"
            r"(\bJanuary|February|March|April|May|June|July|August|September|October|November|December)\.\d{1,2}\.\d{4}\b|"
            r"(\b\d{2}-\d{1,2}-\d{2}\b)|"
            r"(\b(?:Date)?\d{1,2}/\d{1,2}/\d{4}\b)|"
            r"(\bDate:\d{2}/\d{2}/\d{4}\b)|"
            r"(\b\d{2}/\d{2}/\d{2}\b)|"
            r"(\b\d{2} (jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec) \d{4}\b)|"
            r"(\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\.\d{2}\.\d{4}\b)|"
            r"(\b\d{1,2}-(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)-\d{4}\b)|"
            r"(\b\d{1,2}(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\d{4}\b)|"
            r"(\b\d{1,2} Ju1 \d{4}\b)|"
            r"(\b\d{2}-\d{2}-\d{4}\b)|"
            r"(\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec) \d{1,2} \d{4}\b)|"
            r"(\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec) \d{1,2}, \d{4}\b)|"
            r"(\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/\d{1,2}/\d{4}\b)|"
            r"(\b\d{2}/\d{2}/\d{4})\d+|"
            r"(\b[A-Z]{3}/\d{2}/\d{4})\b|"
            r"(\b[A-Z]{3}/\d{2}/\d{4})/[A-Z]{3}\b\|"
            r"(\b\d{4}-\d{2}-\d{2})\d+|"
            r"(\b[A-Z]{3}\.\d{1,2}\.\d{4})[^\\n]*|"
            r"\b0?(\d{2}-\d{2}-\d{2})\b|"
            r"(\b\d{2})/(\d{2})(\d{4})\b|"
            r"(\d{2}/\d{2})(\d{4})|"
            r"(\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\.\d{1,2}\.\d{4})\b|"
            r"(\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec) \d{1,2}, \d{4}\b)|"
            r"(\b[A-Z]{3}\.\s?\d{1,2}\.\d{4})\b|"
            r"(\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s\d{1,2},\s\d{4})\b"
        )
        match = re.search(datetime_pattern, data)
        if match:
            return match.group()
        return None
    return None


def get_all_pages_text(ocred_text):
    """
    This function processes the OCRed text of a document, concatenates the text from each page, and stores the concatenated text of each page in a list.

    Parameters
    ----------
    ocred_text : list of lists
        The OCRed text of the file, where each element is a list representing a page, and each page contains lists of string data.

    Returns
    -------
    str
        String with concatenated text from pages
    """
    page_string = ""
    for page in ocred_text:
        if page:
            # if there is any text+coords on the page, collect only text
            page_text = " ".join([string_data[-1][0] for string_data in page])
            page_string += f"{page_text.strip()} "
    all_pages_text = page_string.strip()
    return all_pages_text


def find_first_claim_number(text):
    """
    Find the first claim number in the given text.

    Parameters
    ----------
    text : str
        Text to search for the claim number.

    Returns
    -------
    Optional[str]
        Claim number if found, otherwise None.
    """
    # Regex pattern to match claim numbers
    pattern = r"\b((\d{3}-\d{2}-\d{4}[A-Za-z]?)|([A-Za-z]{1,3}\d{7,12})|(\d{5}-\d{5}-[A-Za-z]{3})|(\d{6,11})|([A-Za-z]{2}\d{9}))\b"
    match = re.search(pattern, text)
    if match:
        return match.group(0)
    else:
        return None


def extract_npi(text: str) -> str | None:
    """
    Extract NPI number from the given text.

    Parameters
    ----------
    text : str
        Text to search for the NPI number.

    Returns
    -------
    Optional[str]
        NPI number if found, otherwise None.
    """
    if text:
        npi = re.search(r"(?:\d-?){10}", text)
        if npi:
            return npi.group().replace("-", "")
    return None


def transform_date_to_standard(date: str) -> str | None:
    """
    Transform the date to the standard format (YYYYMMDD).

    The function replaces month names with their corresponding numeric values,
    removes punctuation, and ensures that day and month values are two digits.
    It also handles two-digit years by converting them to four-digit years.

    Parameters
    ----------
    date : str
        Date to transform.

    Returns
    -------
    Optional[str]
        Transformed date in the standard format (YYYYMMDD) if the date is found, otherwise None.
    """
    if date:
        # Replace month names with their corresponding numeric values
        for month in months:
            if month in date:
                date = date.replace(month, months[month])
        # Remove punctuation
        modified_text = re.sub(remove_punctuation_pattern, "/", date)
        # Find out what delimiter is used in the date
        if "/" in modified_text:
            dates = modified_text.split("/")
        elif "-" in modified_text:
            dates = modified_text.split("-")
        else:
            dates = modified_text.split(" ")
        # Ensure that day and month values are two digits
        for idx, date in enumerate(dates):
            if len(date) == 1:
                dates[idx] = "0" + date
        # Handle two-digit years
        if len(dates[2]) == 2:
            if int(dates[2]) > current_year:
                dates[2] = "19" + dates[2]
            else:
                dates[2] = "20" + dates[2]
        return dates[2] + dates[0] + dates[1]
    return None


def extract_metadata_from_texas_rfa(ocred_text, splitted_document_file_stream):
    """
    Extract metadata from the RFA document.

    Parameters
    ----------
    ocred_text : list of lists
        The OCRed text of the file, where each element is a list representing a page, and each page contains lists of string data.
    splitted_document_file_stream : bytes
        File images in bytes

    Returns
    -------
    dict
        Extracted metadata from the RFA document in the standard format.
    """
    # Initialize internal QA results dictionary
    qa_results = JSONFormat.GetFieldsForDocument("RFA")
    # If incorrect format of input
    if not is_list_of_lists(ocred_text):
        print("Input text is not list of lists")
        qa_results = yaml_data["ehs"]["qa_results"]
        return qa_results
    attorney = None
    all_pages_text = get_all_pages_text(ocred_text)
    all_pages_text = all_pages_text.capitalize()
    # ================== Extract metadata from the RFA document ==================
    # Extract metadata from the RFA document by using the predefined markers
    qa_results["metaData"]["claim"]["dateOfInjuryFrom"] = find_datetime_strings(
        extract_marker_surroundings(all_pages_text, doi_markers, grip_size=25)
    )
    qa_results["metaData"]["claim"]["dateOfInjuryThrough"] = ""
    qa_results["metaData"]["claim"]["claimNumber"] = find_first_claim_number(
        extract_marker_surroundings(all_pages_text, claim_number_markers, grip_size=30)
    )
    qa_results["namingData"]["claimNumber"] = qa_results["metaData"]["claim"][
        "claimNumber"
    ]
    qa_results["metaData"]["claimant"]["dateOfBirth"] = find_datetime_strings(
        extract_marker_surroundings(all_pages_text, dob_markers, grip_size=30)
    )
    qa_results["metaData"]["physician"]["npi"] = extract_npi(
        extract_marker_surroundings(all_pages_text, npi_markers, grip_size=30)
    )

    # Extract metadata from the RFA document by using the LLM
    messages = [
        {"role": "user", "content": f"{prompt} {example1_text}"},
        {"role": "assistant", "content": json.dumps(example1_json)},
        {"role": "user", "content": f"{prompt} {example2_text}"},
        {"role": "assistant", "content": json.dumps(example2_json)},
        {"role": "user", "content": f"{prompt} {example3_text}"},
        {"role": "assistant", "content": json.dumps(example3_json)},
    ]
    response = chat_with_ollama(
        messages=messages
        + [
            {
                "role": "user",
                "content": f"""
    {prompt}
    {all_pages_text}
    """,
            }
        ],
        format=Metadata.model_json_schema(),
        options={"temperature": 0},
    )
    temp_json = response.get("message").get("content")
    print(temp_json)
    if temp_json:
        try:
            # Fitting final_json
            new_json = json.loads(temp_json)
            qa_results["metaData"]["docDate"] = transform_date_to_standard(
                new_json.get("doc_date", "")
            )
            qa_results["metaData"]["adjuster"]["company"] = new_json.get(
                "adjuster", {}
            ).get("company", "")
            qa_results["metaData"]["adjuster"]["firstName"] = new_json.get(
                "adjuster", {}
            ).get("first_name", "")
            qa_results["metaData"]["adjuster"]["lastName"] = new_json.get(
                "adjuster", {}
            ).get("last_name", "")
            qa_results["metaData"]["adjuster"]["address"]["address1"] = (
                new_json.get("adjuster", {}).get("address", {}).get("street", "")
            )
            qa_results["metaData"]["adjuster"]["address"]["address2"] = (
                new_json.get("adjuster", {}).get("address", {}).get("space", "")
            )
            qa_results["metaData"]["adjuster"]["address"]["city"] = (
                new_json.get("adjuster", {}).get("address", {}).get("city", "")
            )
            qa_results["metaData"]["adjuster"]["address"]["state"] = (
                new_json.get("adjuster", {}).get("address", {}).get("state", "")
            )
            qa_results["metaData"]["adjuster"]["address"]["zip"] = (
                new_json.get("adjuster", {}).get("address", {}).get("zip_code", "")
            )
            qa_results["metaData"]["adjuster"]["phoneNumber"] = new_json.get(
                "adjuster", {}
            ).get("phone_number", "")
            qa_results["metaData"]["adjuster"]["phoneNumber"] = re.sub(
                remove_punctuation_pattern,
                "",
                qa_results["metaData"]["adjuster"]["phoneNumber"],
            )
            qa_results["metaData"]["adjuster"]["phoneExt"] = new_json.get(
                "adjuster", {}
            ).get("phone_ext", "")
            qa_results["metaData"]["adjuster"]["faxNumber"] = new_json.get(
                "adjuster", {}
            ).get("fax_number", "")
            qa_results["metaData"]["adjuster"]["faxNumber"] = re.sub(
                remove_punctuation_pattern,
                "",
                qa_results["metaData"]["adjuster"]["faxNumber"],
            )
            qa_results["metaData"]["adjuster"]["emailAddress"] = new_json.get(
                "adjuster", {}
            ).get("email_address", "")
            qa_results["metaData"]["physician"]["firstName"] = new_json.get(
                "physician", {}
            ).get("first_name", "")
            qa_results["metaData"]["physician"]["lastName"] = new_json.get(
                "physician", {}
            ).get("last_name", "")
            qa_results["metaData"]["physician"]["keyContactName"] = new_json.get(
                "physician", {}
            ).get("key_contact_name", "")
            qa_results["metaData"]["physician"]["credentials"] = new_json.get(
                "physician", {}
            ).get("credentials", "")
            qa_results["metaData"]["physician"]["speciality"] = new_json.get(
                "physician", {}
            ).get("speciality", "")
            qa_results["metaData"]["physician"]["address"]["address1"] = (
                new_json.get("physician", {}).get("address", {}).get("street", "")
            )
            qa_results["metaData"]["physician"]["address"]["address2"] = (
                new_json.get("physician", {}).get("address", {}).get("space", "")
            )
            qa_results["metaData"]["physician"]["address"]["city"] = (
                new_json.get("physician", {}).get("address", {}).get("city", "")
            )
            qa_results["metaData"]["physician"]["address"]["state"] = (
                new_json.get("physician", {}).get("address", {}).get("state", "")
            )
            qa_results["metaData"]["physician"]["address"]["zip"] = (
                new_json.get("physician", {}).get("address", {}).get("zip_code", "")
            )
            qa_results["metaData"]["physician"]["phoneNumber"] = new_json.get(
                "physician", {}
            ).get("phone_number", "")
            qa_results["metaData"]["physician"]["phoneNumber"] = re.sub(
                remove_punctuation_pattern,
                "",
                qa_results["metaData"]["physician"]["phoneNumber"],
            )
            qa_results["metaData"]["physician"]["faxNumber"] = new_json.get(
                "physician", {}
            ).get("fax_number", "")
            qa_results["metaData"]["physician"]["faxNumber"] = re.sub(
                remove_punctuation_pattern,
                "",
                qa_results["metaData"]["physician"]["faxNumber"],
            )
            qa_results["metaData"]["physician"]["emailAddress"] = new_json.get(
                "physician", {}
            ).get("email_address", "")
            qa_results["metaData"]["physician"]["facilityName"] = new_json.get(
                "physician", {}
            ).get("facility_name", "")
            qa_results["metaData"]["physician"]["type"] = new_json.get(
                "physician", {}
            ).get("physician_type", "")
            qa_results["metaData"]["claimant"]["firstName"] = new_json.get(
                "claimant", {}
            ).get("first_name", "")
            qa_results["metaData"]["claimant"]["lastName"] = new_json.get(
                "claimant", {}
            ).get("last_name", "")
            qa_results["metaData"]["claimant"]["middleName"] = new_json.get(
                "claimant", {}
            ).get("middle_name", "")
            qa_results["metaData"]["employer"]["name"] = new_json.get(
                "claimant", {}
            ).get("place_of_work", "")
            qa_results["metaData"]["claimant"]["address"]["address1"] = (
                new_json.get("claimant", {}).get("address", {}).get("street", "")
            )
            qa_results["metaData"]["claimant"]["address"]["address2"] = (
                new_json.get("claimant", {}).get("address", {}).get("space", "")
            )
            qa_results["metaData"]["claimant"]["address"]["city"] = (
                new_json.get("claimant", {}).get("address", {}).get("city", "")
            )
            qa_results["metaData"]["claimant"]["address"]["state"] = (
                new_json.get("claimant", {}).get("address", {}).get("state", "")
            )
            qa_results["metaData"]["claimant"]["address"]["zip"] = (
                new_json.get("claimant", {}).get("address", {}).get("zip_code", "")
            )
            qa_results["metaData"]["claimant"]["ssn"] = new_json.get(
                "claimant", {}
            ).get("ssn", "")
            qa_results["metaData"]["claimant"]["phoneNumber"] = new_json.get(
                "claimant", {}
            ).get("phone_number", "")
            qa_results["metaData"]["claimant"]["phoneNumber"] = re.sub(
                remove_punctuation_pattern,
                "",
                qa_results["metaData"]["claimant"]["phoneNumber"],
            )
            qa_results["metaData"]["claimant"]["emailAddress"] = new_json.get(
                "claimant", {}
            ).get("email_address", "")
            qa_results["metaData"]["claimant"]["gender"] = new_json.get(
                "claimant", {}
            ).get("gender", "")
            qa_results["metaData"]["claim"]["claimNumber"] = new_json.get(
                "claimant", {}
            ).get("claim_number", "")
            qa_results["metaData"]["claimant"]["dateOfBirth"] = (
                transform_date_to_standard(
                    new_json.get("claimant", {}).get("date_of_birth", "")
                )
            )
            qa_results["metaData"]["claim"]["dateOfInjuryFrom"] = (
                transform_date_to_standard(
                    new_json.get("claimant", {}).get("date_of_injury", "")
                )
            )
            qa_results["metaData"]["claim"]["dateOfInjuryThrough"] = (
                transform_date_to_standard(
                    new_json.get("claimant", {}).get("date_of_injury_through", "")
                )
            )
            qa_results["namingData"]["patientName"] = (
                qa_results["metaData"]["claimant"]["firstName"]
                + " "
                + qa_results["metaData"]["claimant"]["lastName"]
            )
        except:
            print("Exception")
    # Another scripts for extracting metadata
    rush_flag = run_rfa_stamp_detection(splitted_document_file_stream)
    qa_results["metaData"]["rushFlag"] = "Y" if rush_flag else "N"
    existing_data = run_tick_detection(splitted_document_file_stream)
    qa_results["metaData"]["expeditedFlag"] = (
        "Y" if existing_data and existing_data.get("expeditedFlag") else "N"
    )
    table_data = run_rfa_table_extraction(ocred_text, splitted_document_file_stream)
    qa_results["metaData"]["treatments"] = table_data if table_data else []
    return qa_results
