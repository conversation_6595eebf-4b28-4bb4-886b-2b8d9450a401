import fitz
import numpy as np
import timm
import torch
import torch.nn as nn
from PIL import Image
from torchvision import transforms
import os

model_name = os.getenv("HANDWRITTEN_MODEL_PATH", "/models/handwritten.pt")

if not os.path.exists(model_name):
    raise FileNotFoundError(f"Model file not found at {model_name}")

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
classifier = timm.create_model("efficientnet_b0", pretrained=True, num_classes=1)
classifier.load_state_dict(
    torch.load(model_name, map_location=torch.device(device=device))
)

# init the classifier model
classifier.to(device)
classifier.eval()


def convert_pdf_to_pixmap(file_stream):
    mat = fitz.Matrix(300 / 72, 300 / 72)
    doc = fitz.open(stream=file_stream, filetype="pdf")
    pages = []
    for page in doc:
        pix = page.get_pixmap(matrix=mat)
        pages.append(pix)
    return pages


def pixmap_to_image(pix):
    bytes = np.frombuffer(pix.samples, dtype=np.uint8)
    img = bytes.reshape(pix.height, pix.width, pix.n)
    return img


def classifier_inference(image, model, imgsz=(720, 720), device=torch.device("cuda")):
    trans = transforms.Compose(
        [
            transforms.Resize(imgsz),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ]
    )

    pred = None
    with torch.set_grad_enabled(False):
        img = trans(Image.fromarray(image).convert("RGB")).unsqueeze(0).to(device)
        outputs = model(img)
        outputs = nn.Sigmoid()(outputs)
        pred = outputs >= 0.5
        pred = pred.tolist()
    return pred


def classify_handwriting(image_array):
    pages = convert_pdf_to_pixmap(image_array)
    image = pixmap_to_image(pages[0])
    classifier_preds = classifier_inference(image, classifier, device=device)
    return classifier_preds[0][0]
