import base64
import json
import os
import re
import copy

import cv2
import fitz
import itertools
import numpy as np
from bs4 import BeautifulSoup
from dotenv import load_dotenv
from naming.prompts import BODY_PART_PROMPT, TABLE_PROMPT, TABLE_WITHOUT_HEADER_PROMPT
from ollama import Client
from paddleocr import PaddleOCR, PPStructure
from pydantic import BaseModel
from rapidfuzz import fuzz

layout_engine = PPStructure(
    layout=True,
    table=True,
    use_gpu=True,
    show_log=False,
    ocr=False,
)

table_engine = PPStructure(
    layout=False,
    use_gpu=True,
    show_log=False,
    cls=True,
    ocr=False,
    recovery=True,
)
custom_ocr = PaddleOCR(
    use_angle_cls=True,
    lang="en",
    ocr_version="PP-OCRv4",
    use_space_char=True,
    show_log=False,
    use_gpu=True,
)

load_dotenv()

# BASE_AUTH_USER_AND_PASSWORD = os.environ["BASE_AUTH_USER_AND_PASSWORD"]
SERVER_HOST = os.environ["SERVER_HOST"]
SERVER_PORT = os.environ["SERVER_PORT"]

# BASE64_STRING = base64.b64encode(BASE_AUTH_USER_AND_PASSWORD.encode()).decode("ascii")
# AUTH_HEADER = {"Authorization": f"Basic {BASE64_STRING}"}

OLLAMA_CLIENT = Client(
    host=f"{SERVER_HOST}:{SERVER_PORT}",
    # headers=AUTH_HEADER,
    timeout=360.0,
    verify=False,
)

OLLAMA_OPTIONS = {
    "num_predict": 768,
    "temperature": 0,
}

MODEL_NAME = "mistral-nemo:12b-instruct-2407-q5_K_M_medical_assistant"

TREATMENTS_START_PATTERN = r"R[eo][gq]u[ea]st[eo]d\s*Tr[oe]a[ti][mn]ent\s*?\(?[SsEe]ee"
TREATMENTS_END_PATTERN = r"(Re[qg][au]esting|Treating)\s*Physician\s*Sign[ae]ture"


with open("cpt_codes.txt", "r", encoding="utf-8") as f:
    CPT_CODES = f.read().splitlines()


class SingleTableResponse(BaseModel):
    diagnosis: str
    icd_code: str
    service_good_requested: str
    cpt_hcpcs_code: str
    other_information: str
    body_part: str


class TableResponse(BaseModel):
    tables: list[SingleTableResponse]


class SingleBodyPartResponse(BaseModel):
    body_part: str


class BodyPartResponse(BaseModel):
    body_parts: list[SingleBodyPartResponse]


def convert_pdf_to_pixmap(file_stream: bytes) -> list:
    mat = fitz.Matrix(300 / 72, 300 / 72)
    doc = fitz.open(stream=file_stream, filetype="pdf")
    pages = []
    for page in doc:
        pix = page.get_pixmap(matrix=mat)
        pages.append(pix)
    return pages


def pix_to_image(pix: fitz.Pixmap) -> np.ndarray:
    img_bytes = np.frombuffer(pix.samples, dtype=np.uint8)
    img = img_bytes.reshape(pix.height, pix.width, pix.n)
    return img


def process_with_ollama(input_prompt: str) -> str:
    response = OLLAMA_CLIENT.generate(
        model=MODEL_NAME, prompt=input_prompt, options=OLLAMA_OPTIONS
    )
    return response["response"]


def process_treatments(input_prompt: str) -> str:
    response = OLLAMA_CLIENT.generate(
        model=MODEL_NAME,
        prompt=input_prompt,
        options=OLLAMA_OPTIONS,
        format=TableResponse.model_json_schema(),
    )
    try:
        return TableResponse.model_validate_json(response["response"]).tables
    except Exception as e:
        return TableResponse(tables=[])


def process_body_part(input_prompt: str) -> str:
    response = OLLAMA_CLIENT.generate(
        model=MODEL_NAME,
        prompt=input_prompt,
        options=OLLAMA_OPTIONS,
        format=BodyPartResponse.model_json_schema(),
    )
    try:
        return BodyPartResponse.model_validate_json(response["response"]).body_parts
    except Exception as e:
        return BodyPartResponse(body_parts=[])


table_start_keywords = [
    "Diagnosis (Required)",
    "ICD-Code (Required)",
    "(Required)",
    "[Required)",
    "(if know)",
    "Diagnosis",
    "ICD-Code",
    "ICD",
    "ICD-",
    "iCD-Code",
    "Code",
    "ICD- Code",
    "CPT/HCPCS",
    "Service/Good Requested",
    "Other Informatlon",
    "(Frequency,",
    "Quantity,",
    " Quantity",
    "etc.)",
]

table_end_keywords = [
    "Request Physician Signature",
    "Physician Signature:",
    "Claims Administrator/Utilization",
    "Signature",
    "Special Instructions / Treatments:",
    "Supervising Provider Name:",
    "Requesting Physician Signature",
    "ReouestingPhysician Signature",
    "Provider Signatuj",
    "Please see attached PR-2",
    "Treating Physician Signature",
    "Supervising Provider",
    "Claims Administrator/Utilization Review Organization (URO) Response",
    "Clain Administrator",
    "Supervlsing Provider",
    "Special Instructions",
    "There are 1 request(s) on this form",
    "ReouesingPhysioanSgnaturo:",
    "For Align network claims",
    "For Align network claimis",
    "For Align network",
]

perfect_table_case = [
    "Diagnosis ",
    "ICD-Code ",
    "CPT/HCPCS",
    "Service/Good Requested",
    "Other Information",
]

ALLOWED_KEYS = {
    "diagnosisDescription",
    "diagnosisCode",
    "treatmentServiceRequested",
    "procedureCode",
    "otherInformation",
    "medicationFlag",
    "physicalTherapyFlag",
    "quantity",
    "frequency",
    "duration",
}


def key_definition(keys: str) -> str:
    categories = {
        "diagnosisDescription": {
            "Diagnosis",
            "Diagnosis(Required)",
            "Dlagnosls",
            "Diag nosis",
            "Diag",
        },
        "diagnosisCode": {
            "ICD",
            "ICD-Code",
            "ICD- Code",
            "ICD-",
            "Code",
            "1CD-Code",
            "iCD-Code",
        },
        "treatmentServiceRequested": {
            "Service",
            "Good",
            "Requested",
            "GoodRequested",
            "Service/Good",
            "Procedure Reguested",
            "Procedure",
            "Servce/GoodRequesled",
        },
        "procedureCode": {"CPT", "HCPCS", "CPT/HCPCS", "(lf", "known)", "Cpt/hcpcs"},
        "otherInformation": {
            "Other",
            "information",
            "Duration",
            "Quantity",
            "(Frequency",
            "etc.)",
            "etc",
            "Frequency",
        },
    }
    if isinstance(keys, list):
        keys = ",".join(keys)
    keys = keys.split()
    for key in keys:
        for category, terms in categories.items():
            if any(fuzz.ratio(term, key) > 85 for term in terms):
                return category

    return "otherInformation"


def detect_medication_flag(
    treatment_service_requested_data: str,
    other_information_data: str,
    procedure_code_data: str,
) -> str:
    patterns = [
        r"\b(Med Refill|Mg|Capsule|Refills|Tablet|Rx|Prescription|Injection|Pharmacy|NDC|\(National Drug Code\))\b",
        r"\b(J\d+|C\d+|9960[5-7]|9636[0-1]|9636[5-9]|9637[0-9]|9640[1-9]|964[1-9][0-9]|965[0-4][0-9]|\d{4}-\d{4}-\d{2})\b",
    ]
    if any(
        re.search(patterns[0], data, re.IGNORECASE)
        for data in (treatment_service_requested_data, other_information_data)
    ):
        return "Y"
    if re.search(patterns[1], procedure_code_data, re.IGNORECASE):
        return "Y"
    return "N"


def detect_physical_therapy_flag(data: str) -> bool:
    regex_patterns = [
        r"\s*physical\s+therapy\s*",
        r"(^|\s+)pt(\s+|$)",
        r"\s*work\s+hardening\s*",
        r"(^|\s+)pt\s*\/\s*po(\s+|$)",
    ]

    if any(re.search(pattern, data, re.IGNORECASE) for pattern in regex_patterns):
        return "Y"
    return "N"


def detect_frequency_and_duration(input_string):
    frequency_patterns = [
        r"(\d+)\s*x\s*(\d+)\s*weeks?",  # matches patterns like "3x4 weeks"
        r"(\d+)\s*x\s*week",  # matches patterns like "3x week"
        r"(\d+)\s*x\s*per\s*week",  # matches patterns like "3x per week"
        r"(\d+)\s*times?\s*a\s*week",  # matches patterns like "2 times a week"
        r"(\d+)\s*x\s*(\d+)\s*visits?",  # matches patterns like "2x4=8 visits"
        r"(\d+(-\d+)?)\s*grams?",  # matches patterns like "1-2 grams"
        r"(\d+)\s*night\s*",  # matches patterns like "2 night"
        r"(\d+)\s*tablet[s]?\s*(\d+)-(\d+)\s*times?\s*a\s*day",  # matches patterns like "1 tablet 2-3 times a day"
        r"(\d+)\s*times?\s*a\s*day",  # matches patterns like "2 times a day"
        r"(\d+)\s*times?\s*",  # matches patterns like "2 times"
    ]

    duration_patterns = [
        r"(\d+)\s*x\s*(\d+)\s*(weeks?|days?|nights?)?",  # matches patterns like "2x4 weeks"
        r"for\s*(\d+)\s*weeks?",  # matches patterns like "for 3 weeks"
        r"for\s*(\d+)\s*days?",  # matches patterns like "for 7 days"
        r"for\s*(\d+)\s*nights?",  # matches patterns like "for 2 nights"
        r"(\d+)-(\d+)\s*days",  # matches patterns like "30-45 days"
        r"(\d+)\s*visits?",  # matches patterns like "6 visits"
        r"(\d+)\s*nights?",  # matches patterns like "2 nights"
        r"(\d+)\s*days?",  # matches patterns like "25 days"
        r"(\d+)\s*weeks?",  # matches patterns like "3 weeks"
        r"(\d+)\s*times?",  # matches patterns like "3 times"
        r"x(\d+)",  # matches patterns like "x1"
    ]

    frequency_options = {
        "once": "1 time a day",
        "one time a day": "1 time a day",
        "1 time": "1 time a day",
        "twice": "2 times a day",
        "two times": "2 times a day",
        "2 times": "2 times a day",
        "thrice": "3 times a day",
        "three times": "3 times a day",
        "3 times": "3 times a day",
        "four times": "4 times a day",
        "4 times": "4 times a day",
        "five times": "5 times a day",
        "5 times": "5 times a day",
        "six times": "6 times a day",
        "6 times": "6 times a day",
        "seven times": "7 times a day",
        "7 times": "7 times a day",
        "eight times": "8 times a day",
        "8 times": "8 times a day",
        "nine times": "9 times a day",
        "9 times": "9 times a day",
    }

    result = {"frequency": None, "duration": None}
    try:
        for pattern in frequency_patterns:
            match = re.search(pattern, input_string, re.IGNORECASE)
            if match:
                if re.match(
                    r"(\d+)\s*x\s*(\d+)\s*(weeks?|days?|nights?)?", input_string
                ):
                    result["frequency"] = f"{match.group(1)} times a week"
                elif "x" in pattern and "visits" in pattern:
                    result["frequency"] = f"{match.group(1)} times a week"
                elif "x" in pattern:
                    result["frequency"] = f"{match.group(1)} times a week"
                elif "times" in pattern and "day" not in pattern:
                    result["frequency"] = f"{match.group(1)} times a week"
                elif "grams" in pattern:
                    frequency_string = ""
                    for freq_option in frequency_options:
                        if freq_option in input_string.lower():
                            frequency_string = frequency_options[freq_option]
                            break
                    result["frequency"] = (
                        f"{match.group(1)} grams {frequency_string}".strip()
                    )
                elif "tablet" in pattern:
                    result["frequency"] = (
                        f"{match.group(1)} tablet {match.group(2)}-{match.group(3)} times a day"
                    )
                elif "day" in pattern:
                    result["frequency"] = f"{match.group(1)} times a day"
                elif "night" in pattern:
                    result["duration"] = f"{match.group(1)} nights"
                elif "x" in pattern:
                    result["duration"] = f"{match.group(1)} weeks"
                else:
                    result["frequency"] = f"{match.group(1)}"
                break
    except Exception as e:
        pass

    try:
        for pattern in duration_patterns:
            match = re.search(pattern, input_string, re.IGNORECASE)
            if match:
                if re.match(
                    r"(\d+)\s*x\s*(\d+)\s*(weeks?|days?|nights?)?", input_string
                ):
                    if len(match.groups()) == 3 and match.group(3) is not None:
                        result["frequency"] = (
                            f"{match.group(1)} times a {match.group(3).lower()[:-1] if match.group(3)[-1] == 's' else match.group(3).lower()}"
                        )
                        result["duration"] = (
                            f"for {match.group(2)} {match.group(3).lower()}"
                        )
                    else:
                        result["frequency"] = f"{match.group(1)} times a week"
                        result["duration"] = f"for {match.group(2)} weeks"
                elif "days" in pattern and len(match.groups()) == 2:
                    result["duration"] = f"{match.group(1)}-{match.group(2)} days"
                elif "weeks" in pattern:
                    result["duration"] = f"for {match.group(1)} weeks"
                elif "visits" in pattern:
                    result["duration"] = f"{match.group(1)} visits"
                elif "nights" in pattern:
                    result["duration"] = f"{match.group(1)} nights"
                elif "x" in pattern:
                    result["duration"] = (
                        f"{match.group(1)} weeks" if match.group(1) != "1" else "1 week"
                    )
                elif "times" in pattern:
                    result["duration"] = f"{match.group(1)} times"
                else:
                    result["duration"] = f"for {match.group(1)} days"
                break
    except Exception as e:
        pass

    for key in result:
        if result[key] is None:
            result[key] = ""
    return result["frequency"], result["duration"]


def html_to_dict(html: BeautifulSoup) -> list:
    try:
        table_data, table_keys = [], []
        table_start = False
        table_without_header = False

        table = html.find_all("tr")
        # vertical table
        if fuzz.ratio(table[0].find_all("td")[0].get_text(), "Diagnosis:") > 80 and any(
            table_start_keywords[j] in table[i].find_all("td")[0].get_text()
            for i in range(1, len(table))
            for j in range(len(table_start_keywords))
        ):
            table_data.append({})
            for row in table:
                try:
                    key = key_definition(row.find_all("td")[0].get_text())
                    table_data[-1][key] = row.find_all("td")[1].get_text()
                except IndexError:
                    continue

        # horizontal table
        else:
            for count, row in enumerate(table):
                if (
                    any(case in row.get_text() for case in table_start_keywords)
                    and len(table_keys) == 0
                ):
                    table_start = True
                    for count, col in enumerate(row.find_all("td")):
                        key = col.get_text()
                        if not key:
                            if count == 0:
                                table_keys.append("Diagnosis:")
                            continue

                        table_keys.append(key)

                    continue
                if (
                    count == 0
                    and not table_start
                    and any(
                        word
                        in "requests on a separate sheet if the space below is insutticient"
                        for word in row.get_text().split()
                    )
                    is False
                ):
                    table_without_header = True

                if any(case in row.get_text() for case in table_end_keywords):
                    if len(row.get_text()) < 150:
                        break

                if table_start:
                    new_block = {}

                    for col, key in zip(row.find_all("td"), table_keys):
                        key = key_definition(key)
                        col_text = col.get_text()

                        if not col_text:
                            new_block[key] = ""
                        else:
                            new_block[key] = col_text

                    try:
                        if new_block["treatmentServiceRequested"] != "":
                            if (
                                len(table_data) > 0
                                and new_block["treatmentServiceRequested"][0].isupper()
                                is False
                            ):
                                for key in new_block:
                                    if not new_block[key]:
                                        continue
                                    table_data[-1][key] = ", ".join(
                                        [table_data[-1][key], new_block[key]]
                                    )
                            else:
                                table_data.append(new_block)
                        else:
                            if not table_data:
                                table_data.append(new_block)
                            for key in new_block:
                                if not new_block[key]:
                                    continue
                                table_data[-1][key] = ", ".join(
                                    [table_data[-1][key], new_block[key]]
                                )
                    except KeyError as e:
                        for key in new_block.keys():
                            if not new_block[key]:
                                continue
                            try:
                                table_data[-1][key] = ", ".join(
                                    [table_data[-1][key], new_block[key]]
                                )
                            except Exception as e:
                                continue

                if table_without_header:
                    new_block = {}

                    for col in row.find_all("td"):
                        col_text = col.get_text()
                        if not col_text:
                            continue
                        try:
                            key = json.loads(
                                process_with_ollama(
                                    TABLE_WITHOUT_HEADER_PROMPT.format(col_text)
                                )
                            )["category"]
                            new_block[key] = " ".join(
                                [
                                    word
                                    for word in col_text.split()
                                    if word not in table_start_keywords
                                ]
                            )
                        except Exception as e:
                            continue
                    table_data.append(new_block)
        if not table_data:
            return []

        # Filter table_data to include only allowed keys
        filtered_table_data = [
            {key: row.get(key, "") for key in ALLOWED_KEYS}
            for row in table_data
            if row != {}
        ]

        for data in filtered_table_data:
            if "quantity" not in data:
                data["quantity"] = ""

        for count, block in enumerate(filtered_table_data):
            if all([block[key] == "" for key in block]):
                filtered_table_data.pop(count)

        for data in filtered_table_data:
            data["medicationFlag"] = detect_medication_flag(
                data["treatmentServiceRequested"],
                data["otherInformation"],
                data["procedureCode"],
            )
            for key in [
                "treatmentServiceRequested",
                "otherInformation",
                "procedureCode",
            ]:
                data["physicalTherapyFlag"] = detect_physical_therapy_flag(data[key])
                if data["physicalTherapyFlag"] == "Y":
                    break
            for key in [
                "treatmentServiceRequested",
                "otherInformation",
                "procedureCode",
            ]:
                data["frequency"], data["duration"] = detect_frequency_and_duration(
                    data[key]
                )
                if data["frequency"] != "" or data["duration"] != "":
                    break
    except Exception as e:
        return []

    return filtered_table_data


def extract_rfa_treatments(splitted_document_file_stream: bytes) -> dict:
    pages = convert_pdf_to_pixmap(splitted_document_file_stream)
    table_end = False

    treatments_data = []
    valid_html, html = BeautifulSoup(), BeautifulSoup()
    for _, page in enumerate(pages):
        new_image = pix_to_image(page)  # np.array(pages[0], dtype=np.uint8)
        new_image = cv2.cvtColor(new_image, cv2.COLOR_RGB2BGR)

        ocr_results = custom_ocr(new_image)

        try:
            crop_coords_top = (
                int(
                    min(
                        bb[1][1]
                        for bb, text in zip(ocr_results[0], ocr_results[1])
                        if any(
                            fuzz.ratio(key, text[0]) > 90
                            for key in table_start_keywords
                        )
                    )
                )
                - 30
            )
        except ValueError:
            crop_coords_top = 0
        try:
            crop_coords_bot = int(
                min(
                    bb[1][1]
                    for bb, text in zip(ocr_results[0], ocr_results[1])
                    if any(fuzz.ratio(key, text[0]) > 90 for key in table_end_keywords)
                )
            )
        except ValueError:
            crop_coords_bot = len(new_image)
        new_image = new_image[crop_coords_top:crop_coords_bot, 0 : len(new_image)]

        html = BeautifulSoup()
        valid_html, table_end = None, False
        try:
            for i in range(4):
                if i == 0:
                    layout_results = layout_engine(new_image)

                    table_results = [
                        table_engine(result["img"])
                        for result in layout_results
                        if result["type"] in ["table", "figure"]
                    ]
                else:
                    for result in table_results:
                        for data in result:
                            html = BeautifulSoup(data["res"]["html"].replace("$", "S"))
                            for count, row in enumerate(html.find_all("tr")):
                                if any(
                                    case in row.get_text()
                                    for case in table_start_keywords
                                ):
                                    layout_results = layout_engine(data["img"])
                                    table_results = [
                                        table_engine(result_img["img"])
                                        for result_img in layout_results
                                        if result_img["type"] in ["table", "figure"]
                                    ]

            for result in table_results:
                if table_end:
                    break
                for data in result:
                    html = BeautifulSoup(data["res"]["html"].replace("$", "S"))

                    for count, row in enumerate(html.find_all("tr")):
                        if (
                            count == 0
                            and all(
                                case in row.get_text() for case in perfect_table_case
                            )
                            or any(
                                case in row.get_text() for case in table_start_keywords
                            )
                        ):
                            valid_html = html
                            treatments_data.extend(html_to_dict(valid_html))
                            table_end = True
                            break
                        if any(case in row.get_text() for case in table_end_keywords):
                            table_end = True

                            break

        except Exception as e:
            return []
        if table_end:
            continue
    return treatments_data


def get_text_from_ocr_results(result: list) -> str:
    all_strings = []
    for page in result:
        if not page:
            continue
        for string_data in page:
            string = string_data[-1][0]
            all_strings.append(string)

    text = " ".join(all_strings)

    return text


def extract_text_between_keywords(
    text: str,
    start_pattern: str = TREATMENTS_START_PATTERN,
    end_pattern: str = TREATMENTS_END_PATTERN,
) -> str:
    start = re.search(start_pattern, text)
    end = re.search(end_pattern, text)

    if not start and not end:
        return ""

    start_index = start.start() if start else 0
    end_index = end.end() if end else len(text)

    return text[start_index:end_index]


def check_if_table_is_empty(table: SingleTableResponse) -> bool:
    """
    Checks if the table is empty.

    Args:
        table (SingleTableResponse): The table result.

    Returns:
        bool: True if the table is empty, False otherwise.
    """

    if (
        not table.diagnosis
        and not table.icd_code
        and not table.service_good_requested
        and not table.cpt_hcpcs_code
        and not table.other_information
    ):
        return True
    return False


def convert_table_results_to_json(table_results: TableResponse) -> list[dict]:
    """
    Converts the table results to a list of dictionaries.

    Args:
        table_results (TableResponse): The table results.

    Returns:
        List[dict]: The table results in a list of dictionaries.
    """

    result = []
    physical_therapy_flag = "N"
    medication_flag = "N"

    try:
        for table in table_results:
            if check_if_table_is_empty(table):
                continue

            table_json = {
                "diagnosisDescription": table.diagnosis,
                "diagnosisCode": table.icd_code,
                "treatmentServiceRequested": table.service_good_requested,
                "procedureCode": table.cpt_hcpcs_code,
                "otherInformation": table.other_information,
                "bodyPart": table.body_part,
                "quantity": "",
            }

            if medication_flag == "N":
                table_json["medicationFlag"] = detect_medication_flag(
                    table.service_good_requested,
                    table.other_information,
                    table.cpt_hcpcs_code,
                )

            if physical_therapy_flag == "N":
                for element in [
                    table.service_good_requested,
                    table.other_information,
                    table.cpt_hcpcs_code,
                ]:
                    table_json["physicalTherapyFlag"] = detect_physical_therapy_flag(
                        element
                    )
                    if table_json["physicalTherapyFlag"] == "Y":
                        break

            table_json["frequency"], table_json["duration"] = (
                detect_frequency_and_duration(table.other_information)
            )

            result.append(table_json)

        return result
    except Exception as e:
        result.append([])

        return result


def extract_codes_from_text(text: str) -> dict:
    icd_pattern = re.compile(
        r"\b[A-Z]\d{2}(\.\d{1,4})([A-Za-z0-9]{1,3})?\b", re.IGNORECASE
    )
    matches = icd_pattern.finditer(text)
    icd_codes = []

    for match in matches:
        full_code = match.group(0)
        icd_codes.append(full_code)

    icd_codes = list(set(icd_code.upper() for icd_code in icd_codes if icd_code))

    procedure_codes = []

    for cpt_code in CPT_CODES:
        if cpt_code in text:
            procedure_codes.append(cpt_code)

    return {
        "icd_codes": icd_codes,
        "procedure_codes": procedure_codes,
    }


def merge_extra_cpt_codes(results: list, ocr_text: str) -> dict:
    extracted_codes = extract_codes_from_text(ocr_text)
    icd_codes = extracted_codes["icd_codes"]
    cpt_codes = extracted_codes["procedure_codes"]

    results_updated = copy.copy(results)
    if len(icd_codes) == 1:
        for cpt_code in cpt_codes:
            if not any(
                cpt_code in result["procedureCode"] for result in results_updated
            ):
                results_updated.append(
                    {
                        "diagnosisDescription": "",
                        "diagnosisCode": icd_codes[0],
                        "treatmentServiceRequested": "",
                        "procedureCode": cpt_code,
                        "otherInformation": "",
                        "bodyPart": "",
                        "medicationFlag": "N",
                        "physicalTherapyFlag": "N",
                        "quantity": "",
                        "frequency": "",
                        "duration": "",
                    }
                )
    elif len(icd_codes) > 1:
        for cpt_code in cpt_codes:
            if not any(
                cpt_code in result["procedureCode"] for result in results_updated
            ):
                results_updated.append(
                    {
                        "diagnosisDescription": "",
                        "diagnosisCode": ", ".join(icd_codes),
                        "treatmentServiceRequested": "",
                        "procedureCode": cpt_code,
                        "otherInformation": "",
                        "bodyPart": "",
                        "medicationFlag": "N",
                        "physicalTherapyFlag": "N",
                        "quantity": "",
                        "frequency": "",
                        "duration": "",
                    }
                )
    elif len(icd_codes) == 0:
        for cpt_code in cpt_codes:
            if not any(
                cpt_code in result["procedureCode"] for result in results_updated
            ):
                results_updated.append(
                    {
                        "diagnosisDescription": "",
                        "diagnosisCode": "",
                        "treatmentServiceRequested": "",
                        "procedureCode": cpt_code,
                        "otherInformation": "",
                        "bodyPart": "",
                        "medicationFlag": "N",
                        "physicalTherapyFlag": "N",
                        "quantity": "",
                        "frequency": "",
                        "duration": "",
                    }
                )

    return results_updated


def run_rfa_table_extraction(
    ocr_results: list, splitted_document_file_stream: bytes
) -> list:
    results = []

    ocr_text = get_text_from_ocr_results(ocr_results)
    table_results = extract_rfa_treatments(splitted_document_file_stream)

    if not table_results:
        treatments_text = extract_text_between_keywords(ocr_text)
        if not treatments_text:
            results = []
            results = merge_extra_cpt_codes(results, ocr_text)
            return results

        treatments_prompt = TABLE_PROMPT.format(table=treatments_text)
        treatments_results = process_treatments(treatments_prompt)
        json_treatments_results = convert_table_results_to_json(treatments_results)
        json_treatments_results = [
            result for result in json_treatments_results if result
        ]

        for table_result in json_treatments_results:
            if not table_result.get("bodyPart"):
                table_data_text = ""
                for key in table_result.keys():
                    if key != "bodyPart":
                        table_data_text += f"{key}: {table_result[key]}\n"
                table_data_text = table_data_text.strip()
                body_part_prompt = BODY_PART_PROMPT.format(table=table_data_text)
                body_part_results = process_body_part(body_part_prompt)
                if body_part_results and body_part_results[0].body_part:
                    table_result["bodyPart"] = body_part_results[0].body_part

        results = [result for result in json_treatments_results if result]

        results = merge_extra_cpt_codes(results, ocr_text)
        return results

    for table_result in table_results:
        table_dict = {
            "diagnosisDescription": table_result["diagnosisDescription"],
            "diagnosisCode": table_result["diagnosisCode"],
            "treatmentServiceRequested": table_result["treatmentServiceRequested"],
            "procedureCode": table_result["procedureCode"],
            "otherInformation": table_result["otherInformation"],
        }

        table_data_text = ""
        for key in table_dict.keys():
            table_data_text += f"{key}: {table_result[key]}\n"
        table_data_text = table_data_text.strip()
        body_part_prompt = BODY_PART_PROMPT.format(table=table_data_text)

        try:
            body_part_results = process_body_part(body_part_prompt)
            if body_part_results and body_part_results[0].body_part:
                table_result["bodyPart"] = body_part_results[0].body_part
            else:
                table_result["bodyPart"] = ""
        except Exception as e:
            table_result["bodyPart"] = ""

    results = [result for result in table_results if result]

    results = merge_extra_cpt_codes(results, ocr_text)
    return results
