from pydantic import BaseModel, Field


class AddressLine(BaseModel):
    street: str = Field(required=False)
    space: str = Field(required=False)
    city: str = Field(required=False)
    state: str = Field(required=False)
    zip_code: str = Field(required=False)


class Claimant(BaseModel):
    first_name: str = Field(required=False)
    last_name: str = Field(required=False)
    middle_name: str = Field(required=False)
    suffix: str = Field(required=False)
    gender: str = Field(required=False)
    claim_number: str = Field(required=False)
    date_of_birth: str = Field(required=False)
    date_of_injury: str = Field(required=False)
    ssn: str = Field(required=False)
    address: AddressLine = Field(required=False)
    phone_number: str = Field(required=False)
    phone_ext: str = Field(required=False)
    email_address: str = Field(required=False)
    place_of_work: str = Field(required=False)


class Physician(BaseModel):
    first_name: str = Field(required=False)
    last_name: str = Field(required=False)
    credentials: str = Field(required=False)
    facility_name: str = Field(required=False)
    address: AddressLine = Field(required=False)
    phone_number: str = Field(required=False)
    phone_ext: str = Field(required=False)
    fax_number: str = Field(required=False)
    key_contact_name: str = Field(required=False)
    email_address: str = Field(required=False)
    physician_type: str = Field(required=False)


class Adjuster(BaseModel):
    company: str = Field(required=False)
    contact_name: str = Field(required=False)
    first_name: str = Field(required=False)
    last_name: str = Field(required=False)
    address: AddressLine = Field(required=False)
    phone_number: str = Field(required=False)
    phone_ext: str = Field(required=False)
    fax_number: str = Field(required=False)
    email_address: str = Field(required=False)


class Metadata(BaseModel):
    physician: Physician
    claimant: Claimant
    adjuster: Adjuster
    doc_date: str = Field(required=False)
