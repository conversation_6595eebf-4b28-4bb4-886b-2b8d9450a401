def initialize_qa_results():
    return {
        "claim": {
            "claimNumber": None,
            "dateOfInjuryFrom": None,
            "dateOfInjuryThrough": None,
        },
        "claimant": {
            "claimantName": None,
            "dateOfBirth": None,
        },
        "physician": {
            "state": None,
            "facilityName": None,
            "physicianName": None,
            "npi": None,
            "keyContactName": None,
            "specialty": None,
            "emailAddress": None,
            "phoneNumber": None,
            "faxNumber": None,
            "address": None,
            "city": None,
            "zip": None,
        },
        "employer": {
            "name": None,
        },
        "adjuster": {
            "company": None,
            "address": None,
            "city": None,
            "state": None,
            "zip": None,
            "phoneNumber": None,
            "faxNumber": None,
            "emailAddress": None,
        },
        "documentDate": None,
        "receiptDate": None,
        "senderName": None,
        "rushFlag": None,
    }


def transform_qa_results(input_data):
    transformed_data = {
        "namingData": {
            "claimNumber": input_data["claim"].get("claimNumber", ""),
            "docReceivedDate": input_data.get("receiptDate", ""),
            "patientFirstName": input_data["claimant"].get("firstName", ""),
            "patientMiddleName": input_data["claimant"].get("middleName", ""),
            "patientLastName": input_data["claimant"].get("lastName", ""),
            "senderName": input_data.get("senderName", ""),
            "docDate": input_data.get("documentDate", ""),
            "docState": input_data["physician"].get("state", ""),
            "providerOrFacilityName": input_data["physician"].get("facilityName", ""),
        },
        "docName": "",
        "docLink": "",
        "docType": "RFA",
        "docConfidence": None,
        "pageRefStart": None,
        "pageRefEnd": None,
        "pagesRef": None,
        "pagesRefOrig": None,
        "claimNumber": input_data["claim"].get("claimNumber", ""),
        "patientName": input_data["claimant"].get("patientName", ""),
        "metaData": {
            "docDate": input_data.get("documentDate", ""),
            "docReceivedDate": input_data.get("receiptDate", ""),
            "expeditedFlag": None,
            "rushFlag": input_data.get("rushFlag", None),
            "jurisdiction": input_data["physician"].get("state", ""),
            "senderName": input_data.get("senderName", ""),
            "newSubmission": None,
            "resubmission": None,
            "writtenConfirmPriorOralRequest": None,
            "isEnglishLanguage": None,
            "isHandwritten": None,
            "physician": {
                "type": "",
                "npi": input_data["physician"].get("npi", ""),
                "firstName": input_data["physician"].get("firstName", ""),
                "lastName": input_data["physician"].get("lastName", ""),
                "physicianName": f"{input_data['physician'].get('firstName', '')} {input_data['physician'].get('lastName', '')}".strip(),
                "facilityName": input_data["physician"].get("facilityName", ""),
                "keyContactName": input_data["physician"].get("keyContactName", ""),
                "specialty": input_data["physician"].get("specialty", ""),
                "credentials": input_data["physician"].get("credentials", ""),
                "emailAddress": input_data["physician"].get("emailAddress", ""),
                "phoneNumber": input_data["physician"].get("phoneNumber", ""),
                "faxNumber": input_data["physician"].get("faxNumber", ""),
                "address": {
                    "address1": input_data["physician"].get("address", ""),
                    "address2": input_data["physician"].get("space", ""),
                    "city": input_data["physician"].get("city", ""),
                    "state": input_data["physician"].get("state", ""),
                    "zip": input_data["physician"].get("zip", ""),
                },
            },
            "adjuster": {
                "adjusterId": "",
                "company": input_data["adjuster"].get("company", ""),
                "keyContact": input_data["adjuster"].get("adjusterName", ""),
                "firstName": input_data["adjuster"].get("firstName", ""),
                "lastName": input_data["adjuster"].get("lastName", ""),
                "middleName": input_data["adjuster"].get("middleName", ""),
                "suffix": input_data["adjuster"].get("suffix", ""),
                "address": {
                    "address1": input_data["adjuster"].get("address", ""),
                    "address2": input_data["adjuster"].get("space", ""),
                    "city": input_data["adjuster"].get("city", ""),
                    "state": input_data["adjuster"].get("state", ""),
                    "zip": input_data["adjuster"].get("zip", ""),
                },
                "phoneNumber": input_data["adjuster"].get("phoneNumber", ""),
                "phoneExt": input_data["adjuster"].get("phoneExt", ""),
                "faxNumber": input_data["adjuster"].get("faxNumber", ""),
                "emailAddress": input_data["adjuster"].get("emailAddress", ""),
            },
            "attorney": {
                "type": "",
                "company": "",
                "firstName": "",
                "lastName": "",
                "middleName": "",
                "suffix": "",
                "address": {
                    "address1": "",
                    "address2": "",
                    "city": "",
                    "state": "",
                    "zip": "",
                },
                "phoneNumber": "",
                "phoneExt": "",
                "faxNumber": "",
                "emailAddress": "",
            },
            "employer": {
                "name": input_data["employer"].get("name", ""),
                "phoneNumber": "",
                "faxNumber": "",
                "contactEmail": "",
                "taxId": "",
                "address": {
                    "address1": "",
                    "address2": "",
                    "city": "",
                    "state": "",
                    "zip": "",
                },
            },
            "claim": {
                "claimNumber": input_data["claim"].get("claimNumber"),
                "dateOfInjuryFrom": input_data["claim"].get("dateOfInjuryFrom"),
                "dateOfInjuryThrough": input_data["claim"].get("dateOfInjuryThrough"),
                "jurisState": input_data["physician"].get("state", ""),
            },
            "claimant": {
                "firstName": input_data["claimant"].get("firstName", ""),
                "lastName": input_data["claimant"].get("lastName", ""),
                "middleName": input_data["claimant"].get("middleName", ""),
                "claimantName": input_data["claimant"].get("claimantName", ""),
                "suffix": "",
                "dateOfBirth": input_data["claimant"].get("dateOfBirth", ""),
                "gender": None,
                "ssn": "",
                "address": {
                    "address1": "",
                    "address2": "",
                    "city": "",
                    "state": "",
                    "zip": "",
                },
                "phoneNumber": "",
                "emailAddress": "",
                "employeeId": "",
            },
            "treatments": [],
        },
    }
    return transformed_data


categorized_questions: dict[str, dict[str, str]] = {
    "documentDate": {
        "Find human name if it exists??": "senderName",
    },
    "physician": {
        "What is Name:?": "physicianName",
        "What is Practice Name:?": "facilityName",
        "What is Contact Name:?": "keyContactName",
        "What is Address:?": "address",
        "What is City:?": "city",
        "What is State:?": "state",
        "What is Zip Code:?": "zip",
        "What is Phone: ?": "phoneNumber",
        "What is Fax Number: ?": "faxNumber",
        "What is Specialty: ?": "specialty",
        "What is NPI Number: ?": "npi",
        "What is E-mail Address: if it present?": "emailAddress",
    },
    "adjuster": {
        "What is Company Name:?": "company",
        "what is Contact Name:?": "adjusterName",
        "What is City:?": "city",
        "What is Address:?": "address",
        "What is State:?": "state",
        "What is Zip Code:?": "zip",
        "What is Phone:?": "phoneNumber",
        "What is Fax Number:?": "faxNumber",
        "What is E-mail Address: if it present?": "emailAddress",
    },
    "claimant": {
        "What is Name (Last, First, Middle):?": "claimantName",
        "What is Date of Birth (MM/DD/YYYY):?": "dateOfBirth",
    },
    "employer": {"What is Employer:?": "name"},
    "claim": {
        "What is Claim Number:?": "claimNumber",
        "What is Date of Injury (MM/DD/YYYY): or DOI?": "dateOfInjuryFrom",
    },
}

physician_specialty_list = [
    "Family Medicine",
    "Internal Medicine",
    "Pediatrics",
    "OB/GYN",
    "Neurology",
    "Psychiatry",
    "Plastic Surgery",
    "Otolaryngology",
    "Urology",
    "Anesthesiology",
    "Radiology",
    "Pathology",
    "Emergency Medicine",
    "Critical Care",
    "Preventive Medicine",
    "Physical Medicine and Rehabilitation",
    "Orthopedics",
    "Ophthalmology",
    "Dermatology",
    "Gastroenterology",
    "Pulmonology",
    "Hematology",
    "Oncology",
    "Rheumatology",
    "Nephrology",
    "Infectious Diseases",
    "Allergy/Immunology",
    "Trauma Surgery",
    "Cardiothoracic Surgery",
    "Vascular Surgery",
    "Gender Surgery",
    "Interventional Cardiology",
    "Reproductive Endocrinology",
    "Neonatology",
    "Pediatric Intensivist",
    "Podiatry",
    "Sports Medicine Doctor",
    "Military Doctor",
    "Global Health Doctor",
    "Pain management",
    "Orthopaedic surgery",
    "Surgery",
    "Cardiology",
    "Endocrinology",
]
