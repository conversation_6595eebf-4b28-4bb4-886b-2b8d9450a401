import base64
import os

from dotenv import load_dotenv
from ollama import Client

load_dotenv()

# BASE_AUTH_USER_AND_PASSWORD = os.getenv("BASE_AUTH_USER_AND_PASSWORD")
SERVER_HOST = os.getenv("SERVER_HOST")
SERVER_PORT = os.getenv("SERVER_PORT")

# BASE64_STRING = base64.b64encode(BASE_AUTH_USER_AND_PASSWORD.encode()).decode("ascii")
# AUTH_HEADER = {"Authorization": f"Basic {BASE64_STRING}"}

OLLAMA_CLIENT = Client(
    host=f"{SERVER_HOST}:{SERVER_PORT}",
    # headers=AUTH_HEADER,
    timeout=360.0,
    verify=False,
)

OLLAMA_OPTIONS = {
    "num_predict": 500,
    "temperature": 0,
}

MODEL_NAME = "mistral-nemo:12b-instruct-2407-q5_K_<PERSON>_medical_assistant"


def process_with_ollama(input_prompt: str) -> str:
    """
    Processes the input prompt using the Ollama client and returns the response.

    Parameters
    ----------
    input_prompt : str
        The input prompt to be processed by the Ollama client.

    Returns
    -------
    str
        The response from the Ollama client with newline characters removed.
    """
    response = OLLAMA_CLIENT.generate(
        model=MODEL_NAME, prompt=input_prompt, options=OLLAMA_OPTIONS
    )
    cleaned_response = response["response"].replace("\n", " ")
    return cleaned_response


def chat_with_ollama(messages, format, options):
    response = OLLAMA_CLIENT.chat(
        model=MODEL_NAME, messages=messages, format=format, options=options
    )
    return response
