docker run \
  --network host \
  -v /home/<USER>/dx_record_ranger_ml_pipeline/config.yml:/app/config.yml \
  -v /home/<USER>/dx_record_ranger_ml_pipeline/models/:/models/  \
  -v ./metadata_extractor.py:/app/metadata_extractor.py \
  -v ./data/paddleocr/:/root/.paddleocr/ \
  -v ./data/nltk_data/:/root/nltk_data/ \
  -v ./rfa_expedited_model.pt:/app/rfa_expedited_model.pt \
  -v ./cmn.py:/app/cmn.py \
  -v ./fax_misc.py:/app/fax_misc.py \
  -v ./medauth_imr_ime_qme_supplemental_work_status_froi_medical_records.py:/app/medauth_imr_ime_qme_supplemental_work_status_froi_medical_records.py \
  -v ./rfa_checkbox.py:/app/rfa_checkbox.py \
  -v ./rfa_expedited_checkbox_model.pt:/app/rfa_expedited_checkbox_model.pt \
  -v ./rfa.py:/app/rfa.py \
  -v ./rfa_table_extraction.py:/app/rfa_table_extraction.py \
  -v ./claim_number_and_patient_name_extraction.py:/app/claim_number_and_patient_name_extraction.py \
  -v ./rfa_formatter.py:/app/rfa_formatter.py \
  -v ./naming_prompts.py:/app/naming_prompts.py \
  -v ./naming_fields_extraction.py:/app/naming_fields_extraction.py \
  -v ./naming_constants.py:/app/naming_constants.py \
  -v ./utils.py:/app/utils.py \
  -v ./.env:/app/.env \
  -v ./naming_fields_extraction.py:/app/naming_fields_extraction.py \
  -v ./naming_prompts.py:/app/naming_prompts.py \
  -v ./required_fields.py:/app/required_fields.py \
  -v ./context_functions.py:/app/context_functions.py \
  -v ./patterns.py:/app/patterns.py \
  -v ./postprocess.py:/app/postprocess.py \
  -v ./prompts.py:/app/prompts.py \
  -v ./rfa_stamp_detection.py:/app/rfa_stamp_detection.py \
  -v ./stamp_is_rush_model.pt:/app/stamp_is_rush_model.pt \
  -v -it --gpus all dx_metadata_extractor
