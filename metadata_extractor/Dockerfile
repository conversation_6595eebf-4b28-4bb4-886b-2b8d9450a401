FROM 112623991000.dkr.ecr.us-east-2.amazonaws.com/paddle-gpu:2.6.1-gpu-cuda11.7-cudnn8.4-trt8.4

ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && apt-get install -y poppler-utils

WORKDIR /app

COPY metadata_extractor/requirements.txt /app/requirements.txt

RUN python3 -m pip install -r requirements.txt
RUN python3 -m spacy download en_core_web_sm

COPY metadata_extractor /app/
COPY pipeline_utils /app/pipeline_utils/
COPY models /app/models/


ENV PYTHONPATH=/app:$PYTHONPATH

CMD ["python3", "-u", "metadata_extractor.py"]
