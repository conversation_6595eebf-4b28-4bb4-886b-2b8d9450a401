import numpy as np
from paddleocr import PaddleOCR
from utils_ import convert_pdf_to_pixmap, pix_to_image

CUSTOM_OCR = PaddleOCR(use_angle_cls=True, lang="en", show_log=False, use_gpu=True, cls=True)

SUBJECT_LINE_KEYWORDS = ["Subject", "Subject Line", "Subject", "Subject:"]

BODY_KEYWORDS = [
    "Message",
    "Comments",
    "Comment",
    "Conments",
    "Note",
    "NOTE",
    "NOTES",
    "Fax Message",
    "Body",
    "Attachments",
]

BODY_END_KEYWORDS = [
    "Confidentiality Notice:",
    "CONFIDENTIALITY NOTE",
    " CONFIDENTIALITY NOTICE",
    "The information contained",
    "This document may contain",
]


def run_subject_line_and_body_extraction(file_stream):

    meta_data = {"subjectLine": "", "body": ""}

    pages = convert_pdf_to_pixmap(file_stream)

    page_image = pix_to_image(pages[0])
    ocr_results = CUSTOM_OCR(page_image)

    # SubjectLine

    # if subject text in ocr results
    for text in ocr_results[1]:
        for keyword in SUBJECT_LINE_KEYWORDS:
            if keyword in text[0]:
                subject_text = text[0].replace(keyword, "").replace(":", "")
                if len(subject_text) > 1:
                    meta_data["subjectLine"] = subject_text

    # if the text is on the right side
    try:
        subject_line_coords = [
            boundary_box
            for boundary_box, detected_word in zip(ocr_results[0], ocr_results[1])
            if any(keyword in detected_word[0] for keyword in SUBJECT_LINE_KEYWORDS)
        ][0]
        distances = []
        for boundary_box, detected_word in zip(ocr_results[0], ocr_results[1]):
            distance = np.sqrt(np.sum(boundary_box[[0, 1, 2, 3], 1] - subject_line_coords[[0, 1, 2, 3], 1]) ** 2)
            distances.append(1000 if np.array_equal(boundary_box, subject_line_coords) else distance)
        if distances and min(distances) < 100:
            min_index = distances.index(min(distances))
            meta_data["subjectLine"] = ocr_results[1][min_index][0]
    except IndexError:
        ...

    # Body
    # if subject text in ocr results
    for text in ocr_results[1]:
        for keyword in BODY_KEYWORDS:
            if keyword in text[0]:
                body_text = text[0].replace(keyword, "").replace(":", "")
                if len(body_text) > 1:
                    meta_data["body"] = body_text
    # if the text is on the right side
    try:
        if any(keyword in meta_data["subjectLine"] for keyword in BODY_KEYWORDS):
            raise IndexError
        body_coords = [
            boundary_box
            for boundary_box, detected_word in zip(ocr_results[0], ocr_results[1])
            if any(keyword in detected_word[0] for keyword in BODY_KEYWORDS)
        ][0]
        distances = []
        for boundary_box, detected_word in zip(ocr_results[0], ocr_results[1]):
            distance = np.sqrt(np.sum(boundary_box[[0, 1, 2, 3], 1] - body_coords[[0, 1, 2, 3], 1]) ** 2)
            distances.append(1000 if np.array_equal(boundary_box, body_coords) else distance)
        indexes = sorted((distances.index(distance)) for distance in distances if distance < 260)
        for index in indexes:
            if (
                any([keyword in ocr_results[1][index][0] for keyword in SUBJECT_LINE_KEYWORDS])
                or ocr_results[1][index][0] in meta_data["subjectLine"]
            ):
                continue
            meta_data["body"] = " ".join([meta_data["body"], ocr_results[1][index][0]])
    except IndexError:
        ...
    # if the text is below
    if meta_data["body"] == "" and any(keyword in text[0] for text in ocr_results[1] for keyword in BODY_KEYWORDS):
        try:
            for boundary_box, detected_word in zip(ocr_results[0], ocr_results[1]):
                if boundary_box[0][1] > (0.9 * len(page_image)):
                    continue
                if boundary_box[3][1] > body_coords[3][1]:
                    if any(keyword in detected_word[0] for keyword in BODY_END_KEYWORDS):
                        break
                    meta_data["body"] = " ".join([meta_data["body"], detected_word[0]])
        except UnboundLocalError:
            ...

    return meta_data
