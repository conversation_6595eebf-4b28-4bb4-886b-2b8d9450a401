### fix of C++ Segmentation fault
from pipeline_utils.PaddleOCR_checker import ensure_numpy
ensure_numpy()

import paddle

from pipeline_utils.rabbitmq_connector import PikaServiceFactory
from pipeline_utils.database_connector import DBConnector

paddle.utils.run_check()
### end of fix of C++ Segmentation fault

import json
import os
import sys
import traceback
from datetime import datetime, timezone
from io import BytesIO
from pprint import pprint

import fitz
import torch
import yaml
from minio import Minio
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.orm.attributes import flag_modified

### external modules import
from naming.naming_fields_extraction import extract_naming_fields
from PIL import Image
from rapidfuzz import fuzz
from rfa.handwritten_model import classify_handwriting
from rfa.language_metadata import classify_language
from rfa.non_ca_rfa_extractor import extract_metadata_from_texas_rfa
from rfa.rfa import extract_metadata_from_rfa
from fax.subject_line_and_body_extraction import run_subject_line_and_body_extraction
from summary.summary_extraction import run_summary_extraction
from hcfa.hcfa import extract_metadata_from_physician_bill
from ub.ub import extract_metadata_from_hospital_bill

### end of external modules import

with open("keywords_config.yaml") as yaml_file:
    californian_keywords = yaml.safe_load(yaml_file)["ca_keywords"]

current_dir = os.path.dirname(__file__)
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import Document, Splitted_Document
from pipeline_utils import JSONFormat, fill_mapped_fields
from pipeline_utils.monitoring import MonitorService, Status
from pipeline_utils.tenant_utils import get_tenant_processing_config, default_tenant_config


MINIO_URI = os.environ.get("MINIO_URI")
MINIO_ACCESS_KEY = os.environ.get("MINIO_ACCESS_KEY")
MINIO_SECRET_KEY = os.environ.get("MINIO_SECRET_KEY")
MINIO_FILES_BUCKET = os.environ.get("MINIO_FILES_BUCKET")
MINIO_SECURE = os.environ.get('MINIO_SECURE', 'false').lower() == 'true'


RABBITMQ_HOST = os.environ.get("RABBITMQ_HOST")
RABBITMQ_PORT = os.environ.get("RABBITMQ_PORT")
RABBITMQ_USERNAME = os.environ.get("RABBITMQ_USERNAME")
RABBITMQ_PASSWORD = os.environ.get("RABBITMQ_PASSWORD")
RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME = os.environ.get("RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME")
RABBITMQ_TO_VALIDATE_QUEUE_NAME = os.environ.get("RABBITMQ_TO_VALIDATE_QUEUE_NAME")
    
RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME = os.environ.get("RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME")

PGSQL_HOST = os.environ.get("PGSQL_HOST")
PGSQL_PORT = os.environ.get("PGSQL_PORT")
PGSQL_USERNAME = os.environ.get("PGSQL_USERNAME")
PGSQL_PASSWORD = os.environ.get("PGSQL_PASSWORD")
PGSQL_DB_NAME = os.environ.get("PGSQL_DB_NAME")

# API_HOST = os.environ.get("MONITOR_HOST")
# API_PORT = os.environ.get("MONITOR_PORT")
PROJECT_NAME = os.environ.get("PROJECT_NAME")
APP_NAME = os.environ.get("APP_NAME")

Base = declarative_base()

Base = declarative_base()

db_connector = DBConnector(
    pgsql_host=PGSQL_HOST,
    pgsql_port=PGSQL_PORT,
    pgsql_username=PGSQL_USERNAME,
    pgsql_password=PGSQL_PASSWORD,
    pgsql_db_name=PGSQL_DB_NAME
)
session = None

rmq_service_factory = PikaServiceFactory(
    host=RABBITMQ_HOST,
    port=RABBITMQ_PORT,
    username=RABBITMQ_USERNAME,
    password=RABBITMQ_PASSWORD,
    ssl_options=None,
    heartbeat=3600,
)
rmq_service = rmq_service_factory.create_service()
rmq_service.start()

minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE,
)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"using device: {device}")


def split_pdf_into_images(file_stream):
    """Split pdf file into pages (images)

    Args:
        file_stream (bytes): pdf file stream.

    Returns:
        pages (list): list of pages.
    """

    mat = fitz.Matrix(300 / 72, 300 / 72)
    doc = fitz.open(stream=file_stream, filetype="pdf")

    print(f"doc len: {len(doc)}")

    pages = []
    for page in doc:
        pix = page.get_pixmap(matrix=mat)
        img_bytes = pix.tobytes("ppm")
        image = Image.open(BytesIO(img_bytes))
        pages.append(image)
    return pages


def save_metadata_to_db(metadata_extracted, splitted_document, parent_document):
    splitted_document.status = "to_metadata_postprocess"
    splitted_document.metadata_ml = metadata_extracted
    splitted_document.metadata_ml_time = datetime.now(timezone.utc)
    parent_document.status = "to_metadata_postprocess"
    flag_modified(splitted_document, "metadata_ml")
    flag_modified(splitted_document, "metadata_ml_time")
    flag_modified(splitted_document, "status")
    flag_modified(parent_document, "metadata_ml")
    flag_modified(parent_document, "metadata_ml_time")
    flag_modified(parent_document, "status")


required_fields = ["physicianName", "claimantName", "claimNumber"]


def expand_metadata(json_obj, cls, required_keys_dict):
    """
    Expands the metadata in a JSON object by adding additional fields such as 'value', 'confidence', 'valid', and 'required'.

    Args:
        json_obj (dict): The JSON object containing the metadata to be expanded.
        cls (str): The class name used to determine the required keys for the metadata.
        required_keys_dict (dict): A dictionary containing required keys for different classes. The keys are class names and the values are dictionaries defining required keys.

    Returns:
        dict: A new dictionary with expanded metadata.

    Steps:
        1. Define a helper function 'is_required' to check if a key path is required based on the required keys dictionary.
        2. Define a helper function 'process_value' to recursively process the values in the JSON object, expanding them with additional fields.
        3. Determine the required keys for the given class name ('cls') from the required keys dictionary.
        4. If no required keys are found for the given class, use the default required keys.
        5. Process the JSON object using the 'process_value' function, expanding each value.
        6. If an exception occurs, print the exception details and return an empty dictionary.

    Notes:
        - The 'is_required' function checks if a key path is required by traversing the required keys dictionary.
        - The 'process_value' function recursively processes each value in the JSON object, expanding it with additional fields.
        - The expanded fields include 'value', 'confidence', 'valid', and 'required'.
        - If the value is already expanded (contains 'value', 'confidence', and 'valid' keys), it is returned as is.
        - The 'required' field is determined based on the required keys for the given class.
    """

    def is_required(key_path, required_keys):
        current_level = required_keys
        for key in key_path[:-1]:
            if isinstance(current_level, dict) and key in current_level:
                current_level = current_level[key]
            else:
                return False
        return key_path[-1] in current_level

    def process_value(key_path, value, cls, required_keys_dict):
        if isinstance(value, dict):
            # Check if the data is already expanded
            if all(k in value for k in ["value", "confidence", "valid"]):
                return value
            return {
                k: process_value(key_path + [k], v, cls, required_keys_dict)
                for k, v in value.items()
            }
        elif isinstance(value, list):
            return [process_value(key_path, v, cls, required_keys_dict) for v in value]
        else:
            required_keys = None
            for key, req_keys in required_keys_dict.items():
                if key.lower() == cls.lower():
                    required_keys = req_keys
                    break
            if required_keys is None:
                required_keys = required_keys_dict.get("default", {})
            required = is_required(key_path, required_keys)
            return {
                "value": value,
                "confidence": None,
                "valid": None,
                "required": required,
            }

    required_keys = None
    for key, req_keys in required_keys_dict.items():
        if key.lower() == cls.lower():
            required_keys = req_keys
            break
    if required_keys is None:
        required_keys = required_keys_dict.get("default", {})

    try:
        return {
            key: process_value([key], value, cls, required_keys_dict)
            for key, value in json_obj.items()
        }
    except Exception as e:
        print(".....exception.....")
        print(len(json_obj))
        print(json_obj)
        print(traceback.format_exc())
        return {}


def get_tenant_extraction_config(tenant_id=None, subtenant_id=None):
    """
    Get tenant-specific metadata extraction configuration.
    
    Args:
        tenant_id: Tenant ID
        subtenant_id: Subtenant ID
    
    Returns:
        Dictionary containing extraction configuration
    """
    if not tenant_id:
        return {
            'enable_language_detection': True,
            'enable_handwriting_detection': True,
            'enable_summary_extraction': True,
            'custom_required_fields': {},
            'extraction_strategies': []
        }
    
    try:
        processing_config = get_tenant_processing_config(tenant_id, subtenant_id)
        tenant_config = default_tenant_config.get_tenant_config(tenant_id, subtenant_id)
        
        metadata_config = tenant_config.get('metadata_extraction', {})
        
        return {
            'enable_language_detection': processing_config.get('enable_language_detection', True),
            'enable_handwriting_detection': processing_config.get('enable_handwriting_detection', True),
            'enable_summary_extraction': metadata_config.get('enable_summary_extraction', True),
            'custom_required_fields': metadata_config.get('required_fields', {}),
            'extraction_strategies': metadata_config.get('extraction_strategies', [])
        }
    except Exception as e:
        print(f"Error getting tenant extraction config: {e}")
        return {
            'enable_language_detection': True,
            'enable_handwriting_detection': True,
            'enable_summary_extraction': True,
            'custom_required_fields': {},
            'extraction_strategies': []
        }


def extract_metadata(splitted_document, document_type, splitted_document_file_stream, tenant_id=None, subtenant_id=None):
    """
    Extract metadata from a splitted document with tenant-specific configurations.
    
    Args:
        splitted_document: The splitted document object
        document_type: Type of the document
        splitted_document_file_stream: File stream of the document
        tenant_id: Tenant ID for tenant-specific processing
        subtenant_id: Subtenant ID for tenant-specific processing
    
    Returns:
        Extracted metadata dictionary
    """
    ocred_text = splitted_document.text_ocr
    metadata = JSONFormat.GetFieldsForDocument(splitted_document.document_type)
    print("\n\n\nfrom JSONFormat empty metadata dict received:")
    pprint(metadata)

    # Get tenant-specific extraction configuration
    extraction_config = get_tenant_extraction_config(tenant_id, subtenant_id)
    
    print(f"Using tenant-specific extraction config for tenant {tenant_id}: {extraction_config}")

    try:
        if document_type.lower() == "rfa":
            californian_rfa = False
            ocred_text_lower = str(ocred_text).lower()
            for variation in californian_keywords:
                if fuzz.partial_ratio(variation, ocred_text_lower) >= 80:
                    californian_rfa = True
            if californian_rfa:
                data_extracted = extract_metadata_from_rfa(
                    ocred_text, splitted_document_file_stream
                )
            else:
                data_extracted = extract_metadata_from_texas_rfa(
                    ocred_text, splitted_document_file_stream
                )
            metadata.update(data_extracted)
        elif document_type == "Physician Bill (HCFA)":
            extracted_data = extract_metadata_from_physician_bill(
                ocred_text, splitted_document_file_stream
            )
            metadata.update(extracted_data)
        elif document_type == "Hospital Bill (UB)":
            extracted_data = extract_metadata_from_hospital_bill(
                ocred_text, splitted_document_file_stream
            )
            metadata.update(extracted_data)
        else:
            data_ = extract_naming_fields(ocred_text, document_type)
            metadata["namingData"].update((k, v) for k, v in data_.items() if v)

        if document_type == "Fax":
            subject_line_and_body = run_subject_line_and_body_extraction(
                splitted_document_file_stream
            )
            metadata["metaData"].update(subject_line_and_body)

        # Apply tenant-specific summary extraction settings
        try:
            if document_type in ["Medical Records", "Supplemental/Work Status"] and extraction_config['enable_summary_extraction']:
                summary = run_summary_extraction(
                    ocred_text, document_type, splitted_document_file_stream
                )
                metadata["metaData"].update(summary)
        except SystemExit as e:
            print(traceback.format_exc())
            print(f"Error during summary extraction operation: {e}")

        # Apply tenant-specific language and handwriting detection
        language_detection_result = "N"
        handwriting_detection_result = "N"
        
        if extraction_config['enable_language_detection']:
            language_detection_result = "Y" if classify_language(splitted_document_file_stream) else "N"
        
        if extraction_config['enable_handwriting_detection']:
            handwriting_detection_result = "Y" if classify_handwriting(splitted_document_file_stream) else "N"

        metadata["metaData"].update({
            "isEnglishLanguage": language_detection_result, 
            "isHandwritten": handwriting_detection_result
        })
        
        # Apply custom required fields if specified by tenant
        custom_fields = extraction_config.get('custom_required_fields', {})
        if custom_fields:
            print(f"Applying custom required fields for tenant {tenant_id}: {custom_fields}")
            # This could be extended to modify the JSONFormat required fields per tenant
            
    except Exception:
        print(
            f"Unable to extract metadata from the splitted document with type: {document_type} and this id: {splitted_document.uuid}. This document can be reviewed in minio bucket."
        )
        print(traceback.format_exc())

    print("extracted metadata with synced mapped fields:")
    fill_mapped_fields(metadata)
    pprint(metadata)

    metadata.update({"pagesRef": splitted_document.parent_document_pages})

    if "metaData" in metadata:
        metadata["metaData"] = expand_metadata(
            metadata["metaData"],
            document_type.lower(),
            JSONFormat.metadata_required_fileds,
        )
    if "namingData" in metadata:
        metadata["namingData"] = expand_metadata(
            metadata["namingData"],
            document_type.lower(),
            JSONFormat.metadata_required_fileds,
        )

    return metadata


def get_parent_document(parent_document_file_id):
    global session
    document = session.query(Document).filter_by(uuid=parent_document_file_id).first()
    return document


def get_splitted_document(splitted_document_file_id):
    global session
    document = (
        session.query(Splitted_Document)
        .filter_by(uuid=splitted_document_file_id)
        .first()
    )
    return document


def get_splitted_document_file_stream(splitted_document_file_id):
    response = minio_client.get_object(MINIO_FILES_BUCKET, splitted_document_file_id)
    in_memory_file = BytesIO(response.read())
    file_stream = in_memory_file.read()
    return file_stream


def callback(msg_tuple):
    ch, method, properties, body = msg_tuple
    """
    Function to be called by RabbitMQ consumer loop when a message is received.

    Args:
        ch (BlockingChannel): The channel object.
        method (Method): The method frame with delivery tag and other delivery parameters.
        properties (BasicProperties): The properties of the message.
        body (bytes): The message body, expected to be a JSON string with keys 'file_id', 'document_type',
          and optionally 'tenant_id' and 'subtenant_id'
          example: {'file_id': str, 'document_type': str, 'tenant_id': str, 'subtenant_id': str}

    Steps:
        1. Decode the JSON string from the message body to extract required fields.
        2. Start a new database session.
        3. Retrieve the splitted document associated with 'file_id' from the database.
        4. If the splitted document exists:
            a. Retrieve the file stream for the splitted document.
            b. Retrieve the parent document associated with the splitted document.
            c. Extract metadata from the splitted document based on its type and file stream.
            d. If metadata extraction is successful:
                i. Save the extracted metadata to the database.
                ii. Commit the changes to the database.
                iii. Print a success message indicating metadata was extracted.
            e. If metadata extraction fails, print a warning message.
            f. Declare the next processing queue in RabbitMQ.
            g. Publish the message to the next processing queue for further handling.
        5. If the splitted document does not exist, print a message indicating the document was skipped.
        6. Close the database session.
        7. Acknowledge the message as processed.

    Note:
        This function handles the processing of a splitted document message, extracts its metadata, saves it to the database, and forwards the message for further processing.
    """

    global session

    print(f" [x] Received {body}")
    queue_item = json.loads(body.decode("UTF-8"))

    splitted_document_file_id = queue_item["file_id"]
    document_type = queue_item["document_type"]
    # Extract tenant information from the message
    tenant_id = queue_item.get('tenant_id')
    subtenant_id = queue_item.get('subtenant_id')
    
    print(f"Extracting metadata for document type '{document_type}' for tenant: {tenant_id}, subtenant: {subtenant_id}")

    session = db_connector.get_session()
    splitted_document = get_splitted_document(splitted_document_file_id)
    if splitted_document:
        splitted_document_file_stream = get_splitted_document_file_stream(
            splitted_document_file_id
        )
        parent_document = get_parent_document(splitted_document.parent_document_uuid)
        print(f"pd id: {str(parent_document.uuid)}")

        metadata_extracted = extract_metadata(
            splitted_document, document_type, splitted_document_file_stream, tenant_id, subtenant_id
        )

        if metadata_extracted:
            session.merge(splitted_document)
            session.merge(parent_document)
            save_metadata_to_db(metadata_extracted, splitted_document, parent_document)
            session.commit()
            print("Metadata extracted.")

        else:
            print("WARNING: Metadata did not extracted.")
        
        # Pass tenant information to the next component
        next_queue_item = {
            'file_id': splitted_document_file_id,
            'document_type': document_type,
            'tenant_id': tenant_id,
            'subtenant_id': subtenant_id
        }
        rmq_service.send_message(RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME, json.dumps(next_queue_item))

    else:
        print("No splitted doc in db. Skipping...")

    session.close()
    rmq_service.safe_ack(ch, method.delivery_tag)
    # connection.close() # if uncomment, process only one message


torch.multiprocessing.set_start_method("spawn", force=True)
rmq_service.read_messages(RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME, callback)
# monitor = MonitorService(
#     PROJECT_NAME, APP_NAME, "metadata_extractor", API_HOST, API_PORT
# )
# monitor.start_monitoring()
# monitor.update_status(Status.UP)
rmq_service.run()
