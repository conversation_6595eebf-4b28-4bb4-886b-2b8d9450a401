INFO_EXTRACTION_PROMPT = """
Given a medical text that contains multiple sections, including a '{keyword}' section:

1. Identify the different section titles in the text.
2. Locate the '{keyword}' section.
3. Extract and return only the content under the '{keyword}' section, preserving its original format and numbering.
4. Do not include any content from other sections.
5. To determine the end of the '{keyword}' section, look for a heading that would look like a new section title. This new section title is often:
    - Written in all capital letters (e.g., "HISTORY OF PRESENT ILLNESS", "HPI" etc.)
    - Followed by a colon (e.g., "Causation:", "Diagnosis:", "Plan:" etc.)
    - A clear and concise phrase that indicates a new topic (e.g., "Special Instructions", "Progress Notes", "Follow-up Plan", "History of Present Illness" etc.)
    - Extract the content until you reach this new section title or the end of the text.
6. If the context contains any random text, please, rewrite it to make it more coherent for a human to read. Also, ensure that the extracted content is grammatically correct and has proper punctuation.
7. IMPORTANT: Do NOT include any contact information in your response. This includes, but is not limited to, phone numbers, email addresses, physical addresses, or any other personally identifiable information. Remove ALL such information from the extracted content.
8. If the keyword is "Chief Complaint", ensure that you do not include anything related to "History of Present Illness", "HPI" or "Allergies" sections in your response.
9. Output the extracted content as a JSON object with the following format:
{{
    "{keyword}": "<Extracted content under the '{keyword}' section>"
}}

Your output should ONLY be a JSON object with the key '{keyword}' and the value as the extracted content under the '{keyword}' section.

Please process the following text according to these instructions:
<text>
{text}
</text>
"""

TEXT_FORMATTING_PROMPT = """
You are an expert in language processing. Your task is to rewrite the following text to make it more coherent for a human to read.
Follow these guidelines:
1. Ensure that the extracted content is grammatically correct and has proper punctuation.
2. Do not change the inherent structure and meaning of the text. Rather, focus on improving the readability and coherence.
3. In case the text contains any information regarding contact details, you should not include them in your response.
4. Do not use any additional pronouns: you should not change the text to be in the first or second person.
5. IMPORTANT: ALWAYS exclude any page numbers, headers, footers, or other OCR-related artifacts that are not part of the main content. This includes removing any text that appears to be formatting information or document metadata as well as any text that is not part of the main content (such as Progress Notes).
6. Preserve any identifiable section headings (such as "Chief Complaint", "Discussion", "History of Present Illness", etc.) in their original form. You may rewrite the content within these sections, but do not remove or alter the section headings themselves.
7. Output the rewritten text as a JSON object with the following format:
{{
    "formatted_text": "<Rewritten content>"
}}

Here is the text that needs to be rewritten:
<text>
{text}
</text>
"""

SUMMARY_KEYWORD_PATTERNS = {
    "Medical Records": [
        {
            "Chief Complaint": r"[Cc][Hh][IiLl][Ee][FfRrLlEe]\s*(?:[Pp][Aa][Ii][Nn]\s*)?[Cc][Oo][Mm][Pp][Ll][Aa][Ii][Nn][Tt]"
        },
        {
            "Assessment/Plan": r"^A[Ss][Ss][Ee][Ss][Ss][Mm][Ee][Nn][TtSs]\s*(/|[Aa][Nn][Dd]|&)(?:[Tt][Rr][Ee][Aa][Tt][Mm][Ee][Nn][Tt]\s*)?\s*[Pp][Ll][Aa][Nn]",
        },
        {
            "Assessment": r"^A[Ss][Ss][Ee][Ss][Ss][Mm][Ee][Nn][TtSs]",
        },
        {
            "Reason for Visit": r"R[Ee][Aa][Ss][Oo][Nn]\s*[Ff][Oo][Rr]\s*(?:[Vv][Ii][Ss][Ii][Tt]|[Aa][Pp][Pp][Oo][Ii][Nn][Tt][Mm][Ee][Nn][Tt]|[Rr][Ee][Ff][Ee][Rr][Rr][Aa][Ll])",
        },
        {
            "Objective": r"\bO[Bb][Jj][Ee][Cc][Tt][Ii][VvWw][Ee]\b",
        },
        {
            "Plan": r"\bP[Ll][Aa][Nn]\b",
        },
        {
            "Discussion/Plan": r"D[Ii][Ss][Cc][Uu][Ss][Ss][Ii][Oo][Nn](\s*(/|[Aa][Nn][Dd])\s*[Pp][Ll][Aa][Nn])?",
        },
        {
            "Additional Notes": r"A[Dd][Dd][Ii][Tt][IiLl][Oo][Nn][Aa][Ll]\s*[Nn][Oo][Tt][Ee][Ss]",
        },
    ],
    "Supplemental/Work Status": [
        {
            "Subjective Complaints": r"S[Uu][BbTt][JjIi][EeOo][Cc][Tt][Ii][Vv][Ee]\s*([Cc][Oo][MmNn][Pp][Ll][Aa][Ii][Nn][Tt][Ss])?",
        },
        {
            "Objective Findings": r"O[Bb][JjIi][Ee][Cc][Tt][Ii][Vv][Ee]\s*([Ff][Ii][Nn][DdXx][Ii][Nn][Gg][Ss])?",
        },
        {
            "Diagnosis": r"D([Ii])?[Aa][Gg]([Gg])?[NnRr][Oo][Ss]([IiEe])?[Ss]",
        },
        {
            "Comments": r"C[Oo][Mm][Mm][Ee][Nn][TtLlIi][Ss]",
        },
        {
            "Assessment": r"^A[Ss][Ss][Ee][Ss][Ss][Mm][Ee][Nn][Tt]",
        },
        {
            "Work Status": r"[WH][Oo][Rr][Kk]\s*S[Tt][Aa][Tt][Uu][Ss]",
        },
    ],
}

SUMMARY_ADDITIONAL_PAGE_PATTERNS = [
    {"See Attached": r"see\s*attached"},
    {"See Additional Page": r"see\s*addi(ti|s)onal"},
    {"See Addendum": r"see\s*addendum"},
]
