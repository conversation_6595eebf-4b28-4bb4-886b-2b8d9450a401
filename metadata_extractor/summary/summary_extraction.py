import base64
import json
import os
import re

from doclayout_yolo import YOLOv10
from dotenv import load_dotenv
from naming.postprocess_naming_fields import process_json_llm_response
from ollama import Client
from rapidfuzz import fuzz
from summary.summary_keyword_patterns import (
    INFO_EXTRACTION_PROMPT,
    SUMMARY_ADDITIONAL_PAGE_PATTERNS,
    SUMMARY_KEYWORD_PATTERNS,
    TEXT_FORMATTING_PROMPT,
)
from utils_ import convert_pdf_to_pixmap, pix_to_image
from summary.work_status_summary_checkbox import run_work_status_checkbox_extraction
from collections import defaultdict

load_dotenv()

MEDICAL_RECORDS_MODEL_PATH = os.getenv(
    "MEDICAL_RECORDS_MODEL_PATH", "/models/medical_records_title_and_text_layout_model.pt"
)
WORK_STATUS_MODEL_PATH = os.getenv(
    "WORK_STATUS_MODEL_PATH", "/models/work_status_title_and_text_layout_model.pt"
)

if not os.path.exists(MEDICAL_RECORDS_MODEL_PATH):
    raise FileNotFoundError(f"Medical Records model not found at {MEDICAL_RECORDS_MODEL_PATH}")
if not os.path.exists(WORK_STATUS_MODEL_PATH):
    raise FileNotFoundError(f"Work Status model not found at {WORK_STATUS_MODEL_PATH}")

MEDICAL_RECORDS_TITLE_PLAIN_TEXT_MODEL = YOLOv10(MEDICAL_RECORDS_MODEL_PATH)
WORK_STATUS_TITLE_PLAIN_TEXT_MODEL = YOLOv10(WORK_STATUS_MODEL_PATH)

TITLE_PLAIN_TEXT_MODELS = {
    "Medical Records": MEDICAL_RECORDS_TITLE_PLAIN_TEXT_MODEL,
    "Supplemental/Work Status": WORK_STATUS_TITLE_PLAIN_TEXT_MODEL,
}

# BASE_AUTH_USER_AND_PASSWORD = os.environ["BASE_AUTH_USER_AND_PASSWORD"]
SERVER_HOST = os.environ["SERVER_HOST"]
SERVER_PORT = os.environ["SERVER_PORT"]

# BASE64_STRING = base64.b64encode(BASE_AUTH_USER_AND_PASSWORD.encode()).decode("ascii")
# AUTH_HEADER = {"Authorization": f"Basic {BASE64_STRING}"}

OLLAMA_CLIENT = Client(
    host=f"{SERVER_HOST}:{SERVER_PORT}",
    # headers=AUTH_HEADER,
    timeout=360.0,
    verify=False,
)

OLLAMA_OPTIONS = {
    "num_predict": 512,
    "temperature": 0,
    "top_k": 5,
    "top_p": 0.1,
    # "mirostat_tau": 0.5,
    "mirostat": 2,
}

MODEL_NAME = "mistral-nemo:12b-instruct-2407-q5_K_M_medical_assistant"


def process_with_ollama(input_prompt: str) -> str:
    """
    Processes the input prompt using Ollama client.

    Args:
        input_prompt (str): The input prompt to process.

    Returns:
        str: The response text from the language model.
    """
    response = OLLAMA_CLIENT.generate(
        model=MODEL_NAME,
        prompt=input_prompt,
        options=OLLAMA_OPTIONS,
    )
    return response["response"]


def sort_data(data: list) -> list:
    """
    Sorts the data based on the bounding box coordinates.

    Args:
        data (list): The data to be sorted.

    Returns:
        list: The sorted data.
    """
    for i in range(len(data) - 1):
        if data[i + 1]["class"] == 0.0 and data[i]["class"] == 1.0:
            if (
                abs(data[i + 1]["bbox"][1] - data[i]["bbox"][1])
                / max(data[i + 1]["bbox"][1], data[i]["bbox"][1])
                < 0.01
            ):
                data[i + 1], data[i] = data[i], data[i + 1]
    return data


def extract_text_from_bbox(doclayout_box: list[int], ocr_res: list) -> str:
    """
    Extracts text from OCR results within a bounding box.

    Args:
        doclayout_box: The bounding box coordinates to extract text from.
        ocr_res: The OCR results containing bounding boxes with text.

    Returns:
        str: The extracted text from the bounding box.
    """

    text_in_box = []
    if not ocr_res or not ocr_res[0]:
        return ""
    for bbox in ocr_res:
        coords_list = bbox[0]
        bbox_coords = (
            coords_list[0][0],
            coords_list[0][1],
            coords_list[2][0],
            coords_list[2][1],
        )
        if calculate_iou(bbox_coords, doclayout_box) > 0.02:
            text_in_box.append(bbox[1][0])
    return "".join(text_in_box)


def extract_context(
    ocr_data: list, document_field: str, keyword_pattern: str, other_headings: list
) -> tuple[str, list]:
    """
    Extracts context text and page numbers surrounding a keyword pattern from OCR data.

    Returns:
        tuple: (extracted_text, list_of_page_numbers)
    """
    result = ""
    pages = []
    bounding_boxes = [
        (page_num, bbox) for page_num, page in ocr_data.items() for bbox in page
    ]

    start_found = False
    context = []

    for index, (page_num, bbox) in enumerate(bounding_boxes):
        text = bbox[1][0]

        if not start_found:
            match = re.search(keyword_pattern, text)
            if match:
                context.append(text[match.start() :])
                pages.append(int(page_num))
                start_found = True

                for page_num_next, bbox in bounding_boxes[index + 1 :]:
                    next_text = bbox[-1][0]
                    text_for_fuzzy_comparison = next_text[: len(document_field) + 10]

                    if len(" ".join(context)) > 768:
                        result = " ".join(context)[:768]
                        return result, sorted(list(set(pages)))

                    if any(re.search(pattern, next_text) for pattern in other_headings):
                        result = " ".join(context)[:768]
                        return result, sorted(list(set(pages)))
                    elif any(
                        fuzz.ratio(text_for_fuzzy_comparison, pattern) > 75
                        for pattern in other_headings
                    ):
                        result = " ".join(context)[:768]
                        return result, sorted(list(set(pages)))
                    else:
                        context.append(next_text)
                        pages.append(int(page_num_next))

    result = " ".join(context)[:768]
    if len(result) < 10:
        return "", []
    return result, sorted(list(set(pages)))


def calculate_iou(box1: list[int], box2: list[int]) -> float:
    """
    Calculates the Intersection over Union (IoU) between two bounding boxes.

    Args:
        box1: The first bounding box as a list of integers [x1, y1, x2, y2].
        box2: The second bounding box as a list of integers [x1, y1, x2, y2].

    Returns:
        float: The IoU value between the two bounding boxes.
    """
    x1, y1, x2, y2 = box1
    x1b, y1b, x2b, y2b = box2

    xi1 = max(x1, x1b)
    yi1 = max(y1, y1b)
    xi2 = min(x2, x2b)
    yi2 = min(y2, y2b)

    inter_area = max(xi2 - xi1, 0) * max(yi2 - yi1, 0)

    box1_area = (x2 - x1) * (y2 - y1)
    box2_area = (x2b - x1b) * (y2b - y1b)

    union_area = box1_area + box2_area - inter_area

    if union_area == 0:
        return 0.0

    iou = inter_area / union_area
    return iou


def process_doclayout_metadata(
    doclayout_res: list, ocr_res: list, page_number: int, json_data: list
) -> list:
    """
    Processes document layout results to extract metadata.

    Args:
        doclayout_res (list): List of document layout results with bounding box and class info.
        ocr_res (list): OCR results for text extraction.
        page_number (int): Current page number.
        json_data (list): The existing JSON structure to be updated.

    Returns:
        list: Updated metadata list for the document.
    """
    meta_data = []

    for data in doclayout_res:
        if data["class"] == 1.0:
            text = extract_text_from_bbox(data["bbox"], ocr_res)

            if len(meta_data) == 0 and page_number == 0:
                meta_data.append({"title": "", "text": []})
            if len(meta_data) == 0 and page_number > 0:
                if not json_data or not json_data[-1]["data"]:
                    json_data[0] = {"page_number": page_number, "data": meta_data}
                else:
                    json_data[-1]["data"][-1]["text"].append(text)
            else:
                meta_data[-1]["text"].append(text)

        elif data["class"] == 0.0:
            title = extract_text_from_bbox(data["bbox"], ocr_res)

            if len(meta_data) != 0 and title in meta_data[-1]["title"]:
                continue

            meta_data.append({"title": title, "text": []})
    return meta_data


def postprocess_llm_response(text: str, json_dict_key: str) -> str:
    """
    Processes the response from a language model to extract and clean up the relevant content.

    Args:
        text (str): The raw response text from the language model.
        json_dict_key (str): The key to look for in the JSON structure.

    Returns:
        str: The cleaned and formatted response text, or an empty string in case of failure.
    """
    result = process_json_llm_response(text)

    try:
        json_result = json.loads(result)
        if json_dict_key in json_result.keys():
            result = json_result[json_dict_key].strip()
        else:
            result = ""

        result = result.replace("\n", " ").replace("\t", " ")

        return result
    except Exception as e:
        return ""


def get_result_for_one_field(context: str, document_field: str) -> str:
    """
    Extracts information for a single field from the given context using a language model.

    Args:
        context (str): The context text to extract information from.
        document_field (str): The field to extract information for.

    Returns:
        str: The extracted information for the specified field.
    """
    formatted_prompt = INFO_EXTRACTION_PROMPT.format(
        text=context, keyword=document_field
    )
    response = process_with_ollama(formatted_prompt)

    try:
        processed_response = postprocess_llm_response(response, document_field)
    except Exception:
        processed_response = ""

    return processed_response


def extract_title_text(file_stream: bytes, ocr_results: list, model: YOLOv10) -> list:
    """
    Extracts title and text from the document layout detection results.

    Args:
        file_stream: The file stream of the document.
        ocr_results: The OCR results for the document.
        model: The document layout detection model.

    Returns:
        list: A list of dictionaries containing the extracted title and text data.
    """

    json_data = []
    pages = convert_pdf_to_pixmap(file_stream)
    for page_number, page_image in enumerate(pages):
        if ocr_results.get(str(page_number)):
            ocr_res = ocr_results[str(page_number)]
        else:
            continue

        image = pix_to_image(page_image)

        doclayout_res = model.predict(
            image,
            imgsz=640,
            conf=0.2,
            device="cuda:0",
            verbose=False,
        )
        doclayout_res = sort_data(
            sorted(
                (
                    {
                        "bbox": data[:4].tolist(),
                        "conf": data[4].item(),
                        "class": data[5].item(),
                    }
                    for data in doclayout_res[0].boxes.data
                    if data[5] in [0.0, 1.0]
                ),
                key=lambda data: data["bbox"][1],
            )
        )

        meta_data = process_doclayout_metadata(
            doclayout_res, ocr_res, page_number, json_data
        )
        json_data.append({"page_number": page_number, "data": meta_data})

    return json_data


def extract_layout_context(
    layout_detection_dict: dict, document_field: str, keyword_pattern: str
) -> str:
    """
    Extracts the context for a keyword from the layout detection results.

    Args:
        layout_detection_dict (dict): The layout detection results.
        document_field (str): The field to extract information for.
        keyword_pattern (str): The keyword pattern to search for.

    Returns:
        str: The extracted context for the keyword.
    """

    result = ""
    pages = []
    extracting = False
    found_pattern = False

    for page in layout_detection_dict:
        page_number = page["page_number"]
        for section in page["data"]:
            title = section["title"]
            text = " ".join(section["text"])

            if title in text:
                text = text.replace(title, "")

            if re.search(keyword_pattern, title, re.IGNORECASE) and not found_pattern:
                extracting = True
                found_pattern = True
                result += f"{title} {text} "
                pages.append(page_number)
            elif re.search(keyword_pattern, text, re.IGNORECASE) and not found_pattern:
                extracting = True
                found_pattern = True
                result += f"{text} "
                pages.append(page_number)
            elif extracting and title:
                extracting = False
            elif extracting:
                result += f"{text} "
                pages.append(page_number)
    result = result.strip()

    return result, sorted(list(set(pages)))


def construct_keyword_context(
    extracted_ocr_context: str,
    extracted_layout_context: str,
    extracted_ocr_pages: list,
    extracted_layout_pages: list,
) -> str:
    """
    Constructs the final context for a keyword by combining the extracted OCR and layout contexts.

    Args:
        extracted_ocr_context (str): The context extracted from OCR.
        extracted_layout_context (str): The context extracted from the layout.

    Returns:
        str: The final context for the keyword.
    """
    if extracted_ocr_context and extracted_ocr_context in extracted_layout_context:
        return (extracted_ocr_context, extracted_ocr_pages)
    elif extracted_layout_context and extracted_layout_context in extracted_ocr_context:
        return (extracted_layout_context, extracted_layout_pages)

    if len(extracted_ocr_context) < len(extracted_layout_context) or (
        extracted_ocr_context and not extracted_layout_context
    ):
        return (extracted_ocr_context, extracted_ocr_pages)
    elif len(extracted_layout_context) < len(extracted_ocr_context) or (
        extracted_layout_context and not extracted_ocr_context
    ):
        return (extracted_layout_context, extracted_layout_pages)
    elif not extracted_ocr_context and not extracted_layout_context:
        return ("", [])


def reformat_extracted_context(extracted_context: str) -> str:
    """
    Reformats the extracted context text using a specified formatting prompt.

    Args:
        extracted_context (str): The raw extracted context that needs formatting.

    Returns:
        str: The formatted response from the language model.
    """

    prompt = TEXT_FORMATTING_PROMPT.format(text=extracted_context)
    response = process_with_ollama(prompt)

    try:
        formatted_response = postprocess_llm_response(response, "formatted_text")
    except Exception:
        formatted_response = extracted_context

    return formatted_response


def check_for_additional_page_pattern(extracted_context: str) -> str:
    """
    Checks for additional page patterns in the extracted context.

    Args:
        extracted_context (str): The extracted context text.
        document_field (str): The document field to check for additional page patterns.

    Returns:
        str: The keyword for the additional page pattern if found, otherwise an empty string.
    """
    for pattern_dict in SUMMARY_ADDITIONAL_PAGE_PATTERNS:
        for keyword, pattern in pattern_dict.items():
            if re.search(pattern, extracted_context, re.IGNORECASE):
                return keyword
    return ""


def process_document(
    ocr_results: list,
    doc_type: str,
    document_file_stream: bytes,
) -> dict:
    """
    Processes the OCR and layout detection results of a document to extract relevant information based on keyword patterns.

    Args:
        ocr_results (list): A list of OCR results for each page of the document.
        doc_type (str): The type of document, used to select keyword patterns.
        document_file_stream (bytes): The file stream of the document being processed.

    Returns:
        dict: A dictionary containing extracted fields and their corresponding values.
    """

    ocr_data_without_empty_pages = {
        str(idx): page for idx, page in enumerate(ocr_results) if page and page[0]
    }
    ocr_results = ocr_data_without_empty_pages

    title_text_detection_model = TITLE_PLAIN_TEXT_MODELS.get(doc_type)

    formatted_ocr_results = ocr_results
    layout_detection_results = extract_title_text(
        document_file_stream, formatted_ocr_results, title_text_detection_model
    )

    final_result = defaultdict(dict)

    for keyword_dict in SUMMARY_KEYWORD_PATTERNS[doc_type]:
        for document_field, keyword_pattern in keyword_dict.items():
            if (
                document_field in ["Assessment", "Plan"]
                and "Assessment/Plan" in final_result
                and len(final_result["Assessment/Plan"]["text"]) > 32
            ):
                continue

            if document_field == "Work Status":
                checkbox_extraction_results = run_work_status_checkbox_extraction(
                    document_file_stream
                )
                if (
                    isinstance(checkbox_extraction_results, dict)
                    and checkbox_extraction_results.get("text")
                    and checkbox_extraction_results.get("pages")
                ):
                    work_status_result_text = checkbox_extraction_results["text"]
                    formatted_work_status_result = reformat_extracted_context(
                        work_status_result_text
                    )
                    final_result[document_field]["text"] = formatted_work_status_result
                    final_result[document_field]["pages"] = checkbox_extraction_results[
                        "pages"
                    ]
                continue

            other_headings = [
                pattern
                for pattern_dict in SUMMARY_KEYWORD_PATTERNS[doc_type]
                for _, pattern in pattern_dict.items()
                if pattern != keyword_pattern
            ]

            extracted_context_ocr, extracted_ocr_pages = extract_context(
                formatted_ocr_results,
                document_field,
                keyword_pattern,
                other_headings=other_headings,
            )

            extracted_context_layout, extracted_layout_pages = extract_layout_context(
                layout_detection_results, document_field, keyword_pattern
            )

            extracted_context, extracted_page_numbers = construct_keyword_context(
                extracted_context_ocr,
                extracted_context_layout,
                extracted_ocr_pages,
                extracted_layout_pages,
            )

            if not extracted_context:
                continue

            # Check for additional page patterns in the extracted context for certain fields
            if document_field in ["Subjective Complaints", "Objective Findings"]:
                additional_page_keyword = check_for_additional_page_pattern(
                    extracted_context
                )
                if additional_page_keyword:
                    final_result[document_field]["text"] = extracted_context
                    final_result[document_field]["pages"] = extracted_page_numbers
                    continue

            if len(extracted_context.strip()) > 128:
                reformatted_context = reformat_extracted_context(extracted_context)
            else:
                reformatted_context = extracted_context

            if not reformatted_context.strip():
                continue

            processed_response = get_result_for_one_field(
                reformatted_context, document_field
            )

            # Filter out similar/duplicate results in the final result
            if any(
                fuzz.ratio(processed_response, final_result[field]["text"]) > 90
                for field in final_result
            ):
                continue

            if processed_response:
                final_result[document_field]["text"] = processed_response
                final_result[document_field]["pages"] = extracted_page_numbers

    return final_result


def run_summary_extraction(
    ocr_results: list, doc_type: str, document_file_stream: bytes
) -> dict:
    """
    Runs the summary extraction process on the OCR results of a document.

    Args:
        ocr_results (list): A list of OCR results for each page of the document.
        doc_type (str): The type of document to process.
        document_file_stream (bytes): The file stream of the document being processed.

    Returns:
        dict: A dictionary containing the extracted summary information."""

    results = process_document(ocr_results, doc_type, document_file_stream)

    text_result = ""

    for key in results:
        text_result += f"{key}: {results[key]['text']}. "

    text_result = re.sub(r"\.{2,}", ".", text_result)

    all_summary_pages = sorted(
        list(set([page for field in results for page in results[field]["pages"]]))
    )
    summary_pages_with_formatted_indexing = [page + 1 for page in all_summary_pages]

    if len(summary_pages_with_formatted_indexing) > 1:
        text_result += (
            f"Pages: [{','.join(map(str, summary_pages_with_formatted_indexing))}]"
        )
    elif len(summary_pages_with_formatted_indexing) == 1:
        text_result += f"Page: [{summary_pages_with_formatted_indexing[0]}]"

    text_result = text_result.strip()

    return {"summary": text_result}
