import base64
import json
import os

from dotenv import load_dotenv
from ollama import Client
from paddleocr import PaddleOC<PERSON>
from ultralytics import Y<PERSON><PERSON>
from utils_ import convert_pdf_to_pixmap, pix_to_image

TEXT_TRANSFORMATION_PROMPT = """
You're tasked with reformatting a medical sentence for clarity, while ensuring no important details are lost. Follow these rules to make sure the data is well-structured and readable:

1. **Keep Dates Intact:** If there's a date in the sentence, don't change or remove it.

2. **Reorder for Clarity:** The sentence should start with "Return to modified work on [date]," followed by the rest of the text in its original order. If the words "Other" and "Restrictions:" appear, combine them into "Other Restrictions:". Also, phrases like "cont current restr" should remain as part of the restrictions.

3. **If It's Correct, Leave It Alone:** If the sentence is already in the correct format, don't change anything. Just return it as it is.

4. **Fix Misspellings:** Correct any typos or misspellings, ensuring the text is professionally written.

5. **Preserve Important Instructions:** Don't remove or alter important phrases like "See attached." These should be kept at the end of the sentence.

You should always output the result in JSON format as:
{{ "text": "transformed sentence here" }}

Examples:

Input : "10/04/23 Return to modified work on with the following limitations or restrictions. ( List all specific restrictions re: standing, sitting, bending, use of hands, etc.): 10/04/23 Modified  Work 10/04/23 10/18/23 WC-RE-CHECK INJURY * No Pushing Pulling Lifting Over 5 Ibs. * Must take a 10 minutes stretch break every. 60 minutes from:. Keyboard"
Output: {{ "text": "Return to modified work on 10/04/23 with the following limitations or restrictions. (List all specific restrictions re: standing, sitting, bending, use of hands, etc): 10/04/23 Modified  Work 10/04/23 10/18/23 WC-RE-CHECK INJURY * No Pushing Pulling Lifting Over 5 Ibs. * Must take a 10 minutes stretch break every. 60 minutes from:. Keyboard" }}

Input: "with the following limitations or restrictions. (List all specific restrictions re: standing, sitting, bending, use of hands, etc.)"
Output: {{ "text": "Return to modified work on with the following limitations or restrictions. (List all specific restrictions re: standing, sitting, bending, use of hands, etc)." }}

Input: ' with the following limitations or restrictions**  Return to modified work on 4/18/2023  Other cont current restr Restrictions:'
Output: {{ "text": "Return to modified work on 4/18/2023 on with the following limitations or restrictions** Other Restrictions: cont current restr" }}

Input:' with the following limitations or restrictions**  Return to modified work on 7/25/2023 Other cont current restr Avoid lifting more than 5 Ibs and repetitive movements with the upper extremities. Avoid frequent reaching. Restrictions: (particularly over head), pulling, or pushing using the upper extremities.'
Output: {{ "text": " Return to modified work on 7/25/2023 with the following limitations or restrictions**  Other Restrictions: cont current restr Avoid lifting more than 5 Ibs and repetitive movements with the upper extremities. Avoid frequent reaching (particularly over head), pulling, or pushing using the upper extremities."}}

Your task is to apply these rules to the following sentence: {0}
"""


MULTIPLE_CHECKBOX_CASE = [
    "Return to full duty",
    "Not First Aid",
    "Not First Aid",
    "Permanent and Stationary",
    "No Longer First Aid",
    "Future Medical",
    "Off Remainder of Shift",
]


WORKSTATUS_START_KEYWORDS = [
    "Work.Status",
    "Work Status:",
    "Work Status",
    "Work status",
    "WORK STATIS",
    "Wark Status",
    "WORK StATUS",
    "WORK STaTUS",
    "Work Slalus:",
    "wORK STATUS",
    "WORKSTATUS",
    "WORK STATUS",
    "Work Stalus",
    "WORK STATUS INFORMATION",
    "(Fully complete one box",
]

WORKSTATUS_END_KEYWORDS = [
    "DISABILITY STATUS",
    "EXAMINING PHYSICIAN",
    "EXAMINNG PHYSICIAN",
    "EXAMINING PHYSICIAN / ASSISTANT",
    "Modified Duties",
    "Prinry Tring",
    "Primary Trealing Physician",
    "Primary Trealing Physiclan:",
    "Primary Treating Physician",
    "ACTIVITY RESTRIC",
    "AGTIVITYRESIRICTIONS",
    "ACTIVITY RESTRICTIONS",
    "HODIFIED MITES",
    "ACTIVITYRES",
    "ACTIVITY RES",
    "(Only complete if box",
    "Posture Restrictions",
    "POSTURE RESTRICTIONS",
    "Motion Restricions",
]

CHECKBOX_DETECTION_MODEL_PATH = os.getenv("CHECKBOX_DETECTION_MODEL_PATH", "/models/work_status_summary_checkbox_model.pt")
CHECKBOX_DETECTION_MODEL = YOLO(CHECKBOX_DETECTION_MODEL_PATH)

CUSTOM_OCR = PaddleOCR(use_angle_cls=True, lang="en", show_log=False, use_gpu=True)

load_dotenv()

# BASE_AUTH_USER_AND_PASSWORD = os.environ["BASE_AUTH_USER_AND_PASSWORD"]
SERVER_HOST = os.environ["SERVER_HOST"]
SERVER_PORT = os.environ["SERVER_PORT"]

# BASE64_STRING = base64.b64encode(BASE_AUTH_USER_AND_PASSWORD.encode()).decode("ascii")
# AUTH_HEADER = {"Authorization": f"Basic {BASE64_STRING}"}

OLLAMA_CLIENT = Client(
    host=f"{SERVER_HOST}:{SERVER_PORT}",
    # headers=AUTH_HEADER,
    timeout=360.0,
    verify=False,
)

OLLAMA_OPTIONS = {
    "num_predict": 200,
    "temperature": 0,
}


MODEL_NAME = "mistral-nemo:12b-instruct-2407-q5_K_M_medical_assistant"


def process_with_ollama(input_prompt: str) -> str:
    """
    Processes the input prompt using Ollama client.

    Args:
        input_prompt (str): The input prompt to process.

    Returns:
        str: The response text from the language model.
    """
    response = OLLAMA_CLIENT.generate(
        model=MODEL_NAME,
        prompt=input_prompt,
        options=OLLAMA_OPTIONS,
    )
    return response["response"]


def calculate_iou(box1: list[int], box2: list[int]) -> float:
    """
    Calculates the Intersection over Union (IoU) of two bounding boxes.

    Args:
        box1 (list[int]): Coordinates of the first box in the format [x1, y1, x2, y2].
        box2 (list[int]): Coordinates of the second box in the same format.

    Returns:
        float: The IoU score between the two boxes, ranging from 0.0 to 1.0.
    """
    x1, y1, x2, y2 = box1
    x1b, y1b, x2b, y2b = box2

    # Find the intersection coordinates
    xi1 = max(x1, x1b)
    yi1 = max(y1, y1b)
    xi2 = min(x2, x2b)
    yi2 = min(y2, y2b)

    inter_area = max(xi2 - xi1, 0) * max(yi2 - yi1, 0)

    box1_area = (x2 - x1) * (y2 - y1)

    if box1_area == 0:
        return 0.0

    iou = inter_area / box1_area
    return iou


def extract_work_status_section_image(page_image: list, ocr_results: list) -> list:
    """
    Extracts the Work Status section from the page image.

    Args:
        page_image: list of pixels of the page image
        ocr_results : list of OCR results

    Returns:
         list: cropped image section of work status from the page image
    """

    try:
        crop_coords_top = int(
            min(
                bb[1][1]
                for bb, text in zip(ocr_results[0], ocr_results[1])
                if any(key in text[0] for key in WORKSTATUS_START_KEYWORDS)
                and bb[2][0] < (0.45 * page_image.shape[1])
            )
        )
    except ValueError:
        crop_coords_top = int(0.1 * len(page_image))

    try:
        crop_coords_bot = int(
            min(
                bb[1][1]
                for bb, text in zip(ocr_results[0], ocr_results[1])
                if any(key in text[0] for key in WORKSTATUS_END_KEYWORDS)
            )
        )
    except ValueError:
        crop_coords_bot = len(page_image)
    new_image = page_image[crop_coords_top:crop_coords_bot, 0 : len(page_image)]

    return new_image


def extract_text_near_checkbox(new_image: list) -> str:
    """
    Extracts text near checkboxes in the Work Status section.

    Args:
        new_image: list of pixels of the Work Status section

    Returns:
         str: text corresponding to checkbox
    """
    try:
        ocr_result = CUSTOM_OCR(new_image)
    except Exception as e:
        return ""
    yolo_result = CHECKBOX_DETECTION_MODEL(new_image, verbose=False)
    boxes = yolo_result[0].boxes
    xyxy = boxes.xyxy.cpu().numpy()
    class_ids = boxes.cls.cpu().numpy()
    checkboxes = [coords for coords, class_id in zip(xyxy, class_ids) if class_id == 0]
    not_checkboxes = [
        coords for coords, class_id in zip(xyxy, class_ids) if class_id == 1
    ]

    work_status_text = ""

    if len(checkboxes) == 0:
        return work_status_text
    for checkbox in sorted(checkboxes, key=lambda x: x[1]):
        try:
            y_max = min(
                not_checkbox[1]
                for not_checkbox in not_checkboxes
                if not_checkbox[1] > checkbox[1]
                and not_checkbox[0] >= (checkbox[0] - 10)
            )

        except ValueError:
            y_max = int(new_image.shape[0])
        try:
            x_max = min(
                not_checkbox[0]
                for not_checkbox in not_checkboxes
                if abs(y_max - not_checkbox[3]) > 60
                and abs(checkbox[1] - not_checkbox[1]) <= 60
                and not_checkbox[0] > checkbox[0]
            )
        except ValueError:
            x_max = new_image.shape[1]

        extraction_box = [
            int(checkbox[0]),
            int(checkbox[1]) - 20,
            int(x_max),
            int(y_max),
        ]
        for bb, text in zip(ocr_result[0], ocr_result[1]):
            if (
                calculate_iou([bb[0][0], bb[0][1], bb[2][0], bb[2][1]], extraction_box)
                > 0.6
            ):
                work_status_text = " ".join([work_status_text, text[0]])
    try:
        prompt = TEXT_TRANSFORMATION_PROMPT.format(work_status_text)
        response = process_with_ollama(prompt)
        response_text = json.loads(response.replace("Output: ", ""))["text"]
        return response_text.replace("\n", "")
    except Exception:
        return work_status_text


def run_work_status_checkbox_extraction(file_stream: bytes) -> dict:
    """
    Extracts Work Status from the given PDF file.

    Args:
        file_stream (bytes): The PDF file as a byte stream.

    Returns:
        dict: The extracted Work Status information.
    """

    pages = convert_pdf_to_pixmap(file_stream)
    for idx, page in enumerate(pages):
        page_image = pix_to_image(page)
        try:
            ocr_results = CUSTOM_OCR(page_image)
        except Exception as e:
            continue

        if any(
            keyword in text[0]
            for text in ocr_results[1]
            for keyword in WORKSTATUS_START_KEYWORDS
        ) and all(
            keyword not in text[0]
            for text in ocr_results[1]
            for keyword in [
                "Procedure",
                "PRESCRIPTION",
                "CONDITION:",
                "CURRENT WORK STATUS",
                "X-rays",
                "Xroye",
            ]
        ):
            work_status_section = extract_work_status_section_image(
                page_image, ocr_results
            )
            work_status_text = extract_text_near_checkbox(work_status_section)
            work_status_text = work_status_text.strip()
            work_status_text = " ".join(work_status_text.split())
            output_pages = [idx]

            return {
                "text": work_status_text,
                "pages": output_pages,
            }

    return {}
