import base64
import json
import os
import re

import regex
from dotenv import load_dotenv
from naming.naming_prompts import DATE_EXTRACTION_PROMPT
from ollama import Client

load_dotenv()

# BASE_AUTH_USER_AND_PASSWORD = os.environ["BASE_AUTH_USER_AND_PASSWORD"]
SERVER_HOST = os.environ["SERVER_HOST"]
SERVER_PORT = os.environ["SERVER_PORT"]

# BASE64_STRING = base64.b64encode(BASE_AUTH_USER_AND_PASSWORD.encode()).decode("ascii")
# AUTH_HEADER = {"Authorization": f"Basic {BASE64_STRING}"}

# OLLAMA_CLIENT = Client(host=f"{SERVER_HOST}:{SERVER_PORT}", headers=AUTH_HEADER, timeout=360.0, verify=False)
OLLAMA_CLIENT = Client(host=f"{SERVER_HOST}:{SERVER_PORT}", timeout=360.0, verify=False)


OLLAMA_OPTIONS = {
    "num_predict": 150,
    "temperature": 0,
    # "top_k": 5,
    # "top_p": 0.1,
    # "mirostat_tau": 0.5,
    # "num_gpu": 1,
}

MODEL_NAME = "mistral-nemo:12b-instruct-2407-q5_K_M_medical_assistant"

BASIC_DATE_PATTERN = r"\d{1,2}(/|-)\d{1,2}(/|-)\d{2,4}"
COMPLEX_DATE_PATTERN = r"(\d{1,2}[/-]\d{1,2}[/-]\d{4})|(\d{1,2}\s(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\s\d{1,2},\s\d{4})|((?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\s\d{1,2}[,-]\s\d{4})|(\d{4}[/-]\d{1,2}[/-]\d{1,2})|(\d{1,2}\s(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\s\d{1,2}\s\d{4})"


def process_with_ollama(input_prompt: str) -> str:
    response = OLLAMA_CLIENT.generate(model=MODEL_NAME, prompt=input_prompt, options=OLLAMA_OPTIONS)
    return response["response"]


def extract_date(result: str) -> str:
    input_prompt = DATE_EXTRACTION_PROMPT.format(result=result)
    response = process_with_ollama(input_prompt=input_prompt)
    return response


def check_year(date_string: str) -> bool:
    year_pattern = r"\b(20\d{2}|[3-9]\d{3})\b"
    match = re.search(year_pattern, date_string)
    if match:
        year = int(match.group(0))
        return year > 2000 and year < 2100
    else:
        return False


COMPLEX_DATE_PATTERN = (
    r"(\d{1,2}[/-]\d{1,2}[/-]\d{4})|"
    r"(\d{1,2}\s(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?)\s\d{1,2},\s\d{4})|"
    r"((?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?)\s\d{1,2}[,-]\s\d{4})|"
    r"(\d{4}[/-]\d{1,2}[/-]\d{1,2})|"
    r"(\d{1,2}\s(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?)\s\d{1,2}\s\d{4})|"
    r"(\d{1,2}\s(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s\d{4})|"
    r"((?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)[/-]\d{1,2}[/-]\d{4})|"
    r"(\d{1,2}-(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-\d{4})|"
    r"(\d[A-Z]/\d{1,2}/\d{4})"
)


def medical_records_extract_dates(text: str) -> dict[str, str]:
    date_pattern = COMPLEX_DATE_PATTERN
    dates = re.findall(date_pattern, text)
    dates = [date for sublist in dates for date in sublist if date]
    actual_dates = []
    for date in dates:
        if not check_year(date):
            continue
        # find the index of the date
        date_index = text.find(date)
        # extract 10 characters before the date
        before_date = text[max(0, date_index - 15) : date_index]
        # extract 10 characters after the date
        after_date = text[date_index : min(len(text), date_index + 15)]
        date_context = f"{before_date} {after_date}"
        if (
            re.search(r"doi[:\s]", date_context, re.IGNORECASE)
            or re.search(r"DOI[:\s]", date_context, re.IGNORECASE)
            or re.search(r"DO1[:\s]", date_context, re.IGNORECASE)
            or re.search(r"dob[:\s]", date_context, re.IGNORECASE)
            or re.search(r"date\s*of\s*birth", date_context, re.IGNORECASE)
            or re.search(r"Visit[:\s]", date_context, re.IGNORECASE)
            or re.search(r"Today[:\s]", date_context, re.IGNORECASE)
            or re.search(r"SOC Date", date_context, re.IGNORECASE)
            or re.search(r"ED on", date_context, re.IGNORECASE)
            or re.search(r"Service OT", date_context, re.IGNORECASE)
            or re.search(r"Onset[:\s]", date_context, re.IGNORECASE)
            or re.search(r"MD Date[:\s]", date_context, re.IGNORECASE)
            or re.search(r"Order Date[:\s]", date_context, re.IGNORECASE)
            or re.search(r"date\s*of\s*injury", date_context, re.IGNORECASE)
            or re.search(r"mmi", date_context, re.IGNORECASE)
            or re.search(r"I N G :", date_context, re.IGNORECASE)
        ):
            continue
        else:
            actual_dates.append(date)
    date_found = actual_dates[0] if actual_dates else ""
    return {"docDate": "", "docReceivedDate": date_found}


def case_management_notes_extract_dates(text: str) -> dict[str, str]:
    text = text[:40]
    match = re.search(BASIC_DATE_PATTERN, text, re.IGNORECASE)

    if match:
        receipt_date = match.group(0)
        return {"docDate": "", "docReceivedDate": receipt_date}
    return {"docDate": "", "docReceivedDate": ""}


def fax_extract_dates(text: str) -> dict[str, str]:
    text = text[:350]
    dates = extract_date(text)
    try:
        json_dates = json.loads(dates)
        receipt_date = json_dates["Date"]
    except Exception as e:
        return {"docDate": "", "docReceivedDate": ""}
    return {"docDate": "", "docReceivedDate": receipt_date}


def imr_ime_qme_extract_dates(text: str) -> dict[str, str]:
    provider_pattern = r"date\s*of\s*utilization"
    provider_match = re.search(provider_pattern, text, re.IGNORECASE)
    if provider_match:
        text = text[provider_match.start() : provider_match.start() + 75]

    if text == "":
        provider_pattern = r"dated\s*:"
        provider_match = re.search(provider_pattern, text, re.IGNORECASE)
        if provider_match:
            text = text[provider_match.start() : provider_match.start() + 75]
    date_pattern = r"\b\d{1,2}/\d{1,2}/\d{2,4}\b"
    found = re.findall(date_pattern, text)
    if found:
        text = found[0]
    document_date = extract_date(text)
    try:
        json_dates = json.loads(document_date)
        document_date = json_dates["Date"]
    except Exception as e:
        document_date = ""
    return {"docDate": document_date, "docReceivedDate": ""}


def determination_med_auth_extract_dates(text: str) -> dict[str, str]:
    patterns = [r"date\s*:"]
    document_date = ""
    match = re.search(patterns[0], text, re.IGNORECASE)
    if match:
        text = text[match.start() : match.start() + 50]
        match = re.search(BASIC_DATE_PATTERN, text)
        if match:
            document_date = match.group(0)

    receipt_date = ""
    medex_idx = text.lower().find("medex")
    text_found = text[:medex_idx] if medex_idx != -1 else text[:50]
    if len(text_found) > 10:
        receipt_date = extract_date(text_found)
    try:
        json_dates = json.loads(receipt_date)
        receipt_date = json_dates["Date"]
    except Exception as e:
        receipt_date = ""

    return {"docDate": document_date, "docReceivedDate": receipt_date}


DATE_FROI_CASES = {
    "WORKERS COMPENSATION": {"docDate": "DATE PREPARE", "docReceivedDate": "form"},
    "OSHA CASE": {"docDate": "Date (mm/dd/yy)", "docReceivedDate": ""},
    "COLORADO DEPARTMENT OF LABOR AND EMPLOYMENT": {
        "docDate": "Date insurer received first report",
        "docReceivedDate": "WC",
    },
    "STATE OF CALIFORNIA DOCTOR'S FIRST REPORT": {
        "docDate": "Date and hour of first examination or treatment",
        "docReceivedDate": "form",
    },
    "DEPARTMENT OF LABOR & INDUSTRIAL RELATIONS": {
        "docDate": "DATE OF INJURY",
        "docReceivedDate": "WC",
    },
    "Injury and Illness (Recordable or Non Recordable) Incident Report": {
        "docDate": "Created On",
        "docReceivedDate": "MPN",
    },
    "First Report of Injury Virginia Workers' Compensation Commission": {
        "docDate": "",
        "docReceivedDate": "",
    },
}


def illness_injury_report_froi_extract_dates(text: str) -> dict[str, str]:
    for case in DATE_FROI_CASES.keys():
        if case in text:
            if not DATE_FROI_CASES[case]["docDate"]:
                document_date = ""
            else:
                document_date_index = text.lower().find(DATE_FROI_CASES[case]["docDate"].lower())
                document_date = extract_date(text[document_date_index : document_date_index + 200])
            if not DATE_FROI_CASES[case]["docReceivedDate"]:
                receipt_date = extract_date(text[-200:])
            else:
                receipt_date_index = text.lower().find(DATE_FROI_CASES[case]["docReceivedDate"].lower())
                if receipt_date_index < 0:
                    receipt_date = extract_date(text[-200:])
                else:
                    receipt_date = extract_date(text[receipt_date_index:])
            return {"docDate": document_date, "docReceivedDate": receipt_date}
    provider_pattern = r"date\s*administrator\s*notifie[dc] "
    provider_match = regex.search(provider_pattern, text, re.IGNORECASE)
    text_found = ""
    if provider_match:
        text_found = text[provider_match.start() - 5 : provider_match.start() + 55]
    else:
        provider_pattern = r"date\s*\("
        provider_match = re.search(provider_pattern, text, re.IGNORECASE)
        if provider_match:
            text_found = text[provider_match.start() - 5 : provider_match.start() + 55]
    if text_found != "":
        document_date = extract_date(text_found)
    else:
        document_date = ""
    provider_pattern = r"risk\s*management"
    provider_match = re.search(provider_pattern, text, re.IGNORECASE)
    if provider_match:
        text_found = text[provider_match.start() - 5 : provider_match.start() + 55]
    else:
        text_found = text[:150]
    if text_found != "":
        receipt_date = extract_date(text_found)
    else:
        receipt_date = ""
    if not check_year(document_date):
        document_date = ""
    if not check_year(receipt_date):
        receipt_date = ""
    return {"docDate": document_date, "docReceivedDate": receipt_date}


def misc_correspondence_extract_dates(text: str) -> dict[str, str]:
    if text.strip() == "":
        return {"docDate": "", "docReceivedDate": ""}
    provider_pattern = r"risk\s*management"
    provider_match = re.search(provider_pattern, text, re.IGNORECASE)

    if provider_match:
        text_found = text[provider_match.start() - 5 : provider_match.start() + 55]
        if text_found == "":
            receipt_date = ""
        else:
            receipt_date = extract_date(text_found)
    else:
        text_found = text[:350]
        receipt_date = extract_date(text_found)

    try:
        json_dates = json.loads(receipt_date)
        receipt_date = json_dates["Date"]
    except Exception as e:
        receipt_date = ""
    return {"docDate": "", "docReceivedDate": receipt_date}


def other_extract_dates(text: str) -> dict[str, str]:
    if text.strip() == "":
        return {"docDate": "", "docReceivedDate": ""}
    provider_pattern = r"risk\s*management"
    provider_match = re.search(provider_pattern, text, re.IGNORECASE)

    if provider_match:
        text_found = text[provider_match.start() - 5 : provider_match.start() + 55]
        if text_found == "":
            receipt_date = ""
        else:
            receipt_date = extract_date(text_found)
    else:
        text_found = text[:350]
        receipt_date = extract_date(text_found)

    try:
        json_dates = json.loads(receipt_date)
        receipt_date = json_dates["Date"]
    except Exception as e:
        receipt_date = ""
    return {"docDate": "", "docReceivedDate": receipt_date}


def rfa_extract_dates(text: str) -> dict[str, str]:
    patterns_for_document_date = [
        r"date\s*:",
        r"date\s*of\s*examination",
        r"encounter\s*date",
        r"date\s*of\s*visit",
    ]
    document_date = ""
    for pattern in patterns_for_document_date:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            extracted_text = text[match.start() : match.start() + 50]
            if re.search(BASIC_DATE_PATTERN, extracted_text, re.IGNORECASE):
                document_date = extract_date(extracted_text)
                try:
                    json_dates = json.loads(document_date)
                    document_date = json_dates["Date"]
                except Exception as e:
                    document_date = ""
    extracted_text = text[:150]
    receipt_date = extract_date(extracted_text)
    try:
        json_dates = json.loads(receipt_date)
        receipt_date = json_dates["Date"]
    except Exception as e:
        receipt_date = ""
    return {"docDate": document_date, "docReceivedDate": receipt_date}


def supplemental_work_status_extract_dates(text: str) -> dict[str, str]:
    patterns_for_document_date = [
        r"encounter\s*date",
        r"date\s*:",
        r"date\s*of\s*examination",
        r"date\s*of\s*visit",
    ]
    document_date = ""
    for pattern in patterns_for_document_date:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            text = text[match.start() : match.start() + 50]
            if re.search(BASIC_DATE_PATTERN, text, re.IGNORECASE):
                document_date = extract_date(text)
                try:
                    json_dates = json.loads(document_date)
                    document_date = json_dates["Date"]
                except Exception as e:
                    document_date = ""

    text = text[:150]
    receipt_date = extract_date(text)
    try:
        json_dates = json.loads(receipt_date)
        receipt_date = json_dates["Date"]
    except Exception as e:
        receipt_date = ""
    return {"docDate": document_date, "docReceivedDate": receipt_date}


DOCTYPE_TO_FUNCTION_MAPPING_DOCUMENT_DATE_AND_RECEIPT_DATE = {
    "Other": other_extract_dates,
    "RFA": rfa_extract_dates,
    "Medical Records": medical_records_extract_dates,
    "Misc Correspondence": misc_correspondence_extract_dates,
    "IMR/IME/QME": imr_ime_qme_extract_dates,
    "Supplemental/Work Status": supplemental_work_status_extract_dates,
    "Determination - Med Auth": determination_med_auth_extract_dates,
    "Case Management Notes": case_management_notes_extract_dates,
    "Fax": fax_extract_dates,
    "Injury/Illness/FROI": illness_injury_report_froi_extract_dates,
}
