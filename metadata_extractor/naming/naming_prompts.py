CLAIM_NUMBER_EXTRACTION_PROMPT = """
Given the OCR text, extract the claim/policy number located in the text.

Format the extracted information into a JSON object with "Claim_number" key, adhering strictly to the following rules:
1. "Claim_number": Extract the claim/policy number, search for it only around the words "claim" and "policy" or before the words "carrier/administrator".
2. Do not ever include any additional information, such as address, phone number etc.!

You should always output the JSON information only, without any additional comments. Otherwise, you will be punished.

You must never output any explanation after the JSON in your response. It is strictly prohibited.

Use this empty JSON template to fill in:

{
  "Claim_number": "",
}

Now, fill in the template by extracting the required information from the given text. The text is given here:
"""

PATIENT_NAME_EXTRACTION_PROMPT = """
Given the OCR text, extract a specific piece of information: the patient's full name: first name, middle name (if present) and last name.

Format the extracted information into a JSON object with "Patient_name" key, adhering strictly to the following rules:
1. "Patient_name": Extract the patient's full name: name and surname (and middlename if exists) mentioned around the words 'patient', 'employee', 'regarding employee'.
2. Do not ever include any additional information, such as address, phone number etc.!

You should always output the JSON information only, without any additional comments. Otherwise, you will be punished.

You must never output any explanation after the JSON in your response. It is strictly prohibited.

"Patient_name" should be in the format <first name, middle name, last name>.
For example, FirstName MiddleName LastName; therefore, "Patient_name" has to have maximum three words.

Use this empty JSON template to fill in:

{
  "Patient_name": "",
}

Now, fill in the template by extracting the required information from the given text. The text is given here:

"""

PROMPT_FOR_INSERTING_TEXT_BELOW = """
-------------------
{ocr_text}
-------------------
"""

SENDER_NAME_EXTRACTION_PROMPT = """
    Given the OCR text, extract the sender's name mentioned in the text.
    Format the extracted information into a JSON object with 'Sender_name' key, adhering strictly to the following rules:
    1. 'Sender_name': Extract the sender's name mentioned in the text. It should be a company that the document was sent from or faxed from. In case there is no name of the company, then sender's name is a name of the person who sent the document. This will be typically located after the word "From: ". If there is a person's name after the word "Provider", then this name should be used as a result. If there is both a company name and a person's name, then the company name should be extracted.
    2. Do not ever include any additional information, such as address, phone number etc.!
    You should always output the JSON information only, without any additional comments.
    Otherwise, you will be punished.
    You must never output any explanation after the JSON in your response. It is strictly prohibited.
    Use this empty JSON template to fill in: {{ 'Sender_name': '' }}
    Now, fill in the template by extracting the required information from the given text.
    The text is given here:
    -----------------
    {result}
    -----------------
"""

DATE_EXTRACTION_PROMPT = """
    Given the OCR text, extract the date name mentioned in the text.
    Format the extracted information into a JSON object with 'Date' key, adhering strictly to the following rules:
    1. 'Date': Extract the date mentioned in the text.
    When extracting the date which includes days of the week, you must not include the day of the week in the extracted date.
    For example, instead of 'Monday, January 12, 2022', you should extract 'January 12, 2022'.
    2. Do not ever include any additional information, such as address, phone number etc.!
    You should always output the JSON information only, without any additional comments.
    Otherwise, you will be punished.
    You must never output any explanation after the JSON in your response. It is strictly prohibited.
    Use this empty JSON template to fill in: {{ 'Date': '' }}
    Now, fill in the template by extracting the required information from the given text.
    The text is given here:
    -----------------
    {result}
    -----------------
"""

PROVIDER_OR_FACILITY_NAME_EXTRACTION_PROMPT = """
    Given the OCR text, extract the provider's name mentioned in the text.
    Format the extracted information into a JSON object with 'Provider_name' key, adhering strictly to the following rules:
    1. 'Provider_name': Extract the provider's name mentioned in the text. It should be a provider or a facility that the document was sent from or faxed from. In case there is no name of the company, then provider's name is a name of the facility who sent the document. This will be typically located after the word "From ". If there is a person's name after the word "Provider", then this name should be used as a result. If there is both a company name and a person's name, then the company name should be extracted.
    2. Do not ever include any additional information, such as address, phone number etc.!
    You should always output the JSON information only, without any additional comments.
    Otherwise, you will be punished.
    You must never output any explanation after the JSON in your response. It is strictly prohibited.
    Use this empty JSON template to fill in: {{ 'providerOrFacilityName': '' }}
    Now, fill in the template by extracting the required information from the given text.
    The text is given here:
    -----------------
    {result}
    -----------------
"""
