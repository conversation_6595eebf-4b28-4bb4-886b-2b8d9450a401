# Determination_Med_Auth

DETERMINATION_MED_AUTH_PATIENT_NAME_PATTERNS = [
    r"regar[dt]ing\s*empl[oa]yee.{0,75}",
    r"claimant(\s*\(\s*s\s*\)|)\s*name.{0,75}",
    r"by\s*facsimile.{0,75}",
    r"employee\s*:.{0,30}",
    r"claimant\s*:.{0,30}",
    r"patient\s*:.{0,30}",
    r"claimant\s*.{0,30}",
    r"attn:\s*.{0,20}",
]

DETERMINATION_MED_AUTH_CLAIM_NUMBER_PATTERNS = [
    r"claim\s*number.{0,30}",
    r"claim\s*.{0,30}",
]

# MiscCorrespondence

MISC_CORRESPONDENCE_PATIENT_NAME_PATTERNS = [
    r"employee\s*advocate\s*introduction\s*letter.{0,45}",
    r"mmn\s*introduction\s*letter.{0,40}",
    r"appointment\s*letter.{0,40}",
    r"mmn\s*transfer\s*letter.{0,50}",
    r"phone\s*contact\s*letter.{0,35}",
    r"claimant:.{0,50}",
    r"claimants\s*name.{0,40}",
    r"to\s*whom\s*it\s*may\s*concern.{0,100}",
    r"this\s*is\s*to\s*certify\s*that.{0,100}",
    r"appointment\s*reminder.{0,100}",
    r"witness.{0,40}",
    r"subject:.{0,40}",
    r"employee\s*:.{0,40}",
    r"re\s*:.{0,30}",
    r"patient\s*:.{0,40}",
    r"name\s*:.{0,25}",
]

MISC_CORRESPONDENCE_CLAIM_NUMBER_PATTERNS = [
    r"claim\s*number.{0,40}",
    r"claim\s*#.{0,40}",
    r"subject.{0,40}",
    r"claim\s*:.{0,40}",
]

# CaseManagementNotes

CASE_MANAGEMENT_NOTES_CLAIM_NUMBER_PATTERNS = [
    r"polic(y|)\s*number.{0,25}",
    r"claim\s*#.{0,25}",
    r"claim\s*number.{0,25}",
    r"claim\s*no.{0,25}",
    r"claim\s*:.{0,25}",
]

CASE_MANAGEMENT_NOTES_PATIENT_NAME_PATTERNS = [
    r".{0,40}polic(y|)\s*number",
    r".{0,40}claim\s*#",
    r".{0,40}claim\s*number",
    r".{0,40}claim\s*no",
    r".{0,40}claim\s*:",
]

# Fax

FAX_PATIENT_NAME_PATTERNS = [
    r"employeee\s*name.{0,30}",
    r"pati[eb]nt:.{0,50}",
    r"claimant.{0,30}",
    r"recipient:.{0,30}",
    r"patient\s*name.{0,30}",
]

FAX_CLAIM_NUMBER_PATTERNS = [
    r"claim\s*#.{0,20}",
    r"claim\s*num[b8]er.{0,20}",
    r"cl\s*#.{0,20}",
    r"claim\s*.{0,20}",
]

# Illness_Injury_Report_FROI

ILLNESS_INJURY_REPORT_FROI_PATIENT_NAME_PATTERNS = [
    r"patient\s*name.{0,150}",
    r"employee\s*\/\s*wage.{0,150}",
    r"e[mxv]plo[yv]ee\s*na[mn]e.{0,150}",
    r"name\s*of\s*injured\s*worker.{0,150}",
    r"employee's.{0,150}",
    r"re:.{0,150}",
]

ILLNESS_INJURY_REPORT_FROI_CLAIM_NUMBER_PATTERNS = [
    r".{0,40}carrier\s*/\s*administrator.{0,20}",
    r"claim\s*number.{0,30}",
    r"claim\s*no\s*:.{0,30}",
]

# Medical Records

MEDICAL_RECORDS_PATIENT_NAME_PATTERNS = [
    r"patient\s*name.{0,35}",
    r"employee\s*name.{0,35}",
    r"client\s*.{0,35}",
    r"employee\s*information.{0,35}",
    r"employee\s*:.{0,35}",
    r"re\s*:.{0,35}",
    r"name\s*:.{0,35}",
    r".{0,30}patient\s*#",
    r"patient\s*:.{0,50}",
    r"guarantor\s*:.{0,35}",
    r"claimant.{0,25}",
]

MEDICAL_RECORDS_CLAIM_NUMBER_PATTERNS = [
    r"cla[il]m\s*number.{0,25}",
    r"claim\s*#.{0,25}",
    r"claim\s*num.{0,25}",
    r"claim\s*no.{0,25}",
    r"claims\s*manage[mn]ent.{0,40}",
    # r"\bclaim\b.{0,30}",
]

# Supplemental_WorkStatus

SUPPLEMENTAL_WORK_STATUS_CLAIM_NUMBER_PATTERNS = [
    r".{0,50}claims\s*administrator\s*name.{0,20}",
    r".{0,30}c[lt]a[ilt]m\s*num.{0,30}",
    r"clam\s*number.{0,30}",
    r"claim\s*?:no.{0,25}",
    r"cla[il]m\s*#.{0,25}",
    r"\bclaim\b.{0,25}",
    r"cl\s*#\s*.{0,25}",
]

SUPPLEMENTAL_WORK_STATUS_PATIENT_NAME_PATTERNS = [
    r"patient\s*:.{0,50}",
    r"patient\s*name.{0,35}",
    r"employee\s*name.{0,35}",
    r"employee\s*information.{0,35}",
    r"employee\s*:.{0,35}",
    r"patient.{0,35}",
    r"page\s*\d{1,2}\s*of\s*\d{1,2}.{0,50}",
    r"page\s*\d{1,2}/\d{1,2}.{0,50}",
    r"name\s*:.{0,35}",
    r".{0,30}patient\s*#",
    r"guarantor\s*:.{0,35}",
    r"claimant.{0,25}",
    r"re\s*:.{0,35}",
]

# Other

OTHER_PATIENT_NAME_PATTERNS = [
    r".{0,20}employee.{0,40}",
    r".{0,20}claimant.{0,40}",
    r".{0,20}patient.{0,40}",
    r".{0,20}subject.{0,40}",
]
OTHER_CLAIM_NUMBER_PATTERNS = [
    r".{0,20}claim\s*number.{0,40}",
    r".{0,20}claim\s*#.{0,40}",
    r".{0,20}claim\s*no.{0,40}",
    r".{0,20}subject.{0,40}",
]

# IMR_IME_QME

IMR_IME_QME_CLAIM_NUMBER_PATTERNS = [
    r"c[li]?a[i]?m\s*n[ua]?mber.{0,30}",
    r"claim\s*no.{0,30}",
]

IMR_IME_QME_PATIENT_NAME_PATTERNS = [
    r"employee\s*name.{0,45}(?:(?!address).){0,45}",
    r"case\s*name.{0,45}(?:(?!address).){0,45}",
]


RFA_CLAIM_NUMBER_PATTERNS = [
    r"claim\s*number{0,30}",
    r"claim\s*#{0,30}",
    r"claim{0,30}",
]

RFA_PATIENT_NAME_PATTERNS = [
    r"pati[eb]nt{0,50}",
    r"employee\s*information{0,50}",
    r"l[as]st\s*firs[t ]\s*middle{0,50}",
    r"first\s*middle{0,50}",
    r"last\s*name{0,50}",
    r"first\s*name{0,50}",
    r"[nN][aAo][mM][eE]{0,50}",
    r"patient\s*id{0,50}",
    r"patient{0,50}",
    r"address{0,50}",
]

DOCTYPE_TO_PATTERNS_MAPPING = {
    "Case Management Notes": (
        CASE_MANAGEMENT_NOTES_CLAIM_NUMBER_PATTERNS,
        CASE_MANAGEMENT_NOTES_PATIENT_NAME_PATTERNS,
    ),
    "Determination - Med Auth": (
        DETERMINATION_MED_AUTH_CLAIM_NUMBER_PATTERNS,
        DETERMINATION_MED_AUTH_PATIENT_NAME_PATTERNS,
    ),
    "Fax": (FAX_CLAIM_NUMBER_PATTERNS, FAX_PATIENT_NAME_PATTERNS),
    "Injury/Illness/FROI": (
        ILLNESS_INJURY_REPORT_FROI_CLAIM_NUMBER_PATTERNS,
        ILLNESS_INJURY_REPORT_FROI_PATIENT_NAME_PATTERNS,
    ),
    "IMR/IME/QME": (
        IMR_IME_QME_CLAIM_NUMBER_PATTERNS,
        IMR_IME_QME_PATIENT_NAME_PATTERNS,
    ),
    "Medical Records": (
        MEDICAL_RECORDS_CLAIM_NUMBER_PATTERNS,
        MEDICAL_RECORDS_PATIENT_NAME_PATTERNS,
    ),
    "Misc Correspondence": (
        MISC_CORRESPONDENCE_CLAIM_NUMBER_PATTERNS,
        MISC_CORRESPONDENCE_PATIENT_NAME_PATTERNS,
    ),
    "Other": (OTHER_CLAIM_NUMBER_PATTERNS, OTHER_PATIENT_NAME_PATTERNS),
    "Supplemental/Work Status": (
        SUPPLEMENTAL_WORK_STATUS_CLAIM_NUMBER_PATTERNS,
        SUPPLEMENTAL_WORK_STATUS_PATIENT_NAME_PATTERNS,
    ),
    "RFA": (RFA_CLAIM_NUMBER_PATTERNS, RFA_PATIENT_NAME_PATTERNS),
}
