import base64
import json
import os

from naming.claim_number_patient_name_context_functions import DOCTYPE_TO_CONTEXT_FUNCTIONS_MAPPING
from naming.document_date_receipt_date_extraction import DOCTYPE_TO_FUNCTION_MAPPING_DOCUMENT_DATE_AND_RECEIPT_DATE
from naming.document_state_extraction import DOCTYPE_TO_DOCUMENT_STATE_MAPPING, DOCTYPES_WITH_STATE
from dotenv import load_dotenv
from naming.naming_regex_patterns import DOCTYPE_TO_PATTERNS_MAPPING
from ollama import Client
from naming.postprocess_naming_fields import (
    postprocess_claim_number,
    postprocess_patient_name,
    postprocess_provider_or_facility_name,
    postprocess_sender_name,
    reformat_date,
)
from naming.prompts import CLAIM_NUMBER_EXTRACTION_PROMPT, PATIENT_NAME_EXTRACTION_PROMPT, PROMPT_FOR_INSERTING_TEXT_BELOW
from naming.provider_or_facility_name_extraction import DOCTYPE_TO_FUNCTION_MAPPING_PROVIDER_OR_FACILITY_NAME
from naming.sender_name_extraction import DOCTYPE_TO_FUNCTION_MAPPING_SENDER_NAME
from utils_ import perform_name_splitting

load_dotenv()

# BASE_AUTH_USER_AND_PASSWORD = os.environ["BASE_AUTH_USER_AND_PASSWORD"]
SERVER_HOST = os.environ["SERVER_HOST"]
SERVER_PORT = os.environ["SERVER_PORT"]

# BASE64_STRING = base64.b64encode(BASE_AUTH_USER_AND_PASSWORD.encode()).decode("ascii")
# AUTH_HEADER = {"Authorization": f"Basic {BASE64_STRING}"}

# OLLAMA_CLIENT = Client(host=f"{SERVER_HOST}:{SERVER_PORT}", headers=AUTH_HEADER, timeout=360.0, verify=False)
OLLAMA_CLIENT = Client(host=f"{SERVER_HOST}:{SERVER_PORT}", timeout=360.0, verify=False)

OLLAMA_OPTIONS = {
    "num_predict": 150,
    "temperature": 0,
    # "top_k": 5,
    # "top_p": 0.1,
    # "mirostat_tau": 0.5,
}


MODEL_NAME = "mistral-nemo:12b-instruct-2407-q5_K_M_medical_assistant"


def process_with_ollama(input_prompt: str) -> str:
    response = OLLAMA_CLIENT.generate(model=MODEL_NAME, prompt=input_prompt, options=OLLAMA_OPTIONS)
    return response["response"]


def process_claim_number(text: str, document_type: str) -> str:
    context = DOCTYPE_TO_CONTEXT_FUNCTIONS_MAPPING[document_type][0](
        text, patterns=DOCTYPE_TO_PATTERNS_MAPPING[document_type][0]
    )

    if context:
        claim_number_context = PROMPT_FOR_INSERTING_TEXT_BELOW.format(ocr_text=context)
        input_prompt = f"{CLAIM_NUMBER_EXTRACTION_PROMPT}{claim_number_context}"
        response = process_with_ollama(input_prompt)
        response = postprocess_claim_number(response)
    else:
        response = ""

    try:
        claim_number = "" if not response else json.loads(response)["Claim_number"]
    except Exception as e:
        claim_number = ""

    return claim_number


def process_patient_name(text: str, document_type: str) -> str:
    context = DOCTYPE_TO_CONTEXT_FUNCTIONS_MAPPING[document_type][1](
        text, patterns=DOCTYPE_TO_PATTERNS_MAPPING[document_type][1]
    )

    if context:
        patient_name_context = PROMPT_FOR_INSERTING_TEXT_BELOW.format(ocr_text=context)
        input_prompt = f"{PATIENT_NAME_EXTRACTION_PROMPT}{patient_name_context}"
        response = process_with_ollama(input_prompt)
        response = postprocess_patient_name(response)
    else:
        response = ""

    try:
        patient_name = "" if not response else json.loads(response)["Patient_name"]
    except Exception as e:
        patient_name = ""

    return patient_name


def process_sender_name(text: str, document_type: str) -> str:
    sender_name_extraction_function = DOCTYPE_TO_FUNCTION_MAPPING_SENDER_NAME[document_type]
    sender_name = sender_name_extraction_function(text)
    sender_name = postprocess_sender_name(sender_name)

    try:
        sender_name = json.loads(sender_name)["senderName"]
    except Exception as e:
        sender_name = ""
    return sender_name


def process_date(text: str, document_type: str) -> tuple[str, str]:
    date_extraction_function = DOCTYPE_TO_FUNCTION_MAPPING_DOCUMENT_DATE_AND_RECEIPT_DATE[document_type]
    dates = date_extraction_function(text)

    try:
        document_date = dates["docDate"]
        receipt_date = dates["docReceivedDate"]
    except Exception as e:
        document_date = ""
        receipt_date = ""

    document_date = reformat_date(document_date)
    receipt_date = reformat_date(receipt_date)

    return document_date, receipt_date


def process_document_state(text: str, document_type: str) -> str:
    if not text:
        return ""

    doctype_state_function = DOCTYPE_TO_DOCUMENT_STATE_MAPPING[document_type]
    state = doctype_state_function(text)
    return state


def process_provider_or_facility_name(text: str, document_type: str) -> str:
    provider_or_facility_name_extraction_function = DOCTYPE_TO_FUNCTION_MAPPING_PROVIDER_OR_FACILITY_NAME[document_type]
    provider_or_facility_name = provider_or_facility_name_extraction_function(text)
    provider_or_facility_name = postprocess_provider_or_facility_name(provider_or_facility_name)

    try:
        provider_or_facility_name = json.loads(provider_or_facility_name)["providerOrFacilityName"]
    except Exception as e:
        provider_or_facility_name = ""
    return provider_or_facility_name


def extract_naming_fields(result: list[str], document_type: str) -> dict:

    all_strings = []
    for page in result:
        if not page:
            continue
        for string_data in page:
            string = string_data[-1][0]
            all_strings.append(string)

    text = " ".join(all_strings)
    # text = result

    patient_name_response = process_patient_name(text, document_type)
    claim_number_response = process_claim_number(text, document_type)

    sender_name_response = process_sender_name(text, document_type)
    document_date, receipt_date = process_date(text, document_type)

    if document_type in ["Fax", "Misc Correspondence", "Other"]:

        output = {
            "claimNumber": claim_number_response,
            "patientName": patient_name_response,
            "senderName": sender_name_response,
            "docReceivedDate": receipt_date,
        }

    elif document_type == "Medical Records":

        provider_or_facility_name = process_provider_or_facility_name(text, document_type)

        output = {
            "claimNumber": claim_number_response,
            "patientName": patient_name_response,
            "senderName": sender_name_response,
            "docReceivedDate": receipt_date,
            "providerOrFacilityName": provider_or_facility_name,
        }
    elif document_type in DOCTYPES_WITH_STATE:
        document_state = process_document_state(all_strings, document_type)

        output = {
            "claimNumber": claim_number_response,
            "patientName": patient_name_response,
            "senderName": sender_name_response,
            "docDate": document_date,
            "docReceivedDate": receipt_date,
            "docState": document_state,
        }

    else:
        output = {
            "claimNumber": claim_number_response,
            "patientName": patient_name_response,
            "senderName": sender_name_response,
            "docDate": document_date,
            "docReceivedDate": receipt_date,
        }

    split_name = perform_name_splitting(output["patientName"])
    output.update(split_name)

    return output
