import re

from pyzipcode import ZipCodeDatabase

zcdb = ZipCodeDatabase()

state_abbreviations = [
    "AL",
    "AK",
    "AZ",
    "AR",
    "CA",
    "CO",
    "CT",
    "DE",
    "FL",
    "GA",
    "HI",
    "ID",
    "IL",
    "IN",
    "IA",
    "KS",
    "KY",
    "LA",
    "ME",
    "MD",
    "MA",
    "MI",
    "MN",
    "MS",
    "MO",
    "MT",
    "NE",
    "NV",
    "NH",
    "NJ",
    "NM",
    "NY",
    "NC",
    "ND",
    "OH",
    "OK",
    "OR",
    "PA",
    "RI",
    "SC",
    "SD",
    "TN",
    "TX",
    "UT",
    "VT",
    "VA",
    "WA",
    "WV",
    "WI",
    "WY",
]

states = {
    "alabama": "AL",
    "alaska": "AK",
    "arizona": "AZ",
    "arkansas": "AR",
    "california": "CA",
    "colorado": "CO",
    "connecticut": "CT",
    "delaware": "DE",
    "florida": "FL",
    "georgia": "GA",
    "hawaii": "HI",
    "idaho": "ID",
    "illinois": "IL",
    "indiana": "IN",
    "iowa": "IA",
    "kansas": "KS",
    "kentucky": "KY",
    "louisiana": "LA",
    "maine": "ME",
    "maryland": "MD",
    "massachusetts": "MA",
    "michigan": "MI",
    "minnesota": "MN",
    "mississippi": "MS",
    "missouri": "MO",
    "montana": "MT",
    "nebraska": "NE",
    "nevada": "NV",
    "new hampshire": "NH",
    "new jersey": "NJ",
    "new mexico": "NM",
    "new york": "NY",
    "north carolina": "NC",
    "north dakota": "ND",
    "ohio": "OH",
    "oklahoma": "OK",
    "oregon": "OR",
    "pennsylvania": "PA",
    "rhode island": "RI",
    "south carolina": "SC",
    "south dakota": "SD",
    "tennessee": "TN",
    "texas": "TX",
    "utah": "UT",
    "vermont": "VT",
    "virginia": "VA",
    "washington": "WA",
    "west virginia": "WV",
    "wisconsin": "WI",
    "wyoming": "WY",
}

cases = [
    "PHYSICIAN'S DESCRIPTION OF MODIFIED DUTIES",
    "Anesthesiology and Pain Management",
    "Medical Group Inc",
    "Reliant Urgent Care - Montebello",
    "Concentra Medical Centers",
    "ORTHOPAEDIC MEDICAL GROUP",
    "BOARD CERTIFIED ORTHOPEDIC SURGEON",
    "SPORTS MEDICINE GROUP",
    "Work Activity Status",
    "Disability Status Form",
    "Bayside Medical Center",
    "MEDICAL GROUP",
    "pr-2",
    "pR-2",
    "Pr-2",
    "PR-2",
    "ST",
    "State:",
    "Title:",
    "PO BOX",
    "P.O. Box",
]


def regex_state(text: list[str], patterns: str) -> str:
    state = []
    for line in text:
        for pattern in patterns:
            matches = re.findall(pattern, line)
            if not matches:
                continue
            for i in matches:
                if i.replace(",", "").replace(" ", "").upper() in state_abbreviations:
                    state.append(i.replace(",", "").replace(" ", "").upper())
    return state


def regex_zipcode(text: list[str]) -> str:
    zip_code_pattern = r"\b\d{5}(?:-\d{4})?\b"
    zip_codes = [re.findall(zip_code_pattern, row) for row in text if re.findall(zip_code_pattern, row) != []]
    if not zip_codes:
        return ""
    try:
        if "-" in zip_codes[0][0]:
            zip_codes = zcdb[zip_codes[0][0].split("-")[0]].state
        else:
            zip_codes = zcdb[zip_codes[0][0]].state
    except:
        return ""
    return zip_codes


def get_document_state_determination_med_auth(text: list[str]) -> str:

    if "PROOF OF SERVICE" in text[:10]:
        state_pattern = [r",\s[A-Z]{2}\b"]
    if "Notice of Adverse Determination" in text[:10]:
        state_pattern = [r",\s[A-Z]{2}\b"]
    if [line for line in text[:10] if "Authorization for" in line] is not None:
        employee_index = next((i for i, word in enumerate(text) if "Claimant" in word), None)
        reference_index = next((i for i, word in enumerate(text) if "Employer" in word), None)
        if None not in (employee_index, reference_index):
            text = text[employee_index:reference_index]
        state_pattern = [
            r"\b(?!TO|IX|MI|PR|ID|OF|IN|AM|PM|MD|TR)[A-Z]{2}\b",
            r",\s[A-Z]{2}\b",
        ]
    if "NOTICE OF UTILIZATION REVIEW DETERMINATION" in text[:10]:
        state_pattern = [
            r"\b(?!TO|IX|MI|PR|ID|OF|IN|AM|PM|MD|TR)[A-Z]{2}\b",
            r",\s[A-Z]{2}\b",
        ]

        employee_index = next((i for i, word in enumerate(text) if "Employee" in word), None)
        reference_index = next((i for i, word in enumerate(text) if "Reference" in word), None)
        if None not in (employee_index, reference_index):
            text = text[employee_index:reference_index]
    else:
        state_pattern = [
            r"\b(?!TO|IX|MI|PR|ID|OF|IN|AM|PM|MD|TR)[A-Z]{2}\b",
            r",\s[A-Z]{2}\b",
        ]

    state = regex_state(text, state_pattern)
    if not state:
        state = ""
    else:
        state = state[0]

    zip_codes = regex_zipcode(text)
    if state == zip_codes:
        return state
    else:
        return state


def get_document_state_illness_injury_report_froi(text: list[str]) -> str:
    if "WORKERS COMPENSATION - FIRST REPORT OF INJURY OR ILLNESS" in text[:5]:
        for line in text:
            if len(line) == 2:
                return line
    for line in text[:5]:
        line = line.lower()
        if line.startswith("state of") and len(line.split()) == 3 and line.split()[2] in states:
            return states[line.split()[2]]

        state = [row for row in [word for word in line.split()] if row in states]

        if len(state) > 0:
            return states[state[0]]
    return "CA"


def get_document_state_imr_ime_qme(text: list[str]) -> str:
    test = [line.lower().split() for line in text[:10]]
    for line in test:
        for word in line:
            if word.replace(" ", "").replace(",", "") in states:
                return states[word.replace(" ", "").replace(",", "")]

    state_pattern = [r"\b[A-Z]{2}\b"]
    state = regex_state(text[:10], state_pattern)
    if len(state) > 0:
        return state[0]
    for line in text[:5]:
        line = line.lower()
        if line.startswith("state of") and len(line.split()) == 3 and line.split()[2] in states:
            return states[line.split()[2]]

        state = [row for row in [word for word in line.split()] if row in states]

        if len(state) > 0:
            return states[state[0]]

    return ""


def get_document_state_supplemental_work_status(text: list[str]) -> str:

    state_pattern = [
        r",[A-Z]{2}",
        r"[A-Z]\s{2}",
        r"\s[A-Z]\s{2}",
        r"\b[A-Z]{2}\b",
        r"\s[A-Z]{2}-",
        r",\s[A-Z]\s{2}",
        r"[A-Z]{2},",
        r",\s[A-Z]{1}[a-z]{1},",
        r"\s[A-Z]{1}[a-z]{1}\s",
    ]
    for line in text[:20]:
        line = line.lower()
        if line.startswith("state of") and len(line.split()) == 3 and line.split()[2] in states:
            return states[line.split()[2]]

        state = [row for row in [word for word in line.split()] if row in states]

        if len(state) > 0:
            return states[state[0]]
        for word in line:
            if word.replace(" ", "").replace(",", "") in states:
                return states[word.replace(" ", "").replace(",", "")]

    for case in cases:
        start = next((i for i, word in enumerate(text) if case in word), None)
        if case == "ORTHOPAEDIC MEDICAL GROUP OF RIVERSIDE, INC":
            start = start + 1
        if start is not None:
            break

    if start is not None:
        state = regex_state(text[start:], state_pattern)
    else:
        state = regex_state(text[2:], state_pattern)

    if not state:
        return "CA"
    else:
        return state[0]


def get_document_state_rfa(text: list[str]) -> str:
    return ""


DOCTYPE_TO_DOCUMENT_STATE_MAPPING = {
    "Determination - Med Auth": get_document_state_determination_med_auth,
    "Injury/Illness/FROI": get_document_state_illness_injury_report_froi,
    "Supplemental/Work Status": get_document_state_supplemental_work_status,
    "IMR/IME/QME": get_document_state_imr_ime_qme,
    "RFA": get_document_state_rfa,
}

DOCTYPES_WITH_STATE = [
    "Determination - Med Auth",
    "Injury/Illness/FROI",
    "Supplemental/Work Status",
    "IMR/IME/QME",
    "RFA",
]
