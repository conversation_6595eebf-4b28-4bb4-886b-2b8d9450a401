import json
import re
from datetime import datetime

INCORRECT_CLAIM_NUMBER_CASES = [
    "**********",
    "123456789",
    "12345678",
    "1234567",
    "123456",
    "12345",
    "**********",
    "987654321",
    "98765432",
    "9876543",
    "987654",
    "**********",
    "098765432",
    "09876543",
    "0987654",
]


PATIENT_NAME_PATTERNS_TO_REMOVE = [
    r"name",
    r"surname",
    r"first",
    r"last",
    r"male",
    r"female",
    r"patient",
]


def postprocess_abbreviations_in_response(text: str) -> str:
    words = text.split()

    formatted_words = []

    ABBREVIATIONS = ["Jr", "Sr", "Jr.", "Sr.", "I", "II", "III", "IV", "V"]

    abbreviations = ABBREVIATIONS

    for word in words:
        if len(word) < 3:
            if word in abbreviations:
                formatted_words.append(word)
            else:
                continue
        else:
            formatted_words.append(word)

    formatted_name = " ".join(formatted_words)
    return formatted_name


def find_jane_or_john_doe(text: str) -> bool:
    patterns = [r"Jane\s+Doe", r"<PERSON>\s+Doe"]

    matches = [re.search(pattern, text, re.IGNORECASE) for pattern in patterns]

    if any(matches):
        return True
    else:
        return False


def process_json_llm_response(text: str) -> str:
    pattern = r"\{([^}]*)\}"
    match = re.search(pattern, text)
    if match:
        result = match.group(0).strip().replace("\n", "")
    else:
        return None
    result = result.strip()

    # find the index of the last occurrence of "
    last_quote_index = result.rfind('"')

    # find the index of the last occurrence of comma
    last_comma_index = result.rfind(",")

    # remove the comma if found
    if last_comma_index > last_quote_index:
        result = result[:last_comma_index] + result[last_comma_index + 1 :]

    # remove whitespace between the first curly bracket and first double quote
    first_curly_bracket_index = result.find("{")
    first_quote_index = result.find('"')
    if first_curly_bracket_index != -1 and first_quote_index != -1 and first_curly_bracket_index < first_quote_index:
        result = result[: first_curly_bracket_index + 1] + result[first_quote_index:]
    result = result.replace("\n", "").replace("\t", "")
    return result


def postprocess_patient_name(text: str) -> str:
    result = process_json_llm_response(text)

    try:
        json_result = json.loads(result)
        if "Patient_name" in json_result.keys():
            json_result["Patient_name"] = json_result["Patient_name"].strip()
            patient_name = json_result["Patient_name"]

            patient_name = re.sub(
                "|".join(PATIENT_NAME_PATTERNS_TO_REMOVE),
                "",
                patient_name,
                flags=re.IGNORECASE,
            ).strip()

            patient_name = re.sub(r"\s+", " ", patient_name)

            not_specified_pattern = r"not\s*specified"
            given_text_pattern = r"given\s*text"
            if re.search(not_specified_pattern, patient_name, re.IGNORECASE) or re.search(
                given_text_pattern, patient_name, re.IGNORECASE
            ):
                return None

            if patient_name.startswith(",") or patient_name.startswith("."):
                patient_name = patient_name[1:]

            if patient_name.endswith(",") or patient_name.endswith("."):
                patient_name = patient_name[:-1]

            if find_jane_or_john_doe(patient_name):
                return None

            if len(patient_name) < 4 or len(patient_name) > 30:
                return None

            json_result["Patient_name"] = patient_name

            return str(json_result).replace("'", '"')
    except Exception as e:
        return None
    return result


def postprocess_claim_number(text: str) -> str:
    result = process_json_llm_response(text)

    try:
        json_result = json.loads(result)
        if "Claim_number" in json_result.keys():
            claim_number = json_result["Claim_number"]

            if claim_number in INCORRECT_CLAIM_NUMBER_CASES:
                return None

            not_specified_pattern = r"not\s*specified"
            given_text_pattern = r"given\s*text"
            if re.search(not_specified_pattern, claim_number, re.IGNORECASE) or re.search(
                given_text_pattern, claim_number, re.IGNORECASE
            ):
                return None

            # if there is a comma in the claim number, take everything before the comma
            if "," in claim_number:
                claim_number = claim_number.split(",")[0]

            # claim_number = re.sub(r"[^a-zA-Z\s-\d]", "", claim_number)

            # if the claim number is less than 4 characters or more than 18 characters, return None
            if len(claim_number) < 4 or len(claim_number) > 18:
                return None

            # if there are more than 4 alpha characters in the claim number, return None
            if sum(1 for char in claim_number if char.isalpha()) > 4:
                return None

            # if there are more than 2 hyphens in the claim number, return None
            if claim_number.count("-") > 2:
                return None

            claim_number = claim_number.strip()

            # if there are multiple spaces in the claim number, replace them with a hyphen
            if " " in claim_number:
                claim_number = "-".join(claim_number.split())

            json_result["Claim_number"] = claim_number.strip()
            return str(json_result).replace("'", '"')
    except Exception as e:
        return None
    return result


def postprocess_sender_name(text: str) -> str:
    result = process_json_llm_response(text)

    try:
        json_result = json.loads(result)
        if "senderName" in json_result.keys():
            json_result["senderName"] = json_result["senderName"].strip()
            sender_name = json_result["senderName"]
            if re.match(r"e(m|n)ployer\s*", sender_name, re.IGNORECASE):
                return None
            if find_jane_or_john_doe(sender_name):
                return None

            not_specified_pattern = r"not\s*specified"
            given_text_pattern = r"given\s*text"
            if re.search(not_specified_pattern, sender_name, re.IGNORECASE) or re.search(
                given_text_pattern, sender_name, re.IGNORECASE
            ):
                return None

            json_result["senderName"] = sender_name
            return str(json_result).replace("'", '"')
    except Exception as e:
        return None
    return result


def postprocess_provider_or_facility_name(text: str) -> str:
    result = process_json_llm_response(text)

    try:
        json_result = json.loads(result)
        if "providerOrFacilityName" in json_result.keys():
            json_result["providerOrFacilityName"] = json_result["providerOrFacilityName"].strip()
            sender_name = json_result["providerOrFacilityName"]
            if re.match(r"e(m|n)ployer\s*", sender_name, re.IGNORECASE):
                return None
            if find_jane_or_john_doe(sender_name):
                return None

            not_specified_pattern = r"not\s*specified"
            given_text_pattern = r"given\s*text"
            if re.search(not_specified_pattern, sender_name, re.IGNORECASE) or re.search(
                given_text_pattern, sender_name, re.IGNORECASE
            ):
                return None

            json_result["providerOrFacilityName"] = sender_name
            return str(json_result).replace("'", '"')
    except Exception as e:
        return None
    return result


def reformat_date(text_with_date: str) -> str:
    if not text_with_date:
        return ""

    date_patterns_adjusted = [
        r"\b(0?[1-9]|1[012])[-/](?:0?[1-9]|[12][0-9]|3[01])[-/](\d{4})\b",  # MM/DD/YYYY or MM-DD-YYYY
        r"\b(\d{4})[-/](0?[1-9]|1[012])[-/](?:0?[1-9]|[12][0-9]|3[01])\b",  # YYYY/MM/DD or YYYY-MM-DD
        r"\b\d{4}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(?:0?[1-9]|[12][0-9]|3[01])\b",  # YYYY Month DD
        r"\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(?:0?[1-9]|[12][0-9]|3[01])\s+\d{4}\b",  # Month DD YYYY
        r"\b(January|February|March|April|May|June|July|August|September|October|November|December)\s+(0?[1-9]|[12][0-9]|3[01]),\s+\d{4}\b",  # Month DD, YYYY
    ]

    for pattern in date_patterns_adjusted:
        match = re.search(pattern, text_with_date)
        if match:
            date_str = match.group(0).replace("/", "-")

            date_formats = [
                "%m-%d-%Y",
                "%Y-%m-%d",
                "%Y %b %d",
                "%Y %B %d",
                "%b %d %Y",
                "%B %d %Y",
                "%B %d, %Y",
            ]

            for format in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, format)
                    return parsed_date.strftime("%Y%m%d")
                except ValueError:
                    continue
            return ""
    return ""
