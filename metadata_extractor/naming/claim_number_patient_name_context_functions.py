import re

# Fax


def find_context_fax_claim_number(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


def find_context_fax_patient_name(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += " "

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


# CaseManagementNotes


def find_context_case_management_notes_claim_number(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


# Determination_Med_Auth


def fix_date_of_birth_in_text(text):
    pattern = r"ate\s*of\s*birth"

    match = re.search(pattern, text, re.IGNORECASE)

    if match:
        match_index = match.start()
        if match_index > 0:
            text = text[: match_index - 1] + "D" + text[match_index:]

    return text


def find_context_determination_med_auth_patient_name(text, patterns):
    extracted_text = ""
    for pattern in patterns:
        matches = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            extracted_text = matches.group(0)
            context = extracted_text
            break
        else:
            context = ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines != "":
        context = fix_date_of_birth_in_text(context)
        patterns_to_replaces = [r"Name", r"Claim Number:"]
        for pattern in patterns_to_replaces:
            context = re.sub(pattern, "", context)

        pattern_to_select_before = "^(.*?)(?:DOB|DOI|DOL|Carrier|Review)"
        matches = re.findall(pattern_to_select_before, context)
        if matches:
            if matches[0]:
                context = matches[0]
        if not context:
            context = ""
    else:
        context = ""

    context = context.strip()

    return context


def find_context_determination_med_auth_claim_number(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


# MiscCorrespondence


def find_context_misc_correspondence_patient_name(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            extracted_text = matches.group(0)
            context = extracted_text
            break
        else:
            context = ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


def find_context_misc_correspondence_claim_number(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


# Illness_InjuryReport_FROI


def find_context_illness_injury_report_froi_patient_name(text, patterns):
    for pattern in patterns:
        matches = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            start_index = max(0, matches.start() - 30)
            context = text[start_index : matches.end()]
            break
        else:
            context = ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines != "":
        context = fix_date_of_birth_in_text(context)
    else:
        context = ""

    context = context.strip()

    return context


def find_context_illness_injury_report_froi_claim_number(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


# MedicalRecords


def find_context_medical_records_patient_name(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    if context.replace(" ", "") == "":
        pattern = r"name.{0,30}"
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context = " ".join(matches)
        else:
            pattern = r"page\s*\d\s*of\s*\d.{0,30}"
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                context = " ".join(matches)
            else:
                pattern = r"patient.{0,30}"
                matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
                if matches:
                    context = " ".join(matches)
                else:
                    context = ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines != "":
        context = fix_date_of_birth_in_text(context)
    else:
        context = ""

    context = context.strip()

    return context


def find_context_medical_records_claim_number(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


# Supplemental_WorkStatus


def find_context_supplemental_work_status_claim_number(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


def find_context_supplemental_work_status_patient_name(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


# IMR_IME_QME


def clean_imr_ime_qme_patient_name_context(patient_name_context):
    patterns_inside_string = [r"^(.*?)Provider", r"^(.*?)Addre(s|)(s|)"]
    for pattern in patterns_inside_string:
        match = re.match(pattern, patient_name_context)
        if match:
            patient_name_context = match.group(1)
            break

    # remove all special characters apart from '.' and '-'
    clean_match = re.sub(r"[^\w\s.-:]", "", patient_name_context)

    # remove name and surname before MD title
    match = re.search(r"(\w+)\s+(\w+)\s+md", clean_match, re.IGNORECASE)

    if match:
        clean_match = re.sub(match.group(0), "", clean_match)

    # remove all unnecessary whitespaces
    clean_match = re.sub(r"\s+", " ", clean_match)

    first_last_pattern = r"first\s*m(i|l)\s*(\.|)\s*last"

    clean_match = re.sub(first_last_pattern, "", clean_match, flags=re.IGNORECASE)

    patient_name_context = clean_match.strip()

    context_without_whitespaces_and_newlines = patient_name_context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        patient_name_context = ""

    context = patient_name_context.strip()

    return context


def find_context_imr_ime_qme_patient_name(text, patterns):
    for pattern in patterns:
        matches = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            extracted_text = matches.group(0)
            context = extracted_text
            break
        else:
            context = ""

    context = clean_imr_ime_qme_patient_name_context(context)

    return context


def find_context_imr_ime_qme_claim_number(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(map(str, matches))
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


# Other


def find_context_other_claim_number(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


def find_context_other_patient_name(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


# RFA


def find_context_rfa_claim_number(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


def find_context_rfa_patient_name(text, patterns):
    context = ""
    for pattern in patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            context += " ".join(matches)
        else:
            context += ""

    context_without_whitespaces_and_newlines = context.replace(" ", "").replace("\n", "")
    if context_without_whitespaces_and_newlines == "":
        context = ""

    context = context.strip()

    return context


DOCTYPE_TO_CONTEXT_FUNCTIONS_MAPPING = {
    "Case Management Notes": (
        find_context_case_management_notes_claim_number,
        find_context_case_management_notes_claim_number,
    ),
    "Determination - Med Auth": (
        find_context_determination_med_auth_claim_number,
        find_context_determination_med_auth_patient_name,
    ),
    "Fax": (find_context_fax_claim_number, find_context_fax_patient_name),
    "Injury/Illness/FROI": (
        find_context_illness_injury_report_froi_claim_number,
        find_context_illness_injury_report_froi_patient_name,
    ),
    "IMR/IME/QME": (
        find_context_imr_ime_qme_claim_number,
        find_context_imr_ime_qme_patient_name,
    ),
    "Medical Records": (
        find_context_medical_records_claim_number,
        find_context_medical_records_patient_name,
    ),
    "Misc Correspondence": (
        find_context_misc_correspondence_claim_number,
        find_context_misc_correspondence_patient_name,
    ),
    "Other": (find_context_other_claim_number, find_context_other_patient_name),
    "Supplemental/Work Status": (
        find_context_supplemental_work_status_claim_number,
        find_context_supplemental_work_status_patient_name,
    ),
    "RFA": (find_context_rfa_claim_number, find_context_rfa_patient_name),
}
