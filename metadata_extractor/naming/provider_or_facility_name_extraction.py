import base64
import os

from dotenv import load_dotenv
from naming.naming_prompts import PROVIDER_OR_FACILITY_NAME_EXTRACTION_PROMPT
from ollama import Client

load_dotenv()

# BASE_AUTH_USER_AND_PASSWORD = os.environ["BASE_AUTH_USER_AND_PASSWORD"]
SERVER_HOST = os.environ["SERVER_HOST"]
SERVER_PORT = os.environ["SERVER_PORT"]

# BASE64_STRING = base64.b64encode(BASE_AUTH_USER_AND_PASSWORD.encode()).decode("ascii")
# AUTH_HEADER = {"Authorization": f"Basic {BASE64_STRING}"}

# OLLAMA_CLIENT = Client(host=f"{SERVER_HOST}:{SERVER_PORT}", headers=AUTH_HEADER, timeout=360.0, verify=False)
OLLAMA_CLIENT = Client(host=f"{SERVER_HOST}:{SERVER_PORT}", timeout=360.0, verify=False)

OLLAMA_OPTIONS = {
    "num_predict": 150,
    "temperature": 0,
    # "top_k": 5,
    # "top_p": 0.1,
    # "mirostat_tau": 0.5,
}


MODEL_NAME = "mistral-nemo:12b-instruct-2407-q5_K_M_medical_assistant"

FACILITY_DATA = [
    "Reliant Urgent Care - Montebello",
    "ABC Occupational Medical Center",
    "James R. McClurg",
    "Institutes of Health",
    "Arrowhead Orthopaedics",
    "ARROWHEAD ORTHOPAEDICS",
    "UCLA Health",
    "MEDEX",
    "Concentra Medical Centers",
    "Concentra Hlth Svcs",
    "Concentra hlth",
    "Concentra",
    "ALTA ORTHOPAEDIC MEDICAL GROUP. INC.",
    "COASTALORTHO ADVANCED ORTHOPEDICS",
    "COASTALORTHO ADVANCED",
    "COASTALORTHO",
    "Valley Radiology CONSULTANTS",
    "Boomerang Healtcare",
    "Boomerang",
    "ARROWHEAD ORTHOPAEDICS",
    "ARASH",
    "Select physical Therapy",
    "LA Heath Solutions",
    "MIZUFUKA",
    "SJ PAIN CENTER & OCCUPATIONAL CLINIC",
    "Broadspire",
    "Broadspire A CRAWFORD COMPANY",
    "Sports & Spine Orthopaedics",
    "ORTHOPAEDIC MEDICAL GROUP OF RIVERSIDE",
    "PAIN & WELLNESS CENTERS OF SOUTHERN CALIFORNIA",
    "San Bernardino County",
    "South Atlanta Neurology and Pain Clinic",
    "RadNet West Coast",
    "RadNet",
    "VIBRANT care",
    "Akeso Occupational Health",
    "Pain and Rehabilitative Consultants Medical Group",
    "ORTHO MED CENTER",
    "HEALTHPONTE",
    "PULSE",
]


def process_with_ollama(input_prompt: str) -> str:
    response = OLLAMA_CLIENT.generate(model=MODEL_NAME, prompt=input_prompt, options=OLLAMA_OPTIONS)
    return response["response"]


def extract_sender_name(result: str) -> str:
    input_prompt = PROVIDER_OR_FACILITY_NAME_EXTRACTION_PROMPT.format(result=result)
    response = process_with_ollama(input_prompt=input_prompt)
    return response


def facility_check(text: str) -> str:
    for facility in FACILITY_DATA:
        if facility.lower() in text.lower():
            return facility
    pass


def medical_records_extract_provider_or_facility_name(text: str) -> str:
    facility = facility_check(text[:200])
    if facility is not None:
        return '{"providerOrFacilityName": "' + facility + '"}'
    else:
        sender_name = extract_sender_name(text[:250])
    return sender_name


DOCTYPE_TO_FUNCTION_MAPPING_PROVIDER_OR_FACILITY_NAME = {
    "Medical Records": medical_records_extract_provider_or_facility_name,
}
