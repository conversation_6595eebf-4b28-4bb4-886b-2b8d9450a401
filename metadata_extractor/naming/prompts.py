CLAIM_NUMBER_EXTRACTION_PROMPT = """
Given the OCR text, extract the claim/policy number located in the text.

Format the extracted information into a JSON object with "Claim_number" key, adhering strictly to the following rules:
1. "Claim_number": Extract the claim/policy number, search for it only around the words "claim" and "policy" or before the words "carrier/administrator".
2. Do not ever include any additional information, such as address, phone number etc.!

You should always output the JSON information only, without any additional comments. Otherwise, you will be punished.

You must never output any explanation after the JSON in your response. It is strictly prohibited.

Use this empty JSON template to fill in:

{
  "Claim_number": "",
}

Now, fill in the template by extracting the required information from the given text. The text is given here:
"""

PATIENT_NAME_EXTRACTION_PROMPT = """
Given the OCR text, extract a specific piece of information: the patient's full name – first name, middle name (if present) and last name.

Format the extracted information into a JSON object with "Patient_name" key, adhering strictly to the following rules:
1. "Patient_name": Extract the patient's full name: name and surname (and middlename if exists) mentioned around the words 'patient', 'employee', 'regarding employee'.
2. Do not ever include any additional information, such as address, phone number etc.!

You should always output the JSON information only, without any additional comments. Otherwise, you will be punished.

You must never output any explanation after the JSON in your response. It is strictly prohibited.

"Patient_name" should be in the format <first name, middle name, last name>.
For example, FirstName MiddleName LastName – therefore, "Patient_name" has to have maximum three words.

Use this empty JSON template to fill in:

{
  "Patient_name": "",
}

Now, fill in the template by extracting the required information from the given text. The text is given here:

"""

PROMPT_FOR_INSERTING_TEXT_BELOW = """
-------------------
{ocr_text}
-------------------
"""

SENDER_NAME_EXTRACTION_PROMPT = """
    Given the OCR text, extract the sender's name mentioned in the text.
    Format the extracted information into a JSON object with 'Sender_name' key, adhering strictly to the following rules:
    1. 'Sender_name': Extract the sender's name mentioned in the text. It should be a company that the document was sent from or faxed from. In case there is no name of the company, then sender's name is a name of the person who sent the document. This will be typically located after the word "From: ". If there is a person's name after the word "Provider", then this name should be used as a result. If there is both a company name and a person's name, then the company name should be extracted.
    2. Do not ever include any additional information, such as address, phone number etc.!
    You should always output the JSON information only, without any additional comments.
    Otherwise, you will be punished.
    You must never output any explanation after the JSON in your response. It is strictly prohibited.
    Use this empty JSON template to fill in: {{ 'Sender_name': '' }}
    Now, fill in the template by extracting the required information from the given text.
    The text is given here:
    -----------------
    {result}
    -----------------
"""

DATE_EXTRACTION_PROMPT = """
    Given the OCR text, extract the date name mentioned in the text.
    Format the extracted information into a JSON object with 'Date' key, adhering strictly to the following rules:
    1. 'Date': Extract the date mentioned in the text.
    When extracting the date which includes days of the week, you must not include the day of the week in the extracted date.
    For example, instead of 'Monday, January 12, 2022', you should extract 'January 12, 2022'.
    2. Do not ever include any additional information, such as address, phone number etc.!
    You should always output the JSON information only, without any additional comments.
    Otherwise, you will be punished.
    You must never output any explanation after the JSON in your response. It is strictly prohibited.
    Use this empty JSON template to fill in: {{ 'Date': '' }}
    Now, fill in the template by extracting the required information from the given text.
    The text is given here: {result}"""


STATE_OF_CALIFORNIA_REGEX_PATTERN = r"^(.*?)S(t|)at(e|o)\s*of\s*Ca(l|t|ll)(i|l|il|)(f|t)(o|a)(rni|mli|ml|rnl|mi|nl)(a|s)"
PROVIDER_REGEX_PATTERN = r"(?<!note to\s*)provider\s*:"
EMPLOYER_REGEX_PATTERN = r"e(m|n)ployer\s*"
BASIC_DATE_PATTERN = r"\d{1,2}(/|-)\d{1,2}(/|-)\d{2,4}"
COMPLEX_DATE_PATTERN = r"(\d{1,2}[/-]\d{1,2}[/-]\d{4})|(\d{1,2}\s(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\s\d{1,2},\s\d{4})|((?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\s\d{1,2}[,-]\s\d{4})|(\d{4}[/-]\d{1,2}[/-]\d{1,2})|(\d{1,2}\s(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|Dec(?:ember)?)\s\d{1,2}\s\d{4})"

TABLE_WITHOUT_HEADER_PROMPT = """I need to classify the following sentences into one of the following categories:

Diagnosis Description: If the sentence is a description of a medical diagnosis, return {{ 'category': 'diagnosisDescription' }} .
ICD-Code: If the sentence is an ICD code, return {{ 'category': 'diagnosisCode' }}.
Service/Good Requested: If the sentence describes a service or good requested, return {{ 'category': 'treatmentServiceRequested' }}.
CPT/HCPCS Code: If the sentence is a CPT/HCPCS code, return {{ 'category': 'procedureCode' }}.
Other Information: If the sentence contains other information like frequency, duration, quantity, etc., return {{ 'category': 'otherInformation' }}.

Use the following examples to guide your classification:
Examples:
Input: "LUMBAR SPRAIN"
return: {{ 'category': 'diagnosisDescription' }}

Input: "S30.012A"
return: {{ 'category': 'diagnosisCode' }}

Input: "Therm Car Heating Wrap"
return: {{ 'category': 'treatmentServiceRequested' }}

Input: "97012"
return: {{ 'category': 'procedureCode' }}

Input: "Once daily for 30 days"
return: {{ 'category': 'otherInformation' }}

You should always output the JSON information only, without any additional comments.
Here is the sentence: {0}
"""

TABLE_PROMPT = """You will be presented with a text extracted from the document.
Your task is to extract the following information from the text:
- Diagnosis Code
- ICD Code
- Service/Good Requested
- CPT/HCPCS Code
- Other Information
- Body Part

This information will most likely be structured as a Markdown table inside the text, so you will need to extract the information from the table and provide it in a JSON format.
Otherwise, look for the keywords and the values associated with them in the text.

For example, if there is a section within the text which starts with "Diagnosis", look for the value associated with it in the next few lines.
The same goes for the other fields, such as "ICD Code", "Service/Good Requested", "CPT/HCPCS Code", "Other Information", and "Body Part".

The Body Part field should be extracted from all the fields extracted previously. If there is no explicit mention of the body part, derive it from the Diagnosis field. If it is impossible to so, please, leave the Body Part field empty.

If there are no values for a specific field, you can leave it empty.
You are prohibited to include any values that are not present in the text.

Examples of the ICD Code format: "M79.632", "M18.12", "H60.1", "M31.9", etc.
Examples of the CPT/HCPCS Code format: "90658", "99213", "99396", etc.

Example:

- Input data:
<input_data>
Fax Server 4716/2024 2:09:11 PM PAGE 2/009 Fax Server DHRHealth Handand.Wrist Institute 5121 S. McColl Road Office: ************ Edinburg, TX 78539 Fax: ************ PRE-AUTHORIZATION REQUEST for WORKER'S COMP MARIA MARTINEZ 06/29/1963 Patient Name: DOB: Patient Address: 1301 S 6TH ST APT B2 TX 78501 City: _MCALLEN State: Zip: SSN#: Phone#: ************ Date of Injury: 03/04/2024 Claim#: *********  Insurance Carrier: TRISTAR Adjuster: SALLY HERNANDEZ  
Phone/Fax#: ************ EXT:2909 /************ Outpt Surgery Date: 23Hr. Observation/Admission MRI Date: PENDING  DOCTORS HOSPITAL RENAISSANCE NPI:********** Facility Name: - Diagnosis: DORSAL WRIST PAIN ICD10: S63.521A/ S66.911A Procedure: MRI RIGHT WRIST WITHOUT CONTRAST Requesting Physician: Dr. Sergio Rodriguez Tax ID 46-3969440 Phone#: (************* Fax#: (************* Person to Contact: LUPITA Date Submitted: 04/16/2024
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "diagnosis": "DORSAL WRIST PAIN",
        "icd_code": "S63.521A/S66.911A",
        "service_good_requested": "MRI RIGHT WRIST WITHOUT CONTRAST",
        "cpt_hcpcs_code": "73221",
        "other_information": "",
        "body_part": "RIGHT WRIST"
    }},
]
```
</extracted_data>


Example:
- Input data:
<input_data>
TRISTAR UR REFERRAL FORM DATE SENT TO TRISTAR: 11/13/2023 DATE RCVD BY SISC: 11/11/2023 Prospective Review: Y JURISDICTIONAL CLAIM #: 2023021011493378640485 Retrospective Review: N EAMS# Appeal: N PATIENT NAME: Karla Gonzalez-Garcia DOB: 09/03/1992 ADDRESS: 10113 Monahan St PH: (661)377-4993 Bakersfield, CA 93311-4687 SSN: ********* INSURANCE CARRIER/TPA: Self Insured Schools of CA (SISC1)
PATIENT EMPLOYER: Panama Buena Vista Union School District DOI: 01/16/2023 ADJUSTER: Marilyn Locey CLAIM: ********** PHONE: (661)636-4281 Fax: (661)636-4721 PRIMARY TREATING PHYSICIAN: Linh K. Ngo/Sendas PH: (************* ADDRESS: 9450 Ming Ave., Bakersfield, CA 93311 FAX: (************* SECONDARY TREATING PHYSICIAN: O. Oluyede/SCOI PH: (************* ext. 6327 5201 Truxtun Ave, Bakersfield, CA 93309 FAX: (************* AA: N/A PH: ADDRESS: FAX: DA: N/A PH: ADDRESS: FAX:
ACCEPTED BODY PARTS: Low back, tail bone DIAGNOSIS: Low back pain with leg paresthesia MECHANISM OF INJURY: Sitting in office chair at her desk, since 1/16/2023 REQUESTED TREATMENT: Add'l Physical therapy 2 x 6 = 12 visits for low back (If auth, pleases schedule thru Payers Direct - (*************) ADJUSTER COMMENTS: Please review for medical necessity Please fax request to ************ Phone referral/Inquiries: ************
The information contained in this message is intended only for the use of the individual or entity to which it was addressed and may contain information that is privileged and confidential. If the reader of this message is not the intended recipient, you are notified that any dissemination or copying of this communication is strictly prohibited. If you have received this communication in error, please destroy this message, delete any. copies held in your system and notify the sender immediately. Thank you. UR Referral
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "diagnosis": "Low back pain with leg paresthesia",
        "icd_code": "",
        "service_good_requested": "Add'l Physical therapy 2 x 6 = 12 visits for low back",
        "cpt_hcpcs_code": "",
        "other_information": "",
        "body_part": "Low back"
    }}
]
```
</extracted_data>


Example:
- Input data:
<input_data>
Fax: *********** To: Fax: (************* From: Mariana Jimenez Page: 2 ot 8 06/03/2024 9:52 AM  Nova Medical Centers Alodical Centers Centralized Biling Office ova 550 Club Drive, Suite 244 Texas TDI Certified Montgomery TX 77315 HCN Provider Fropriet ary Nova So wore. Ph:************ Fax: ************ Centralized Scheduling Department: ************ PT/OT 
Pre-Authorization Request Form Patient Name: Jacob Hunt SSN# *********** DOB 10/7/1995 Address: 1231 huisache ave, Apt 1312 New Braunfels, TX 78130 DOI: 4/27/2024 Sprain of ligaments of lumbar spine, subsequent encounter s33.5xxd strain of muscle, fascia and tendon of lower back, subsequent encounter s39.012d unspecified abnormalities of gait and mobility r26.9 
Employer: Vaughn -(CCIP TFC -Phase One) Facility Tax ID: 84-2131917 NPI:********** Danielle Coulter, MD P7094TX Treating Dr: Therapist: ********* Claim #: IIns. Carrier: TRISTAR Adjuster: Number of Visits Requosted: 6(3 times a week for 2 weeks) Number of Visits Authorized:  Beginning Authorization Total PT/Ot Visits to Date 5 Date: Ending Authorization Date: Approved by Approval Dato: Writien 
Approval Required: Yes Fax or Emall to: ************ Authorization Number: Authorization Number Obtained Request Date: 6/3/2024 By: Due to unknown fuluro progression of this pationt, any combination of CpT codos below may bo warranted for this proscription. Any procedures and units performed are provided to reach MMI at the earliest possible date CPT 97110 97112 97535 97530 97140 From: Mariana Jimenez Fax: *********** To: Fax: (************* Page: 3 ot 8 06/03/2024 9:52 AM NicCest Occufle? Easy-Script Powered By Froprietary Nove Sattware Company: Vaughn - (CCIP TFC - Phase One) Clalm Number: ********* 
Company Phono: ************ Date: 06/03/2024 Patient Name: Jacob Hunt. Date of Injury: 04/27/2024 Patient Phone: ************  Diagnosis: Sprain of ligaments of lumbar spine, subsequent encounter S33.5xXD Strain of muscle, fascia and tendon of lower back, subsequent encounter S39.012D. Unspecified abnormalities of gait and mobility R26.9 Subjective Complaints (what patient states): Pt is here for a f/u. Pt statos pain is a 1/10, mild ache after driving. Typically, ho reports no pain. Lumbar Spine: Patient states that overall the symptoms have decroased. Pain docreased. Pationt reports a pain level of (Visual Analog Scale) 1. Range of motion normal. Radiating pain resolved. Numbness and tingling: None. Lower extremity weaknoss: None. Loss of bowel/bladder control: No. Saddle anesthesia: No.  Reviewed Family, Past Medical, Social History, and Review of Systems from 4/29/2024 and there has been no change.. 
Exam/Results: Physical: Blood pressure 126 / 74. Pulse 77. Respiratory rate 16. Height (inches) 68. Weight (Ibs) 228. BMl: 34.7, BSA (m?): 2.23. Age 28. General: Alert and oriented to time, place, and person: Yes. Affect normal. Gait normal. Distress no apparent. Patient appears anxious: No. Well developed: Yes. Well nutritioned: Yes. Lumbar Spine: Full range of motion. Inspection no obvious doformities. Latoral deviation to normal. Lordosis normal. Range of motion flexion normal. Extonsion normal. Rotation normal. Muscle spasm along the paraspinal musclos: None. Tendornoss resolved.. Lower extremities: Rango of motion full range of motion. Vascular intact: Yes. Deop tendon reflexes normal. Sensation normal. Muscle strength normal. Special testing sitting SLR right negative. Sitting SLR left negative. Gait normal: Yes. Biling Proce dures  
Employer #: UGH19 SUBSCRIBER Emp Ins Code: 06U6K Ins/TPA: ACIG AMERICAN CONTRACTORS INS GRP Ins/TPA TRISTAR NA VERIFIED 6/18/19 KL Phone: / Fax: Phone: I Fax: ins Codo: AME11 Ins Code: TRI00 Emp Guarantort: Emp Guarantor#: Price Code: Price Code: Continue Physical Therapy. TIW (three times week) for 2 weeks / 6 visits Home Exercise Program of lower back, subsequent encounter S39.012D, Unspecified abnormalities of gait and mobility R26.9. Weekly frequency and prescription duration may vary to allow for complotion of the total prescribod visits.. Statement of Modical Nocossity: I deom that the above prescribed treatment is medically necessary.  Physician's Note: Reason for continuing Physical therapy: Support increasing activities and home exercise program. Complete the currently scheduled PT session. Danielle Coulter, MD
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "diagnosis": "Sprain of ligaments of lumbar spine, subsequent encounter",
        "icd_code": "S33.5xXD",
        "service_good_requested": "",
        "cpt_hcpcs_code": "",
        "other_information": "",
        "body_part": "Lumbar spine"
    }},
    {{
        "diagnosis": "Strain of muscle, fascia and tendon of lower back, subsequent encounter",
        "icd_code": "S39.012D",
        "service_good_requested": "",
        "cpt_hcpcs_code": "",
        "other_information": "",
        "body_part": "Lower back"
    }},
    {{
        "diagnosis": "Unspecified abnormalities of gait and mobility",
        "icd_code": "R26.9",
        "service_good_requested": "",
        "cpt_hcpcs_code": "",
        "other_information": "",
        "body_part": ""
    }}
]
```

Example:
- Input data:
<input_data>
f
sdfj
we[a
asdf]
k
11
asjdf
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "diagnosis": "",
        "icd_code": "",
        "service_good_requested": "",
        "cpt_hcpcs_code": "",
        "other_information": "",
        "body_part": ""
    }}
]



You should output the table in JSON format with the following structure:
```json
[
    {{
    "diagnosis": "<extracted text for the Diagnosis>",
    "icd_code": "<extracted code for the ICD Code>",
    "service_good_requested": "<extracted text for the Service/Good Requested or for Procedure Requested>",
    "cpt_hcpcs_code": "<extracted code for the CPT/HCPCS Code>",
    "other_information": "<extracted text for the Other Information>",
    "body_part": "<extracted body part for Accepted Body Parts>"
    }}
]
```

You have to follow this structure for every row in the table.
If there is more than one row in the table, separate the rows by adding more elements to the provided JSON list.

Think through it step by step and make sure to extract the correct information from the text.

Table:
{table}
"""

BODY_PART_PROMPT = """You will be presented with a text extracted from the document.
Your task is to extract the body part(s) from the text.

This information will be structured as a list of key-value pairs inside the text, so you will need to extract the information from the list and provide it in a JSON format.

The Body Part field should be extracted from the fields provided in the table. If there is no explicit mention of the body part, derive it from the Diagnosis field. If it is impossible to so, please, leave the Body Part field empty.
Do not include any additional information that is not present in the text. For example, if body part is not mentioned in the text, do not include it in the output.

If there are no values for a specific field, you can leave it empty.
You are prohibited to include any values that are not present in the text.

Example:

- Input data:
<input_data>
    diagnosisDescription: Right knee
    diagnosisCode: M25.561
    treatmentServiceRequested: Additional post op Physical Therapy
    procedureCode: 97110
    otherInformation: Right knee Physical pain Patient has completed 24 sessions to date, done at Arrowhead Orthopaedics, to be S/P Right knee scope
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "bodyPart":"Right knee",
    }}
]
```
</extracted_data>


Example:
- Input data:
<input_data>
      diagnosisDescription: Low back pain with leg paresthesia
      diagnosisCode:
      treatmentServiceRequested: Add'l Physical therapy 2 x 6 = 12 visits for low back (If auth, pleases schedule thru Payers Direct - (*************)
      procedureCode:
      otherInformation:
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "body_part": "Low back"
    }}
]
```
</extracted_data>


Example:
- Input data:
<input_data>
[
   {{
      "diagnosisDescription":"M54.17 Radiculopathy, lumbosacral region",
      "diagnosisCode":"M54.17",
      "treatmentServiceRequested":"Tramadol Hcl 50 Mg, Take 1 tablet daily",
      "procedureCode":"#25",
      "otherInformation":"",
   }},
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "body_part": "Lumbosacral region"
    }}
]
```
</extracted_data>

Example:
- Input data:
<input_data>
      diagnosisDescription: M23.239 Derangement
      diagnosisCode: M23.239
      treatmentServiceRequested:
      procedureCode:
      otherInformation:
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "body_part": ""
    }}
]

Example:
- Input data:
<input_data>
      diagnosisDescription: M51.9 Unspecified thoracic, thoracolumbar and lumbosacral intervertebral disc disorder
      diagnosisCode: M51.9
      treatmentServiceRequested:
      procedureCode:
      otherInformation:
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "body_part": "Thoracic, thoracolumbar and lumbosacral intervertebral disc"
    }}
]

Example:
- Input data:
<input_data>
      diagnosisCode: Adjuster :KarsonJKammerer:Phone:************;Fax:************ , Adjuster :KarsonJKammerer:Phone:************;Fax:************
      otherInformation: Date: 04/20/2023, Date: 04/20/2023
      diagnosisDescription:
      treatmentServiceRequested:
      procedureCode:
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "body_part": ""
    }}
]
</extracted_data>

- Input data:
<input_data>
   diagnosis: Bilateral knee osteoarthritis (M17.0)
   icd_code: M17.0
   service_good_requested: Request authorization for postoperative medications with Hydrocodone. 10/325 mg #90 one tablet every 6 hours as needed for pain.
   cpt_hcpcs_code:
   other_information:
</input_data>

- Extracted data:
<extracted_data>
```json
[
    {{
        "bodyPart": "Bilateral knee"
    }}
]
```

You should output the table in JSON format with the following structure:
```json
[
    {{
    "bodyPart": ""
    }}
]
```

You have to follow this structure for every row in the provided table.
Make sure to not add <extracted body part> or non-existing body parts in the text to the JSON output.
Think throught it step by step and make sure to extract the correct information from the text.

Table:
{table}
"""
