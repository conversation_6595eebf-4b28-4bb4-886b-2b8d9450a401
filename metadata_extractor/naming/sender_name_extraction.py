import base64
import json
import os
import re

import regex
from dotenv import load_dotenv
from ollama import Client

load_dotenv()

# BASE_AUTH_USER_AND_PASSWORD = os.environ["BASE_AUTH_USER_AND_PASSWORD"]
SERVER_HOST = os.environ["SERVER_HOST"]
SERVER_PORT = os.environ["SERVER_PORT"]

# BASE64_STRING = base64.b64encode(BASE_AUTH_USER_AND_PASSWORD.encode()).decode("ascii")
# AUTH_HEADER = {"Authorization": f"Basic {BASE64_STRING}"}

# OLLAMA_CLIENT = Client(host=f"{SERVER_HOST}:{SERVER_PORT}", headers=AUTH_HEADER, timeout=360.0, verify=False)
OLLAMA_CLIENT = Client(host=f"{SERVER_HOST}:{SERVER_PORT}", timeout=360.0, verify=False)

OLLAMA_OPTIONS = {
    "num_predict": 150,
    "temperature": 0,
    # "top_k": 5,
    # "top_p": 0.1,
    # "mirostat_tau": 0.5,
}


MODEL_NAME = "mistral-nemo:12b-instruct-2407-q5_K_M_medical_assistant"

prompt = """
    Given the OCR text, extract the sender's name mentioned in the text.
    Format the extracted information into a JSON object with 'Sender_name' key, adhering strictly to the following rules:
    1. 'Sender_name': Extract the sender's name mentioned in the text. It should be a company that the document was sent from or faxed from. In case there is no name of the company, then sender's name is a name of the person who sent the document. This will be typically located after the word "From ". If there is a person's name after the word "Provider", then this name should be used as a result. If there is both a company name and a person's name, then the company name should be extracted.
    2. Do not ever include any additional information, such as address, phone number etc.!
    You should always output the JSON information only, without any additional comments.
    Otherwise, you will be punished.
    You must never output any explanation after the JSON in your response. It is strictly prohibited.
    Use this empty JSON template to fill in: {{ 'senderName': '' }}
    Now, fill in the template by extracting the required information from the given text.
    The text is given here:
    -----------------
    {result}
    -----------------
"""

FROI_TEXT_BAD_CASES = [
    "EMPLOYER'S REPORT OF OCCUPATIONAL INJURY OR ILLNESSE FATALITY",
    "STATE OF CALIFORNIA DOCTOR'S FIRST REPORT OF OCCUPATIONAL INJURY OR ILLNESS",
    "OSHA",
    "WORKERS COMPESSATION",
]

FROI_BAD_CASES = [
    "Forms On-A-Disk",
    "Vithya Jwners",
    "State of California",
    "STATEOF",
    "SBC-DPH/CCS",
    "DMPANY",
    "STATE",
]

FACILITY_DATA = [
    "Reliant Urgent Care - Montebello",
    "ABC Occupational Medical Center",
    "James R. McClurg",
    "Institutes of Health",
    "Arrowhead Orthopaedics",
    "ARROWHEAD ORTHOPAEDICS",
    "UCLA Health",
    "MEDEX",
    "Concentra Medical Centers",
    "Concentra Hlth Svcs",
    "Concentra hlth",
    "Concentra",
    "ALTA ORTHOPAEDIC MEDICAL GROUP. INC.",
    "COASTALORTHO ADVANCED ORTHOPEDICS",
    "COASTALORTHO ADVANCED",
    "COASTALORTHO",
    "Valley Radiology CONSULTANTS",
    "Boomerang Healtcare",
    "Boomerang",
    "ARROWHEAD ORTHOPAEDICS",
    "ARASH",
    "Select physical Therapy",
    "LA Heath Solutions",
    "MIZUFUKA",
    "SJ PAIN CENTER & OCCUPATIONAL CLINIC",
    "Broadspire",
    "Broadspire A CRAWFORD COMPANY",
    "Sports & Spine Orthopaedics",
    "ORTHOPAEDIC MEDICAL GROUP OF RIVERSIDE",
    "PAIN & WELLNESS CENTERS OF SOUTHERN CALIFORNIA",
    "San Bernardino County",
    "South Atlanta Neurology and Pain Clinic",
    "RadNet West Coast",
    "RadNet",
    "VIBRANT care",
    "Akeso Occupational Health",
    "Pain and Rehabilitative Consultants Medical Group",
    "ORTHO MED CENTER",
    "HEALTHPONTE",
    "PULSE",
]


def process_with_ollama(input_prompt: str) -> str:
    response = OLLAMA_CLIENT.generate(model=MODEL_NAME, prompt=input_prompt, options=OLLAMA_OPTIONS)
    return response["response"]


def extract_sender_name(result: str) -> str:
    input_prompt = prompt.format(result=result)
    response = process_with_ollama(input_prompt=input_prompt)
    return response


def determination_med_auth_extract_sender_name(text: str) -> str:
    sender_name = ""
    if "medex" in text.lower()[:30]:
        sender_name = '{"Sender_name": "MEDEX"}'
    else:
        patterns = [r"RE:", r"From:", r"Sincerely"]
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                text = text[match.start() : match.start() + 50]
                sender_name = extract_sender_name(text)
                break
    if not sender_name:
        sender_name = '{"Sender_name": ""}'
    return sender_name


STATE_OF_CALIFORNIA_REGEX_PATTERN = r"^(.*?)Stat[eo]\s*of\s*Ca(l|t|ll)(i|l|il|)(f|t)(o|a)(rni|mli|ml|rnl|mi|nl)(a|s)"


def illness_injury_report_froi_extract_sender_name(text: str) -> str:
    match = re.search(STATE_OF_CALIFORNIA_REGEX_PATTERN, text[:150], re.IGNORECASE)
    if match:
        text_before_pattern = text[: match.start()]
        if text_before_pattern != "":
            text = text_before_pattern
    else:
        text = text[:150]

    if any(bad_case in text for bad_case in FROI_TEXT_BAD_CASES):
        return '{"senderName": ""}'
    sender_name = extract_sender_name(text)

    if any(case in sender_name for case in FROI_BAD_CASES):
        return '{"senderName": ""}'

    return sender_name


def facility_check(text: str) -> str:
    for facility in FACILITY_DATA:
        if facility.lower() in text.lower():
            return facility
    pass


def supplemental_work_status_extract_sender_name(text: str) -> str:
    provider_pattern = r"(?<!note to\s*)provider\s*:"
    provider_match = regex.search(provider_pattern, text, re.IGNORECASE)
    facility = facility_check(text)
    if facility is not None:
        return '{"senderName": "' + facility + '"}'

    if provider_match:
        text_found = text[provider_match.start() - 10 : provider_match.start() + 80]
    else:
        match = re.search(STATE_OF_CALIFORNIA_REGEX_PATTERN, text[:150], re.IGNORECASE)

        if match:
            text_before_pattern = text[: match.start()]
            if text_before_pattern != "":
                text_found = text_before_pattern
            else:
                return '{"senderName": ""}'
        else:
            text_found = text[:150]
    sender_name = extract_sender_name(text_found)
    return sender_name


def medical_records_extract_sender_name(text: str) -> str:
    facility = facility_check(text[:200])
    if facility is not None:
        return '{"senderName": "' + facility + '"}'
    sender_name = extract_sender_name(text[:200])
    return sender_name


def misc_correspondence_extract_sender_name(text: str) -> str:
    facility = facility_check(text[:200])
    if facility is not None:
        return '{"senderName": "' + facility + '"}'
    else:
        sender_name = extract_sender_name(text[:250])
    return sender_name


def fax_extract_sender_name(text: str) -> str:
    extracted_text = text[:250]
    sender_name = extract_sender_name(extracted_text)
    try:
        sender_name = json.loads(sender_name)["Sender_name"]
    except Exception as e:
        sender_name = ""
    if not sender_name:
        pattern = r"from\s*:"
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            extracted_text = text[match.start() : match.start() + 50]
            sender_name = extract_sender_name(extracted_text)
        else:
            facility = facility_check(text[:200])
            if facility is not None:
                return '{"senderName": "' + facility + '"}'

    return sender_name


def rfa_extract_sender_name(text: str) -> str:
    sender_name = ""
    result = re.search(STATE_OF_CALIFORNIA_REGEX_PATTERN, text, re.IGNORECASE)

    if result:
        extracted_text = result.group(1)
        extracted_text = re.sub(r"Powered\s*by\s*GoldFax", "", extracted_text)
        if extracted_text.strip() != "":
            sender_name = extract_sender_name(extracted_text)
    else:
        extracted_text = text[:150]
        sender_name = extract_sender_name(extracted_text)
    return sender_name


def case_management_notes_extract_sender_name(text: str) -> str:
    return '{"Sender_name": "MEDEX"}'


def imr_ime_qme_extract_sender_name(text: str) -> str:
    text = text[:450]
    sender_name = extract_sender_name(text)
    return sender_name


def other_extract_sender_name(text: str) -> str:
    if text.strip() == "":
        return '{"Sender_name": ""}'
    text = text[:200]
    facility = facility_check(text)
    if facility is not None:
        return '{"senderName": "' + facility + '"}'

    sender_name = extract_sender_name(text)
    return sender_name


DOCTYPE_TO_FUNCTION_MAPPING_SENDER_NAME = {
    "Other": other_extract_sender_name,
    "RFA": rfa_extract_sender_name,
    "Medical Records": medical_records_extract_sender_name,
    "Misc Correspondence": misc_correspondence_extract_sender_name,
    "IMR/IME/QME": imr_ime_qme_extract_sender_name,
    "Supplemental/Work Status": supplemental_work_status_extract_sender_name,
    "Determination - Med Auth": determination_med_auth_extract_sender_name,
    "Case Management Notes": case_management_notes_extract_sender_name,
    "Fax": fax_extract_sender_name,
    "Injury/Illness/FROI": illness_injury_report_froi_extract_sender_name,
}
