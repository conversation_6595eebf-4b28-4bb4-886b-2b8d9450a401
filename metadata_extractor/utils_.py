import re
from datetime import datetime

import fitz
import numpy as np
from rapidfuzz import fuzz


def replace_last(string: str, delimiter=",", replacement=" ") -> str:
    # replace the last occurrence of comma with the a space
    start, found_sep, end = string.rpartition(delimiter)
    if found_sep:
        return start + replacement + end
    return start + end


def generalized_name_splitting(entity_name, entity):
    if not entity_name:
        return {
            f"{entity}FirstName": "",
            f"{entity}MiddleName": "",
            f"{entity}LastName": "",
        }

    entity_name = entity_name.strip()

    if entity_name.count(",") > 1:
        # if there are more than one comma, then replace the last comma with a space
        entity_name = replace_last(entity_name)

    if len(entity_name.split()) == 1:
        # if there is only one word in the patient name, then return the name as it is
        return {
            f"{entity}FirstName": entity_name,
            f"{entity}MiddleName": "",
            f"{entity}LastName": "",
        }

    try:
        # check for "last_name, first_name" format
        if "," in entity_name:

            # split the full name by the comma
            last_name, first_name = entity_name.split(",")

            # strip the possible first name and last name
            first_name = first_name.strip()
            last_name = last_name.strip()

            # extract the middle name (if present)
            name_parts = first_name.split()

            if len(name_parts) > 1:
                # if there are more than one parts in the first name, then the middle name is the second part
                middle_name = " ".join(name_parts[1:])
                first_name = name_parts[0]
            else:
                middle_name = ""
        else:
            # if no comma is present, then split the name by spaces
            name_parts = entity_name.split()

            first_name = name_parts[0]
            last_name = name_parts[-1]
            middle_name = " ".join(name_parts[1:-1])

        return {
            f"{entity}FirstName": first_name,
            f"{entity}MiddleName": middle_name,
            f"{entity}LastName": last_name,
        }
    except Exception as e:
        return {
            f"{entity}FirstName": "",
            f"{entity}MiddleName": "",
            f"{entity}LastName": "",
        }


def reformat_date(text_with_date):
    print(f"reformat_date, text input: {text_with_date}")
    if not text_with_date:
        return ""
    # Adjusted date patterns to include comprehensive coverage of date formats
    date_patterns_adjusted = [
        r"\b(0?[1-9]|1[012])[-/](?:0?[1-9]|[12][0-9]|3[01])[-/](\d{4})\b",  # MM/DD/YYYY or MM-DD-YYYY
        r"\b(\d{4})[-/](0?[1-9]|1[012])[-/](?:0?[1-9]|[12][0-9]|3[01])\b",  # YYYY/MM/DD or YYYY-MM-DD
        r"\b\d{4}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(?:0?[1-9]|[12][0-9]|3[01])\b",  # YYYY Month DD
        r"\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+(?:0?[1-9]|[12][0-9]|3[01])\s+\d{4}\b",  # Month DD YYYY
    ]

    for pattern in date_patterns_adjusted:
        match = re.search(pattern, text_with_date)
        if match:
            date_str = match.group(0).replace("/", "-")

            date_formats = [
                "%m-%d-%Y",
                "%Y-%m-%d",
                "%Y %b %d",
                "%Y %B %d",
                # Formats for matching "Month DD YYYY" and "Month D YYYY"
                "%b %d %Y",
                "%B %d %Y",
            ]

            for format in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, format)
                    return parsed_date.strftime("%Y%m%d")
                except ValueError:
                    continue
            return ""
    return ""


def perform_name_splitting(patient_name: str) -> dict[str, str]:

    if not patient_name:
        return {
            "patientFirstName": "",
            "patientMiddleName": "",
            "patientLastName": "",
        }

    patient_name = patient_name.strip()

    if patient_name.count(",") > 1:
        # if there are more than one comma, then replace the last comma with a space
        patient_name = replace_last(patient_name)

    if len(patient_name.split()) == 1:
        # if there is only one word in the patient name, then return the name as it is
        return {
            "patientFirstName": patient_name,
            "patientMiddleName": "",
            "patientLastName": "",
        }

    try:
        # check for "last_name, first_name" format
        if "," in patient_name:

            # split the full name by the comma
            last_name, first_name = patient_name.split(",")

            # strip the possible first name and last name
            first_name = first_name.strip()
            last_name = last_name.strip()

            # extract the middle name (if present)
            name_parts = first_name.split()

            if len(name_parts) > 1:
                # if there are more than one parts in the first name, then the middle name is the second part
                middle_name = " ".join(name_parts[1:])
                first_name = name_parts[0]
            else:
                middle_name = ""
        else:
            # if no comma is present, then split the name by spaces
            name_parts = patient_name.split()

            first_name = name_parts[0]
            last_name = name_parts[-1]
            middle_name = " ".join(name_parts[1:-1])

        return {
            "patientFirstName": first_name,
            "patientMiddleName": middle_name,
            "patientLastName": last_name,
        }
    except Exception as e:
        return {
            "patientFirstName": "",
            "patientMiddleName": "",
            "patientLastName": "",
        }


def convert_pdf_to_pixmap(file_stream):
    mat = fitz.Matrix(300 / 72, 300 / 72)
    doc = fitz.open(stream=file_stream, filetype="pdf")
    pages = []
    for page in doc:
        pix = page.get_pixmap(matrix=mat)
        pages.append(pix)
    return pages


def pix_to_image(pix):
    bytes = np.frombuffer(pix.samples, dtype=np.uint8)
    img = bytes.reshape(pix.height, pix.width, pix.n)
    return img


def check_if_californian_rfa(ocr_text: str, ca_keywords: list[str]) -> bool:
    californian_rfa = any(fuzz.partial_ratio(variation, ocr_text.lower()) >= 80 for variation in ca_keywords)
    return californian_rfa
