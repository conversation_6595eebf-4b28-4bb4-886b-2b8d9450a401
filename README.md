# DX Ranger document classification and metadata extraction pipeline

## Overview

This pipeline is designed to:
1. Download document packages from SFTP server
2. Classify downloaded pages of the document by type
3. Split packages by classified pages
4. Extract metadata from documents
5. Postprocess extracted metadata
6. Validate extracted fields and route them to upload or to QA tool for manual review
7. Postprocess manual reviewed metadata
8. Upload split documents with metadata attached to output SFTP server

## Installing prerequisites

Before you begin, ensure you have met the following requirements:

- Docker should be installed on servers system. You can follow the [official Docker installation guide](https://docs.docker.com/get-docker/) for instructions.
- NVIDIA Docker (nvidia-docker2) installed. Refer to the [NVIDIA Docker installation guide](https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html) for installation instructions specific to servers operating system and GPU setup. Ensure that GPU driver is compatible with CUDA version 11.7 or later.
- Docker Compose installed on servers system. You can follow the [official Docker Compose installation guide](https://docs.docker.com/compose/install/) for instructions.


##### Clone this repository to your local machine.
```
git clone https://github.com/DeepXHub/dx_record_ranger_ml_pipeline/ 
cd dx_record_ranger_ml_pipeline
```
###### Install pip and venv
- install pip
- apt install python3-virtualenv

Activate venv
```
python -m venv ./
```

###### Install python dependencies
```py
.venv/bin/pip install alembic
.venv/bin/pip install psycopg2-binary==2.9.9 SQLAlchemy-Utils==0.41.1 SQLAlchemy==2.0.25 uuid6
.venv/bin/alembic init alembic
```
###### set alembic.ini:
```
sqlalchemy.url = postgresql+psycopg2://user:password@localhost:port/dbname
```
###### Then, you need to configure the env.py file inside the alembic folder to include your models for Alembic to detect them. Import your models at the top of alembic/env.py and then modify the run_migrations_offline() and run_migrations_online() functions by adding a line to include the model's MetaData object:
```
from models import models
target_metadata = models.Base.metadata
```
###### Perform migration:
```
.venv/bin/alembic revision --autogenerate -m "Initial migration"
.venv/bin/alembic upgrade head
```
##### Install DVC via pip
- DVC (Data Version Control) installed. You can install DVC by following the [official installation guide](https://dvc.org/doc/install) specific to servers operating system.
```
.venv/bin/pip install dvc[gdrive]
```
## Pipeline Installation

### Running database, rabbitMQ, minio and SFTP input output
###### Copy docker-compose.yml for services
```
cd containers
cp docker-compose.yml.sample docker-compose.yml
```
###### Edit docker-compose.yml as needed and run:
```
ne docker-compose.yml
```

###### Change owner of sftp folder to prevent permission mismatch when pipeline will operate:
```
chown -R <your_user>:<your_group> sftp # example: chown -R pipeline:pipeline sftp
```
Now you shouild be able to join your 2 sftp servers from remote host

###### Add certificates for secure connections
```
mkdir certs
cd certs
mv {path_to_certificates}/* ./
```

###### Grant access to mounted keys to 999:999 (psql user)
```
chown -R 999:999 ./*
chmod 0600 server.key
```
###### Edit mounted posgresql.conf keys to 
```
ssl = true
ssl_cert_file = {path where ssl cert is mounted to in pgsql image}
ssl_key_file = {path where ssl private key is mounted to in pgsql image}
```
###### For minio mounting cetficates in docker-compose.yml should work without additional configuration

###### TODO ssl rabbitmq

###### After configuring service modules execute.
```
docker compose up -d
```

###### Exec into rabbitmq container to add new non root user

```
docker exec -it rabbit-atom-1 /bin/bash
rabbitmqctl add_user USERNAME PASSWD
rabbitmqctl set_user_tags USERNAME administrator
rabbitmqctl set_permissions -p / USERNAME ".*" ".*" ".*"
```
Provide backend with newly created USERNAME and PASSWD
~TODO make automatical using some configs~


## Starting the pipeline 

### Add models to their respective modules using DVC
    To use your Oauth login tranfer it to the server and execute
    ```dvc remote modify classifier_dvc gdrive_user_credentials_file {CRED_PATH}```
    where CRED_PATH is the location of DVC Gdrive Oauth credentials
### Configure environment variables for accessing remote ollama server using variables:
    SERVER_HOST
    SERVER_PORT
    BASE_AUTH_USER_AND_PASSWORD
### Configure path to configs for launching docker compose for the pipeline
    export config_path={path to config.yml}
### Build images for all modules by using commant from root project directory
    ```docker compose build``` 
### Edit config.yml to use valid values to access certificates sftp input and output, rabbitmq queues, DB and file storage
### Configure scaling of metadata extractors by changing services.metadata_extractor.deploy.replicas to your desired number of replicas. 
### Run the following command:
   ```docker-compose up -d```

