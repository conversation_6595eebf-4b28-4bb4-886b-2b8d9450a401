project_name: "put your data" # for monitoring service
app_name: "put your data"
CRYPTOGRAPHY_KEY: 'put your data' # ENCRYPTION_KEY = Fernet.generate_key()
TENANT_NAME: "put your data"

s3:
    S3_BUCKET_NAME: "put your data"
    S3_REGION: "us-east-1"
    S3_ENDPOINT_URL: null  # Set to null for AWS S3, or specify custom endpoint for S3-compatible storage

# ------------------  S3 INBOX (DOWNLOADER)  ------------------
s3_downloader:
  bucket:         rr-inbox-dev
  prefix:         ""               # logical folder;
  region:         eu-central-1
  secure:         true
  endpoint:       ""               # optional
  access_key:     "" #"${AWS_ACCESS_KEY_ID}"      optional (use IAM role if blank)
  secret_key:     "" #"${AWS_SECRET_ACCESS_KEY}"  optional
  # tweaked behaviour
  new_file_cutoff:   300           # (sec)
  download_timeout:  60            # (sec) per-object read timeout

# ------------------  S3 OUTBOX (UPLOADER)  -------------------
s3_uploader:
  bucket:         rr-outbox-dev
  prefix:         ""
  region:         eu-central-1
  secure:         true
  endpoint:       ""
  access_key:     ""
  secret_key:     ""

# ------------------  RAW STORAGE (RAWCFG)  -------------------
raw_storage:
  type:        s3                  # s3 | gcp
  prefix:      "raw/"
  s3_bucket:   rr-raw-dev
  s3_region:   eu-central-1
  s3_secure:   true
  s3_endpoint: ""

minio:
    MINIO_URI: "127.0.0.1:9015"
    MINIO_ACCESS_KEY: "put your data"
    MINIO_SECRET_KEY: "put your data"
    MINIO_FILES_BUCKET: 'from-sftp'
    MINIO_OBJECT_URI_PREFIX: 'put your data'
    MINIO_SECURE: true
    MINIO_EXPIRATION_TIME: 1209600 # its 14 days in sec
    MINIO_PRESIGNED_URL_HOST: "put your data" # template: "server.domain.com:9015/"
    MINIO_PRESIGNED_URL_SECURE: true

sftp_remote:
    SFTP_HOST: "127.0.0.1"
    SFTP_PORT: '2222'
    SFTP_USER: "put your data"
    SFTP_PASSWORD: "put your data"
    SFTP_PATH: 'Documents'

sftp_upload:
    SFTP_HOST: "127.0.0.1"
    SFTP_PORT: '2223'
    SFTP_USER: "put your data"
    SFTP_PASSWORD: "put your data"
    SFTP_PATH: 'Documents'
    SORT_NAMING_BY_DATE: False  # True / False if client=ANS SORT_NAMING_BY_DATE=True

rabbitmq:
    RABBITMQ_HOST: "localhost"
    RABBITMQ_PORT: '5682'
    RABBITMQ_USERNAME: "put your data"
    RABBITMQ_PASSWORD: "put your data"
    RABBITMQ_TO_CLASSIFY_QUEUE_NAME: 'to_classify'
    RABBITMQ_TO_SPLIT_QUEUE_NAME: 'to_split'
    RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME: 'to_extract_metadata'
    RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME: 'to_metadata_postprocess'
    RABBITMQ_TO_VALIDATE_QUEUE_NAME: 'to_validate'
    RABBITMQ_TO_QA_POSTPROCESS_QUEUE_NAME: 'to_qa_postprocess'
    RABBITMQ_TO_UPLOAD_QUEUE_NAME: 'to_upload'
    REMOTE_SSL_CAFILE_PATH:  "/etc/nginx/ssl/.."
    REMOTE_SSL_CERTFILE_PATH: "/etc/nginx/ssl/.."
    REMOTE_SSL_KEYFILE_PATH: "/etc/nginx/ssl/.."
    REMOTE_RABBITMQ_HOST: "put your data"
    REMOTE_RABBITMQ_PORT: '5671'
    REMOTE_RABBITMQ_USERNAME: "put your data"
    REMOTE_RABBITMQ_PASSWORD: "put your data"
    REMOTE_RABBITMQ_VHOST: "/"
    REMOTE_RABBITMQ_TO_BACKEND_QA_QUEUE_NAME: 'put your data'
    REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME: "put your data"
    REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME: "put your data"
    REMOTE_RABBITMQ_FROM_BACKEND_SCREENSHOT: "put your data"

pgsql:
    PGSQL_HOST: "localhost"
    PGSQL_PORT: '5438'
    PGSQL_USERNAME: "put your data"
    PGSQL_PASSWORD: "put your data"
    PGSQL_DB_NAME: "put your data"
    PGSQL_HOST_REMOTE: "put your data"
    PGSQL_PORT_REMOTE: "put your data"
    PGSQL_USERNAME_REMOTE: "put your data"
    PGSQL_PASSWORD_REMOTE: "put your data"
    PGSQL_DB_NAME_REMOTE: "put your data"
    PGSQL_SSL_MODE_REMOTE: "require"
    RETENTION_HOURS: 340
    RETENTION_HOURS_REMOTE: 740

monitoring:
    MONITOR_HOST: "http://_address_here_"
    MONITOR_PORT: "put your data"

downloader:
    SUPPORTED_EXTENTIONS: ['pdf', 'tif', 'tiff', 'rtf', 'docx'] #['pdf', 'tif', 'tiff', 'msg', 'rtf', 'eml', 'zip', 'docx']
    SUPPORTED_EXTENTIONS_CONTAINERS: [] #['msg', 'eml', 'zip',]
    SUPPORTED_EXTENTIONS_DOCUMENTS_PREPROCESS: ['tif', 'tiff', 'rtf', 'docx']
    CHANNEL: "sftp" # "servicebus_queue" or "servicebus_topic" or "sftp"

classifier:
    DEVICE: cuda:0 # usually cpu or cuda:0
    GENERAL_MODEL_PATH: './general_model.pkl'
    PACKET_MODEL_PATH: './packet_model.pkl'

splitter:
    MINIMAL_CONFIDENCE_THRESHOLD: 0.5

metadata_extractor:
    DEVICE: cuda:0 # usually cpu or cuda:0


validate_and_route:
    MINIMAL_CLASSIFICATION_CONFIDENCE: 0.985
    MINIMAL_METADATA_CONFIDENCE: 0.985
    FORCE_ROUTE: 1 # 0 - disable; 1 - force all to qa tool; 2 - force all to upload
    MINIMUM_SIZE_OF_LARGE_FILE: 250
    METADATA_DISPLAY: ['namingData', 'metaData']   # ['namingData', 'metaData'] to be displayed on the frontend
    CHUNK_SIZE: 200  # default median size of chunk
    CHUNK_SIZE_RANGE: 100     # chunk can be CHUNK_SIZE +/- CHUNK_SIZE_RANGE
    HEADER_CONF_THRESHOLD: 0.91    # minimal conf of 1st page classification nodel

uploader:
    ADD_VALIDATION_REPORT_DATA: no
    CHANNEL: "sftp" # "servicebus_topic" or "sftp"

azure_uploader:
    AZURE_STORAGE_CONNECTION_STRING: "your_connection_string_here"
    AZURE_STORAGE_CONTAINER_NAME: "rr999"
    SERVICE_BUS_CONNECTION_STRING: 'your_connection_string_here'
    SERVICE_BUS_TOPIC_NAME: 'put your data'

servicebus:
    SERVICE_BUS_CONNECTION_STRING: 'your_connection_string_here'
    TOPIC_NAME: 'put your data'
    QUEUE_NAME: 'put your data'
    SUBSCRIPTION_NAME: 'put your data'
