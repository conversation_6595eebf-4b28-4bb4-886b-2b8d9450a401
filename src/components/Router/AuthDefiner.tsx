import { FC, useEffect } from "react";
import { Navigate } from "react-router-dom";
import { refreshToken } from "../../api/tokenRefresh";
import { isMoreThanSevenDaysAgo } from "../../helpers/helperFunctions.ts";
import { useAuthContext } from "../../hooks/useAuth.ts";

interface Props {
  Component?: FC;
}

export const AuthDefiner: FC<Props> = ({ Component }) => {
  const { token, date, twoFactorPassed } = useAuthContext();

  useEffect(() => {
    if (token && date && isMoreThanSevenDaysAgo(date)) {
      refreshToken();
    }
  }, [date, token]);

  if (token && twoFactorPassed) return <Navigate to={"/home"} replace />;

  if ((!token || !twoFactorPassed) && Component) return <Component />;

  return <Navigate to={"/login"} replace />;
};
