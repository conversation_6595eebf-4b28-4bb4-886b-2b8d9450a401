import { Modal, Paper, Typography } from "@mui/material";
import { Dispatch, FC, SetStateAction, useCallback } from "react";

interface Props {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  message: string;
}

export const ForgotPasswordModal: FC<Props> = ({ open, setOpen, message }) => {
  const closeModal = useCallback(() => setOpen(false), [setOpen]);

  return (
    <Modal
      sx={{ height: "100%", display: "flex", msUserSelect: "none" }}
      open={open}
      onClose={closeModal}
    >
      <Paper
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          margin: "auto",
          padding: "24px 16px",
          userSelect: "none",
          "&:focus": {
            outline: "none",
            boxShadow: "none",
          },
        }}
      >
        <Typography>{message}</Typography>
      </Paper>
    </Modal>
  );
};
