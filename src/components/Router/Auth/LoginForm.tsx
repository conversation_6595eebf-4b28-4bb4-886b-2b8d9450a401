import { yup<PERSON>esolver } from "@hookform/resolvers/yup";
import { Box, Button, CircularProgress, Link, Tooltip } from "@mui/material";
import React, { useCallback, useMemo } from "react";
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { useAuthContext } from "../../../hooks/useAuth.ts";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { ResponseError } from "../../../store/queries/users/types/User.ts";
import { useToken } from "../../../store/queries/users/users.query.ts";
import { UserPasswordField } from "../../Admin/Users/<USER>/UserPasswordField.tsx";
import { UserTextField } from "../../Admin/Users/<USER>/UserTextField.tsx";
import { FormValidator } from "../../Admin/Users/<USER>/FormValidator.tsx";
import { LoginValidationSchema } from "./utils/LoginValidationSchema.ts";

const styles: StyleCreator<"container" | "section"> = () => ({
  container: {
    margin: "auto",
    display: "flex",
    width: "100%",
    maxWidth: "300px",
    justifyContent: "center",
    alignItems: "center",
    gap: 2,
    flexDirection: "column",
    position: "relative",
  },
  section: { display: "flex", gap: "8px", width: "100%" },
});

const LoginForm: React.FC = () => {
  const c = useStyleCreator(styles);
  const navigate = useNavigate();

  const { login } = useAuthContext();
  const [getToken, { isError, isLoading, error }] = useToken();

  const FormMethods = useForm({
    resolver: yupResolver(LoginValidationSchema),
  });

  const errorMessage = useMemo(() => {
    if (!isError) return null;
    const typedError = error as ResponseError;

    if (!typedError.data) return "Unknown error";

    if (typedError.data.detail instanceof Array) {
      return typedError.data.detail.map(({ msg }) => msg);
    }

    return typedError.data.detail;
  }, [error, isError]);

  const onSubmit: SubmitHandler<any> = useCallback(
    (data) =>
      getToken(data)
        .unwrap()
        .then((response) => {
          login(response);
          navigate("/two-factor", { replace: true });
        }),
    [getToken, login, navigate],
  );

  const toResetPassword = useCallback(
    () => navigate("/reset-password"),
    [navigate],
  );

  return (
    <FormProvider {...FormMethods}>
      <FormValidator schema={LoginValidationSchema} />
      <Box
        sx={{ height: "100%", display: "flex", alignItems: "center" }}
        component={"form"}
        onSubmit={FormMethods.handleSubmit(onSubmit)}
      >
        <Box sx={c.container}>
          <Box sx={c.section}>
            <UserTextField
              name={"username"}
              label={"Email"}
              placement={"right"}
            />
          </Box>
          <Box sx={c.section}>
            <UserPasswordField
              name={"password"}
              label={"Password"}
              placement={"right"}
            />
          </Box>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: "8px",
              width: "100%",
            }}
          >
            <Tooltip title={isError ? errorMessage : null} placement={"right"}>
              <Button
                type="submit"
                disabled={!FormMethods.formState.isValid || isLoading}
                variant="contained"
                fullWidth
                color={isError ? "error" : "primary"}
              >
                {isLoading && <CircularProgress size={24} />}
                {!isLoading && "Login"}
              </Button>
            </Tooltip>
            <Link
              typography={"caption"}
              onClick={toResetPassword}
              textAlign={"center"}
              sx={{ cursor: "pointer" }}
            >
              Forgot password?
            </Link>
          </Box>
        </Box>
      </Box>
    </FormProvider>
  );
};

export default LoginForm;
