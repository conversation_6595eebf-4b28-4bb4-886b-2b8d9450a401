import { yupResolver } from "@hookform/resolvers/yup";
import { Box, Button, CircularProgress, Tooltip } from "@mui/material";
import { useCallback, useMemo } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { ResponseError } from "../../../store/queries/users/types/User.ts";
import { useConfirmReset } from "../../../store/queries/users/users.query.ts";
import { UserPasswordField } from "../../Admin/Users/<USER>/UserPasswordField.tsx";
import { FormValidator } from "../../Admin/Users/<USER>/FormValidator.tsx";
import { ResetPasswordValidationSchema } from "./utils/ResetPasswordValidationSchema.ts";

export const ResetPassword = () => {
  const navigate = useNavigate();
  const { token } = useParams();

  const [resetPassword, { isError, error, isLoading }] = useConfirmReset();

  const FormMethods = useForm({
    resolver: yupResolver(ResetPasswordValidationSchema),
  });

  const errorMessage = useMemo(() => {
    if (!isError) return null;
    const typedError = error as ResponseError;

    if (!typedError.data) return "Unknown error";

    if (typedError.data.detail instanceof Array) {
      return typedError.data.detail.map(({ msg }) => msg);
    }

    return typedError.data.detail;
  }, [error, isError]);

  const onSubmit = useCallback(
    ({ password }) => {
      resetPassword({ confirmation_token: token, new_password: password })
        .unwrap()
        .then(() => navigate("/login"));
    },
    [navigate, resetPassword, token],
  );

  const toLogin = useCallback(
    () => navigate("/login", { replace: true }),
    [navigate],
  );

  return (
    <FormProvider {...FormMethods}>
      <FormValidator schema={ResetPasswordValidationSchema} />
      <Box
        sx={{ height: "100%", display: "flex", alignItems: "center" }}
        component={"form"}
        onSubmit={FormMethods.handleSubmit(onSubmit)}
      >
        <Box
          sx={{
            margin: "auto",
            display: "flex",
            width: "100%",
            maxWidth: "300px",
            justifyContent: "center",
            alignItems: "center",
            gap: 2,
            flexDirection: "column",
            position: "relative",
          }}
        >
          <Box sx={{ display: "flex", gap: "8px", width: "100%" }}>
            <UserPasswordField
              name={"password"}
              label={"Password"}
              placement={"right"}
            />
          </Box>
          <Box sx={{ display: "flex", gap: "8px", width: "100%" }}>
            <UserPasswordField
              name={"password_repeat"}
              label={"Confirm password"}
              placement={"right"}
            />
          </Box>
          <Box sx={{ display: "flex", gap: "8px", width: "100%" }}>
            <Button variant="contained" onClick={toLogin} fullWidth>
              Cancel
            </Button>
            <Tooltip title={isError ? errorMessage : null} placement={"right"}>
              <Button
                fullWidth
                type={"submit"}
                disabled={!FormMethods.formState.isValid || isLoading}
                variant="contained"
                color={isError ? "error" : "primary"}
              >
                {isLoading && <CircularProgress size={24} />}
                {!isLoading && "Reset"}
              </Button>
            </Tooltip>
          </Box>
        </Box>
      </Box>
    </FormProvider>
  );
};
