import { object, string } from "yup";
import { BaseString } from "../../../Admin/Users/<USER>/UserValidationSchema.ts";
import { PasswordValidationSchema } from "./PasswordValidationSchema.ts";

export const ResetPasswordValidationSchema = object().shape({
  given_name: BaseString("First Name")
    .matches(
      /^[a-zA-Z-\s]+$/,
      (params) => `${params.label} can't contain any special characters`,
    )
    .trim()
    .required("Field is required"),
  family_name: BaseString("Last Name")
    .matches(
      /^[a-zA-Z-\s]+$/,
      (params) => `${params.label} can't contain any special characters`,
    )
    .trim()
    .required("Field is required"),
  username: BaseString("Username").trim().required("Field is required"),
  password: PasswordValidationSchema.test(
    "Password",
    "Passwords dont match",
    (value, context) => {
      const { password_repeat } = context.parent as {
        password: string;
        password_repeat: string;
      };
      if (!password_repeat) return false;
      return value.trim() === password_repeat.trim();
    },
  ),
  password_repeat: string().trim().required("Password is required"),
});
