import { string } from "yup";

const PasswordPatterns = {
  OneLowercase: /.*[a-z].*/,
  OneUppercase: /.*[A-Z].*/,
  OneNumber: /.*[0-9].*/,
  OneSpecial: /.*[!@#$%^&*()_+-={}[]|:;'<>?,.\/~`].*/,
  OnlyLatin: /^[a-zA-Z0-9!@#$%^&*()_+-={}[]|:;'<>?,.\/~`]+$/,
};

export const PasswordValidationSchema = string()
  .trim()
  .required("Password is required")
  .min(12, "Password must be at least 12 characters long")
  .max(16, "Password must be maximum 16 characters long")
  .matches(
    PasswordPatterns.OnlyLatin,
    "Password can only contain Latin characters",
  )
  .matches(
    PasswordPatterns.OneLowercase,
    "Password must have at least one lowercase letter",
  )
  .matches(
    PasswordPatterns.OneUppercase,
    "Password must have at least one uppercase letter",
  )
  .matches(
    PasswordPatterns.OneSpecial,
    "Password must have at least one special character",
  )
  .matches(
    PasswordPatterns.OneNumber,
    "Password must have at least one number",
  );
