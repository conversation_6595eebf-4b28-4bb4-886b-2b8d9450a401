import * as yup from "yup";
import { BaseString } from "../../../Admin/Users/<USER>/UserValidationSchema.ts";

export const ForgotPasswordValidationSchema = yup
  .object()
  .shape({
    email: BaseString("Email")
      .matches(
        /^((?!\.)[\w-_.]*[^.])(@\w+)(\.\w+(\.\w+)?[^.\W])$/gim,
        "Value should be valid email format",
      )
      .matches(/^[^.].*[^.]$/, "The email couldn't start or finish with a dot")
      .matches(
        /^\S+\S+\S+$/,
        "The email shouldn't contain spaces into the string",
      )
      .matches(
        /^[a-zA-Z0-9@._-]+$/,
        "The email shouldn't contain special characters",
      )
      .trim()
      .required("Field is required"),
  })
  .required();
