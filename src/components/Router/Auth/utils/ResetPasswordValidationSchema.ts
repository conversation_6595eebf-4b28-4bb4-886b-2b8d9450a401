import { object, string } from "yup";
import { PasswordValidationSchema } from "./PasswordValidationSchema.ts";

export const ResetPasswordValidationSchema = object().shape({
  password: PasswordValidationSchema.test(
    "Password",
    "Passwords dont match",
    (value, context) => {
      const { password_repeat } = context.parent as {
        password: string;
        password_repeat: string;
      };
      if (!password_repeat) return false;
      return value.trim() === password_repeat.trim();
    },
  ),
  password_repeat: string().trim().required("Password is required"),
});
