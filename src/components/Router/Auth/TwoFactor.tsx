import { <PERSON>, <PERSON><PERSON>, Cir<PERSON><PERSON><PERSON>ress, TextField } from "@mui/material";
import {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useNavigate } from "react-router-dom";
import { useAuthContext } from "../../../hooks/useAuth.ts";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { ResponseError } from "../../../store/queries/users/types/User.ts";
import { useVerify2Factor } from "../../../store/queries/users/users.query.ts";
import { QRCodeModal } from "./QRCodeModal.tsx";

const styles: StyleCreator<"container" | "field" | "textRow"> = (theme) => ({
  container: {
    userSelect: "none",
    margin: "auto",
    display: "flex",
    width: "100%",
    maxWidth: "300px",
    justifyContent: "center",
    alignItems: "center",
    gap: 2,
    flexDirection: "column",
    position: "relative",
  },
  textRow: {
    userSelect: "none",
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  field: {
    minWidth: "45px",
    width: "45px",
    maxWidth: "45px",
    userSelect: "none",
    "& .MuiInputBase-input": {
      height: "56px",
      padding: "0",
      textAlign: "center",
      ...theme.typography.h5,
    },
  },
});

const replaceAtIndex = (
  setter: Dispatch<SetStateAction<string[]>>,
  index: number,
  newValue: string,
): void => {
  setter((prevState) => {
    const newArr = [...prevState];
    newArr[index] = newValue;
    return newArr;
  });
};

export const TwoFactor = () => {
  const c = useStyleCreator(styles);
  const navigate = useNavigate();

  const [verify2Factor, { isError, isLoading, error }] = useVerify2Factor();
  const {
    token: access_token,
    pass_two_factor,
    getAuthData,
  } = useAuthContext();

  const [token, setToken] = useState(["", "", "", "", "", ""]);

  const inputsRef = useRef<HTMLInputElement[]>([]);
  const isAutoValidated = useRef(false);

  const errorMessage = useMemo(() => {
    if (!isError) return null;

    const typedError = error as ResponseError;
    if (typedError.data.detail instanceof Array) {
      return typedError.data.detail.map(({ msg }) => msg);
    }

    return typedError.data.detail;
  }, [error, isError]);

  const isReadyToValid = useMemo(() => token.some((value) => !value), [token]);

  const validate = useCallback(() => {
    verify2Factor({ access_token, totp_code: token.join("") })
      .unwrap()
      .then(() => {
        const authData = getAuthData();
        localStorage.setItem(
          "userAuth",
          JSON.stringify({ ...authData, validated: true }),
        );

        pass_two_factor();
        navigate("/home", { replace: true });
      });
  }, [
    access_token,
    getAuthData,
    navigate,
    pass_two_factor,
    token,
    verify2Factor,
  ]);

  useEffect(() => {
    if (inputsRef.current[0]) {
      inputsRef.current[0].focus();
    }
  }, []);

  useEffect(() => {
    if (!isReadyToValid && !isAutoValidated.current) {
      validate();
      isAutoValidated.current = true;
    }
  }, [isReadyToValid, validate]);

  return (
    <Box sx={{ height: "100%", display: "flex", alignItems: "center" }}>
      <Box sx={c.container}>
        <Box sx={c.textRow}>
          {token.map((value, index) => (
            <TextField
              sx={c.field}
              inputRef={(el) => (inputsRef.current[index] = el)}
              value={value}
              onKeyDown={(event) => {
                const value = event.key;

                switch (true) {
                  case /^[a-zA-Z0-9]$/.test(value):
                    replaceAtIndex(setToken, index, value.slice(-1));
                    if (index < inputsRef.current.length - 1) {
                      inputsRef.current[index + 1].focus();
                    }
                    break;
                  case value === "Backspace":
                    replaceAtIndex(setToken, index, "");
                    if (index > 0) {
                      inputsRef.current[index - 1].focus();
                    }
                    break;
                  case value === "ArrowLeft":
                    if (index > 0) {
                      inputsRef.current[index - 1].focus();
                    }
                    break;
                  case value === "ArrowRight":
                    if (index < inputsRef.current.length - 1) {
                      inputsRef.current[index + 1].focus();
                    }
                    break;
                  case value === "Enter":
                    if (!isReadyToValid) validate();
                    break;
                  default:
                    break;
                }
              }}
              onPaste={(event) => {
                const code = event.clipboardData
                  .getData("Text")
                  .split("")
                  .filter((value) => !/[^a-zA-Z0-9]/.test(value));
                setToken(code.slice(0, 6));
                event.preventDefault();
              }}
            />
          ))}
        </Box>
        <Button
          disabled={isReadyToValid || isLoading}
          variant={"contained"}
          onClick={validate}
          fullWidth
        >
          {isLoading && <CircularProgress size={24} color={"primary"} />}
          {!isLoading && "validate"}
        </Button>
        <Box sx={{ color: "red", position: "absolute", bottom: "-40px" }}>
          {errorMessage}
        </Box>
        <QRCodeModal />
      </Box>
    </Box>
  );
};
