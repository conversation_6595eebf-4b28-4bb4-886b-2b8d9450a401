import { yupResolver } from "@hookform/resolvers/yup";
import { Box, Button, CircularProgress, Tooltip } from "@mui/material";
import React, { useCallback, useMemo, useState } from "react";
import { <PERSON><PERSON><PERSON>ider, SubmitHandler, useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { ResponseError } from "../../../store/queries/users/types/User.ts";
import { useResetPassword } from "../../../store/queries/users/users.query.ts";
import { UserTextField } from "../../Admin/Users/<USER>/UserTextField.tsx";
import { FormValidator } from "../../Admin/Users/<USER>/FormValidator.tsx";
import { ForgotPasswordModal } from "./ForgotPasswordModal.tsx";
import { ForgotPasswordValidationSchema } from "./utils/ForgotPasswordValidationSchema.ts";

export const ForgotPassword: React.FC = () => {
  const navigate = useNavigate();
  const [resetPassword, { isError, error, isLoading, data }] =
    useResetPassword();

  const FormMethods = useForm({
    resolver: yupResolver(ForgotPasswordValidationSchema),
  });

  const [open, setOpen] = useState(false);
  const openModal = useCallback(() => setOpen(true), []);

  const errorMessage = useMemo(() => {
    if (!isError) return null;
    const typedError = error as ResponseError;

    if (!typedError.data) return "Unknown error";

    if (typedError.data.detail instanceof Array) {
      return typedError.data.detail.map(({ msg }) => msg);
    }

    return typedError.data.detail;
  }, [error, isError]);

  const onSubmit: SubmitHandler<any> = useCallback(
    (data) => {
      resetPassword(data)
        .unwrap()
        .then(() => openModal());
    },
    [openModal, resetPassword],
  );

  const toLogin = useCallback(
    () => navigate("/login", { replace: true }),
    [navigate],
  );

  return (
    <FormProvider {...FormMethods}>
      <FormValidator schema={ForgotPasswordValidationSchema} />
      <Box
        sx={{ height: "100%", display: "flex", alignItems: "center" }}
        component={"form"}
        onSubmit={FormMethods.handleSubmit(onSubmit)}
      >
        <Box
          sx={{
            margin: "auto",
            display: "flex",
            width: "100%",
            maxWidth: "300px",
            justifyContent: "center",
            alignItems: "center",
            gap: 2,
            flexDirection: "column",
            position: "relative",
          }}
        >
          <Box sx={{ display: "flex", gap: "8px", width: "100%" }}>
            <UserTextField name={"email"} label={"Email"} placement={"right"} />
          </Box>
          <Box sx={{ display: "flex", gap: "8px", width: "100%" }}>
            <Button
              type="submit"
              variant="contained"
              onClick={toLogin}
              fullWidth
            >
              Back
            </Button>
            <Tooltip title={isError ? errorMessage : null} placement={"right"}>
              <Button
                type="submit"
                disabled={!FormMethods.formState.isValid || isLoading}
                variant="contained"
                fullWidth
                color={isError ? "error" : "primary"}
              >
                {isLoading && <CircularProgress size={24} />}
                {!isLoading && "Reset"}
              </Button>
            </Tooltip>
          </Box>
        </Box>
        <ForgotPasswordModal
          open={open}
          message={data?.details ?? ""}
          setOpen={setOpen}
        />
      </Box>
    </FormProvider>
  );
};
