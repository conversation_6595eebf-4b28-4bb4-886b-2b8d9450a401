import { yupResolver } from "@hookform/resolvers/yup";
import {
  Box,
  Button,
  CircularProgress,
  Link,
  Paper,
  Tooltip,
  Typography,
} from "@mui/material";
import { useCallback, useEffect, useMemo } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useNavigate, useParams } from "react-router-dom";
import { useAuthContext } from "../../../hooks/useAuth.ts";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { ResponseError } from "../../../store/queries/users/types/User.ts";
import {
  useAcceptInviteUser,
  useInviteInfo,
  useToken,
} from "../../../store/queries/users/users.query.ts";
import { UserPasswordField } from "../../Admin/Users/<USER>/UserPasswordField.tsx";
import { UserTextField } from "../../Admin/Users/<USER>/UserTextField.tsx";
import { FormValidator } from "../../Admin/Users/<USER>/FormValidator.tsx";
import { ResetPasswordValidationSchema } from "./utils/AcceptInviteValidationSchema.ts";

const styles: StyleCreator<"container" | "section"> = (theme) => ({
  container: {
    margin: "auto",
    display: "flex",
    width: "100%",
    maxWidth: "400px",
    justifyContent: "center",
    alignItems: "center",
    gap: 2,
    flexDirection: "column",
    position: "relative",
  },
  section: { display: "flex", gap: "8px", width: "100%" },
});

export const AcceptInvite = () => {
  const c = useStyleCreator(styles);
  const navigate = useNavigate();

  const { login } = useAuthContext();
  const { token } = useParams();

  const [getInviteInfo, { isLoading: isInfoLoading, isError: isInfoError }] =
    useInviteInfo();
  const [acceptInvite, { isError, isLoading, error }] = useAcceptInviteUser();
  const [getToken, { isLoading: isTokenLoading }] = useToken();

  const FormMethods = useForm({
    resolver: yupResolver(ResetPasswordValidationSchema),
  });

  const errorMessage = useMemo(() => {
    if (!isError) return null;

    const typedError = error as ResponseError;
    if (typedError.data.detail instanceof Array) {
      return typedError.data.detail.map(({ msg }) => msg);
    }

    return typedError.data.detail;
  }, [error, isError]);

  const onSubmit = useCallback(
    (data) => {
      acceptInvite({ ...data, access_token: token })
        .unwrap()
        .then(({ username }) => {
          getToken({ username, password: data.password })
            .unwrap()
            .then((response) => {
              login(response);
              navigate("/two-factor", { replace: true });
            });
        });
    },
    [acceptInvite, getToken, login, navigate, token],
  );

  const toLogin = useCallback(
    () => navigate("/login", { replace: true }),
    [navigate],
  );

  useEffect(() => {
    getInviteInfo({ invite_token: token })
      .unwrap()
      .then((data) => {
        FormMethods.reset({ ...data, password: "", password_repeat: "" });
      });
  }, [token]);

  if (isInfoLoading || isLoading || isTokenLoading)
    return (
      <Box
        sx={{
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <CircularProgress />
      </Box>
    );

  if (!isInfoLoading && isInfoError)
    return (
      <Box
        sx={{
          height: "100%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Paper sx={{ padding: "32px" }}>
          <Typography typography={"h6"}>Token has been expired</Typography>
          <Link
            sx={{ cursor: "pointer" }}
            typography={"caption"}
            onClick={() => navigate("/login")}
          >
            Back to login
          </Link>
        </Paper>
      </Box>
    );

  return (
    <FormProvider {...FormMethods}>
      <FormValidator schema={ResetPasswordValidationSchema} />
      <Box
        sx={{ height: "100%", display: "flex", alignItems: "center" }}
        component={"form"}
        onSubmit={FormMethods.handleSubmit(onSubmit)}
      >
        <Box sx={c.container}>
          <Box sx={c.section}>
            <UserTextField
              name={"given_name"}
              label={"First name"}
              placement={"top"}
              shrink
            />
            <UserTextField
              name={"family_name"}
              label={"Last name"}
              placement={"top"}
              shrink
            />
          </Box>
          <Box sx={c.section}>
            <UserTextField
              name={"username"}
              label={"Username"}
              placement={"right"}
              shrink
            />
          </Box>
          <Box sx={c.section}>
            <UserPasswordField
              name={"password"}
              label={"Password"}
              placement={"left"}
            />
            <UserPasswordField
              name={"password_repeat"}
              label={"Confirm password"}
              placement={"right"}
            />
          </Box>
          <Box sx={c.section}>
            <Button
              variant="contained"
              type={"reset"}
              onClick={toLogin}
              fullWidth
            >
              Cancel
            </Button>
            <Tooltip title={isError ? errorMessage : null} placement={"right"}>
              <Button
                type={"submit"}
                disabled={!FormMethods.formState.isValid || isLoading}
                variant="contained"
                fullWidth
              >
                {isLoading && <CircularProgress size={24} />}
                {!isLoading && "Accept Invite"}
              </Button>
            </Tooltip>
          </Box>
        </Box>
      </Box>
    </FormProvider>
  );
};
