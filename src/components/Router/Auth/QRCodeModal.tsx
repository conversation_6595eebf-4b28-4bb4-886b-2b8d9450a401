import { InfoOutlined } from "@mui/icons-material";
import {
  Box,
  Button,
  CircularProgress,
  Divider,
  Link,
  Modal,
  Paper,
  Tooltip,
} from "@mui/material";
import { useCallback, useEffect, useState } from "react";
import QRCode from "react-qr-code";
import { useNavigate } from "react-router-dom";
import { useAuthContext } from "../../../hooks/useAuth.ts";
import { useEnable2Factor } from "../../../store/queries/users/users.query.ts";

export const QRCodeModal = () => {
  const { token, logout } = useAuthContext();
  const navigate = useNavigate();
  const [enable2Factor, { isLoading, data, isSuccess }] = useEnable2Factor();

  const [open, setOpen] = useState(false);

  const openModal = useCallback(() => setOpen(true), []);
  const closeModal = useCallback(() => setOpen(false), []);

  useEffect(() => {
    enable2Factor({ access_token: token });
  }, [enable2Factor, token]);

  return (
    <>
      <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <Link
          typography={"caption"}
          sx={{ cursor: "pointer" }}
          onClick={() => {
            logout();
            navigate("/login");
          }}
        >
          Logout
        </Link>
        {isSuccess && (
          <>
            <Divider flexItem variant={"middle"} orientation={"vertical"} />
            <Link
              typography={"caption"}
              sx={{ cursor: "pointer" }}
              onClick={openModal}
            >
              Link Authenticator App
            </Link>
          </>
        )}
      </Box>
      <Modal
        sx={{ height: "100%", display: "flex", msUserSelect: "none" }}
        open={open}
        onClose={closeModal}
      >
        <Paper
          sx={{
            padding: "12px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "auto",
            minHeight: "300px",
            minWidth: "300px",
            userSelect: "none",
            "&:focus": {
              outline: "none",
              boxShadow: "none",
            },
            flexDirection: "column",
          }}
        >
          {isLoading && <CircularProgress />}
          {!isLoading && (
            <Box sx={{ display: "flex", flexDirection: "column", gap: "8px" }}>
              {isSuccess && (
                <QRCode
                  style={{ userSelect: "none", msUserSelect: "none" }}
                  value={data.details}
                  viewBox={`0 0 256 256`}
                />
              )}
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  gap: "8px",
                }}
              >
                <Button
                  sx={{ height: "24px" }}
                  fullWidth
                  variant={"contained"}
                  onClick={closeModal}
                >
                  Done
                </Button>
                <Tooltip title={"Scan via Authenticator App"}>
                  <InfoOutlined fontSize={"small"} />
                </Tooltip>
              </Box>
            </Box>
          )}
        </Paper>
      </Modal>
    </>
  );
};
