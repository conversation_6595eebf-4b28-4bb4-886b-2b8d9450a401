import { createBrowserRouter } from "react-router-dom";
import { testManagerAccess } from "../../helpers/Roles.ts";
import { HomePage } from "../../pages/HomePage.tsx";
import { UserCreate } from "../Admin/Users/<USER>/UserCreate.tsx";
import { UserFile } from "../Admin/Users/<USER>/UserFile.tsx";
import { UserList } from "../Admin/Users/<USER>/UserList.tsx";
import { AcceptInvite } from "./Auth/AcceptInvite.tsx";
import { ForgotPassword } from "./Auth/ForgotPassword.tsx";
import LoginForm from "./Auth/LoginForm.tsx";
import { ResetPassword } from "./Auth/ResetPassword.tsx";
import { TwoFactor } from "./Auth/TwoFactor.tsx";
import { AuthDefiner } from "./AuthDefiner.tsx";
import { AdminLayout } from "./Layouts/AdminLayout.tsx";
import { AppLayout } from "./Layouts/AppLayout.tsx";
import { Protected } from "./Protected.tsx";

export const AppRouter = createBrowserRouter([
  {
    path: "/",
    element: <AppLayout />,
    children: [
      {
        path: "login",
        element: <AuthDefiner Component={LoginForm} />,
      },
      {
        path: "reset-password",
        element: <AuthDefiner Component={ForgotPassword} />,
      },
      {
        path: "confirm_reset/:token",
        element: <AuthDefiner Component={ResetPassword} />,
      },
      {
        path: "invite/:token",
        element: <AuthDefiner Component={AcceptInvite} />,
      },
      {
        path: "two-factor",
        element: <AuthDefiner Component={TwoFactor} />,
      },
      {
        path: "home",
        element: <Protected Component={HomePage} />,
      },
      {
        path: "home/delegates",
        element: <Protected Component={() => <>delegates</>} />,
      },
      {
        path: "admin",
        element: (
          <Protected validator={testManagerAccess} Component={AdminLayout} />
        ),
        children: [
          {
            path: "user/list",
            element: <UserList />,
          },
          {
            path: "user/create",
            element: <UserCreate />,
          },
          {
            path: "user/file",
            element: <UserFile />,
          },
        ],
      },
      {
        index: true,
        element: <AuthDefiner />,
      },
      {
        path: "*",
        element: <AuthDefiner />,
      },
    ],
  },
]);
