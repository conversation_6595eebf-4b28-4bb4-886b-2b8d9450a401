import { Box, useTheme } from "@mui/material";
import { FC } from "react";
import { Outlet } from "react-router-dom";
import { DrawerMenu } from "../../Drawer/DrawerMenu.tsx";
import { VersionLabel } from "../../Widgets/VersionLabel.tsx";

export const AdminLayout: FC = () => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        [theme.breakpoints.down(1440)]: {
          height: "auto",
          flexDirection: "column-reverse",
        },
        width: "100%",
        padding: "32px 32px 10px 32px",
        alignItems: "center",
        gap: "32px",
      }}
    >
      <Box sx={{ width: "100%", maxWidth: "1440px", height: "100%" }}>
        <Outlet />
      </Box>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          gap: "8px",
          marginTop: "auto",
        }}
      >
        <DrawerMenu />
        <VersionLabel />
      </Box>
    </Box>
  );
};
