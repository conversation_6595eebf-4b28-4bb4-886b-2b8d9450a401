import { Box, CssBaseline } from "@mui/material";
import { FC, useEffect, useMemo } from "react";
import { Toaster } from "react-hot-toast";
import { Outlet, useLocation } from "react-router-dom";
import { useAuthContext } from "../../../hooks/useAuth.ts";
import { useWS } from "../../WSContext/useWS.ts";
import { VersionLabel } from "../../Widgets/VersionLabel.tsx";

export const AppLayout: FC = () => {
  const location = useLocation();
  const { token, twoFactorPassed } = useAuthContext();
  const WS = useWS();

  const isAuthLocation = useMemo(() => {
    const auth = [
      "/login",
      "/reset-password",
      "/confirm_reset",
      "/invite",
      "/two-factor",
    ];

    return auth.includes(location.pathname);
  }, [location.pathname]);

  useEffect(() => {
    if (<PERSON><PERSON>an(token) && twoFactorPassed) {
      WS.open();
    }
  }, [token, twoFactorPassed]);

  return (
    <Box
      onContextMenu={(e) => {
        if (!+import.meta.env.VITE_DEVELOPMENT_MODE) e.preventDefault();
      }}
      sx={{ height: "100vh" }}
    >
      <CssBaseline />
      <Toaster />
      <Outlet />
      {isAuthLocation && (
        <VersionLabel sx={{ position: "absolute", bottom: 5, left: 10 }} />
      )}
    </Box>
  );
};
