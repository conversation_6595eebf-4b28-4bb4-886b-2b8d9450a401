import { FC, useMemo } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { isMoreThanSevenDaysAgo } from "../../helpers/helperFunctions.ts";
import { useAuthContext } from "../../hooks/useAuth.ts";

interface Props {
  Component: FC;
  validator?: (role: string) => boolean;
}

export const Protected: FC<Props> = ({ Component, validator }) => {
  const location = useLocation();
  const { token, date, role, twoFactorPassed, validated, logout } =
    useAuthContext();

  const isValidRole = useMemo(() => {
    if (validator) {
      return validator(role);
    }

    return true;
  }, [role, validator]);

  if (date && isMoreThanSevenDaysAgo(date)) {
    localStorage.removeItem("userAuth");
    return <Navigate to={"/login"} replace />;
  }

  if (token && isValidRole && (twoFactorPassed || validated)) {
    return <Component />;
  } else {
    localStorage.setItem("redirect", location.pathname);
  }
  if (!isValidRole) logout();

  return <Navigate to={"/login"} replace />;
};
