import {
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  SxProps,
} from "@mui/material";
import { FC, ReactNode } from "react";

interface Props {
  name: string;
  Icon?: ReactNode;
  onClick?: () => void;
  selected: boolean;
  sx?: SxProps;
}

export const DrawerItem: FC<Props> = ({
  name,
  Icon,
  onClick,
  selected,
  sx,
}) => {
  return (
    <ListItem onClick={onClick} disablePadding>
      <ListItemButton selected={selected} sx={sx}>
        {Icon && <ListItemIcon>{Icon}</ListItemIcon>}
        <ListItemText primary={name} />
      </ListItemButton>
    </ListItem>
  );
};
