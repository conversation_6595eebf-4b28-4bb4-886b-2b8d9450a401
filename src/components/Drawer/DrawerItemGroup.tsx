import { ExpandLess, ExpandMore } from "@mui/icons-material";
import {
  Collapse,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import { FC, ReactNode, useCallback, useState } from "react";
import { DrawerItem } from "./DrawerItem.tsx";

interface Props {
  name: string;
  Icon: ReactNode;
  items: {
    name: string;
    Icon?: ReactNode;
    onClick?: () => void;
    selected: boolean;
  }[];
}

export const DrawerItemGroup: FC<Props> = ({ name, Icon, items }) => {
  const [open, setOpen] = useState(() =>
    items.some(({ selected }) => selected),
  );

  const handleClick = useCallback(() => {
    setOpen((prevState) => !prevState);
  }, []);

  return (
    <>
      <ListItemButton onClick={handleClick}>
        <ListItemIcon>{Icon}</ListItemIcon>
        <ListItemText primary={name} />
        {open ? <ExpandLess /> : <ExpandMore />}
      </ListItemButton>
      <Collapse in={open} timeout="auto" unmountOnExit>
        <List component="div" disablePadding>
          {items.map(({ name, Icon, onClick, selected }) => (
            <DrawerItem
              name={name}
              Icon={Icon}
              onClick={onClick}
              selected={selected}
              sx={{ pl: 4 }}
            />
          ))}
        </List>
      </Collapse>
    </>
  );
};
