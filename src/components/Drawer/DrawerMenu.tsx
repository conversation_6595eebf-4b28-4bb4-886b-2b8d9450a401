import {
  <PERSON>Awesome,
  Layers,
  Menu,
  Settings,
  SupervisorAccount,
} from "@mui/icons-material";
import { <PERSON>, But<PERSON>, Divider, Drawer, List } from "@mui/material";
import { FC, useCallback, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { testDelegateAccess, testManagerAccess } from "../../helpers/Roles.ts";
import { useAuthContext } from "../../hooks/useAuth.ts";
import { ResetPasswordModal } from "../Admin/Settings/ResetPasswordModal.tsx";
import { useDelegatedListContext } from "../DelegatedPackages/DelegatedContext.tsx";
import LogoutButton from "../Widgets/LogoutButton.tsx";
import { DrawerItem } from "./DrawerItem.tsx";
import { DrawerItemGroup } from "./DrawerItemGroup.tsx";

export const DrawerMenu: FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const { handleOpen } = useDelegatedListContext();

  const { role } = useAuthContext();

  const [open, setOpen] = useState(false);
  const [resetModalOpen, setResetModalOpen] = useState(false);

  const openResetModal = useCallback(() => {
    setResetModalOpen(true);
    setOpen(false);
  }, []);
  const closeResetModal = useCallback(() => setResetModalOpen(false), []);

  const toggleDrawer = useCallback(
    (newOpen: boolean) => () => {
      setOpen(newOpen);
    },
    [],
  );

  const testSelected = useCallback(
    (path) => {
      return location.pathname === `/${path}`;
    },
    [location],
  );

  const goTo = useCallback(
    (path: string) => () => {
      navigate(path);
      setOpen(false);
    },
    [navigate],
  );

  return (
    <div>
      <Button sx={{ minWidth: "36px" }} onClick={toggleDrawer(true)}>
        <Menu />
      </Button>
      <Drawer open={open} onClose={toggleDrawer(false)}>
        <Box
          sx={{
            width: "300px",
            height: "100%",
            display: "flex",
            flexDirection: "column",
          }}
          role="presentation"
          component={"nav"}
        >
          <img src={"/RRLogo.png"} width={"300px"} alt="RR Logo" />
          <Divider />
          <List>
            <DrawerItem
              name={"QA Tool"}
              Icon={<AutoAwesome />}
              onClick={goTo("/home")}
              selected={testSelected("home")}
            />
            {!testDelegateAccess(role) && (
              <DrawerItem
                name={"Delegated packages"}
                Icon={<Layers />}
                onClick={() => {
                  handleOpen();
                  setOpen(false);
                }}
                selected={false}
                sx={{ backgroundColor: "transparent" }}
              />
            )}
            {testManagerAccess(role) && (
              <DrawerItemGroup
                name={"Admin Page"}
                Icon={<SupervisorAccount />}
                items={[
                  {
                    name: "Users List",
                    selected: testSelected("admin/user/list"),
                    onClick: goTo("/admin/user/list"),
                  },
                  {
                    name: "Invite Users",
                    selected: testSelected("admin/user/create"),
                    onClick: goTo("/admin/user/create"),
                  },
                  {
                    name: "Import/Export Users",
                    selected: testSelected("admin/user/file"),
                    onClick: goTo("/admin/user/file"),
                  },
                ]}
              />
            )}
            <DrawerItemGroup
              name={"Settings"}
              Icon={<Settings />}
              items={[
                {
                  name: "Reset Password",
                  selected: false,
                  onClick: openResetModal,
                },
              ]}
            />
          </List>
          <Box
            sx={{
              padding: "8px 4px",
              display: "flex",
              gap: "8px",
              marginTop: "auto",
            }}
          >
            <LogoutButton />
          </Box>
        </Box>
      </Drawer>
      <ResetPasswordModal open={resetModalOpen} handleClose={closeResetModal} />
    </div>
  );
};
