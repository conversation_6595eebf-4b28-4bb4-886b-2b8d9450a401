import * as pdfjsLib from "pdfjs-dist";
import {
  Dispatch,
  FC,
  SetStateAction,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { IntegratedViewerPageWrapper } from "./IntegratedViewerPageWrapper.tsx";

interface Props {
  document: pdfjsLib.PDFDocumentProxy;
  page: number;
  currentPage: number;
  pageToScroll: number;
  setPageToScroll: Dispatch<SetStateAction<number | null>>;
  scale: number;
  rotation: number;
  defaultPageSize: number;
}

export const IntegratedViewerPage: FC<Props> = ({
  document,
  page,
  currentPage,
  pageToScroll,
  setPageToScroll,
  scale,
  rotation,
  defaultPageSize,
}) => {
  const [isRendered, setIsRendered] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const render = useCallback(async () => {
    const pdf_page = await document.getPage(page);
    const viewport = pdf_page.getViewport({ scale, rotation });

    const canvas = canvasRef.current;
    const context = canvas.getContext("2d");

    if (context) {
      canvas.width = viewport.width;
      canvas.height = viewport.height;
      const renderContext = {
        canvasContext: context,
        viewport,
      };
      await pdf_page.render(renderContext).promise;
      setIsRendered(true);
    }
  }, [document, page, rotation, scale]);

  useEffect(() => {
    const isDocumentValid = document && Boolean(document.getPage);
    const isCanvasInitialized = Boolean(canvasRef.current);
    const isInRange = page - 10 <= currentPage && currentPage <= page + 10;

    if (isDocumentValid && isCanvasInitialized && isInRange && !isRendered) {
      setIsRendered(false);
      render().finally(() => setIsRendered(true));
    }
  }, [currentPage, document, isRendered, page, render]);

  useEffect(() => {
    setIsRendered(false);
  }, [scale, rotation]);

  return (
    <IntegratedViewerPageWrapper
      page={page}
      pageToScroll={pageToScroll}
      setPageToScroll={setPageToScroll}
      rendered={isRendered}
    >
      <canvas ref={canvasRef} height={defaultPageSize} />
    </IntegratedViewerPageWrapper>
  );
};
