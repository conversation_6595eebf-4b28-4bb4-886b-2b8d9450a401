import { Box, CircularProgress } from "@mui/material";
import {
  <PERSON><PERSON><PERSON>,
  FC,
  ReactNode,
  SetStateAction,
  useEffect,
  useRef,
} from "react";

interface Props {
  page: number;
  pageToScroll: number;
  setPageToScroll: Dispatch<SetStateAction<number | null>>;
  children: ReactNode;
  rendered: boolean;
}

export const IntegratedViewerPageWrapper: FC<Props> = ({
  page,
  pageToScroll,
  setPageToScroll,
  children,
  rendered,
}) => {
  const ContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (pageToScroll === page && ContainerRef.current) {
      ContainerRef.current.scrollIntoView();
      setPageToScroll(null);
    }
  }, [page, pageToScroll, setPageToScroll]);

  return (
    <Box ref={ContainerRef} sx={{ width: "100%", position: "relative" }}>
      {children}
      {!rendered && (
        <Box
          sx={{
            position: "absolute",
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};
