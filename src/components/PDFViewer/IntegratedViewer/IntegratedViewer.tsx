import {
  ChevronLeft,
  ChevronRight,
  RotateLeft,
  RotateRight,
  ZoomIn,
  ZoomOut,
} from "@mui/icons-material";
import { Box, CircularProgress, IconButton, Input } from "@mui/material";
import * as pdfjsLib from "pdfjs-dist";
import "pdfjs-dist/web/pdf_viewer.css";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Waypoint } from "react-waypoint";
import { IntegratedViewerPage } from "./IntegratedViewerPage.tsx";

pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

type PdfViewerProps = {
  file: string & { error?: string };
  loading: boolean;
  size: number;
  startPage: number;
};

const IntegratedPdf: React.FC<PdfViewerProps> = ({
  startPage,
  file,
  loading,
}) => {
  const [pdfDocument, setPdfDocument] =
    useState<pdfjsLib.PDFDocumentProxy | null>(null);
  const [defaultPageHeight, setDefaultPageHeight] = useState(600);
  const [scale, setScale] = useState(1.5);
  const [angle, setAngle] = useState(0);
  const [pages, setPages] = useState<number | null>(null);
  const [editing, setEditing] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageToScroll, setPageToScroll] = useState<number | null>(null);
  const [inputEditValue, setInputEditValue] = useState<string>("1");

  const ContentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadPdf = async () => {
      try {
        const pdf = await pdfjsLib.getDocument(file).promise;
        setPdfDocument(pdf);
        setPages(pdf.numPages);
      } catch (err) {
        console.error("Failed to load PDF:", err);
      }
    };
    loadPdf();
  }, [file]);

  const handleZoomChange = (type: "zoom_in" | "zoom_out") => {
    setScale((prevScale) => {
      if (type === "zoom_in") return Math.min(prevScale + 0.2, 5);
      return Math.max(prevScale - 0.2, 0.5);
    });
  };
  const handleRotateChange = (direction: "positive" | "negative") => {
    setAngle((prevAngle) => {
      if (direction === "positive") {
        return (prevAngle + 90) % 360;
      }
      return (prevAngle - 90 + 360) % 360;
    });
  };

  const handlePageChange = useCallback(
    (direction: "prev" | "next") => {
      if (direction === "prev") {
        setPageToScroll(currentPage - 1);
        setCurrentPage(currentPage - 1);
        setInputEditValue((currentPage - 1).toString());
        return currentPage - 1;
      } else if (direction === "next") {
        setPageToScroll(currentPage + 1);
        setCurrentPage(currentPage + 1);
        setInputEditValue((currentPage + 1).toString());
        return currentPage + 1;
      }
    },
    [currentPage],
  );

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!editing) setEditing(true);
    setInputEditValue(event.target.value);
  };

  const submitPage = useCallback(() => {
    if (editing) {
      const pageToScroll = Math.min(
        Math.max(Number(inputEditValue) - startPage, 1),
        pages || 1,
      );
      setPageToScroll(pageToScroll);
      setCurrentPage(pageToScroll);
      setInputEditValue(pageToScroll.toString());
      setEditing(false);
    }
  }, [editing, inputEditValue, pages, startPage]);

  useEffect(() => {
    window.addEventListener("keypress", (e) => {
      if (e.key === "Enter" && editing) {
        submitPage();
      }
    });
    return () => window.removeEventListener("keypress", submitPage);
  }, [editing, submitPage]);

  useEffect(() => {
    if (pdfDocument) {
      pdfDocument
        .getPage(1)
        .then((res) =>
          setDefaultPageHeight(
            res.getViewport({ scale, rotation: angle }).height,
          ),
        );
    }
  }, [angle, pdfDocument, scale]);

  if (loading) return <CircularProgress />;

  return (
    <Box
      sx={{
        width: "100%",
        height: "calc(100% - 48px)",
        display: "flex",
        gap: "8px",
        flexDirection: "column",
      }}
    >
      <Box
        ref={ContentRef}
        sx={{
          overflow: "scroll",
          textAlign: "center",
          height: "calc(100% - 48px)",
        }}
      >
        {Array.from({ length: pages }, (_, i) => i + 1).map((page) => (
          <Box key={page} sx={{ position: "relative" }}>
            <Box sx={{ position: "absolute", top: "50%" }}>
              <Waypoint
                key={page}
                onEnter={() => {
                  if (!pageToScroll) {
                    setCurrentPage(page);
                    setInputEditValue(page.toString());
                  }
                }}
                fireOnRapidScroll={false}
                scrollableAncestor={ContentRef.current}
              />
            </Box>
            <IntegratedViewerPage
              document={pdfDocument}
              page={page}
              scale={scale}
              rotation={angle}
              currentPage={currentPage}
              pageToScroll={pageToScroll}
              setPageToScroll={setPageToScroll}
              defaultPageSize={defaultPageHeight}
            />
          </Box>
        ))}
      </Box>

      <Box
        sx={{
          width: "100%",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Box sx={{ display: "flex", gap: "8px" }}>
          <IconButton onClick={() => handleRotateChange("negative")}>
            <RotateLeft />
          </IconButton>
          <IconButton onClick={() => handleRotateChange("positive")}>
            <RotateRight />
          </IconButton>
        </Box>

        <Box sx={{ display: "flex", gap: "8px", alignItems: "center" }}>
          <IconButton
            disabled={currentPage === 1}
            onClick={() => handlePageChange("prev")}
          >
            <ChevronLeft />
          </IconButton>
          <Input
            sx={{ width: "60px" }}
            value={editing ? inputEditValue : currentPage + startPage}
            onChange={handleInputChange}
            onFocus={() => {
              setEditing(true);
              setInputEditValue((prev) => (+prev + startPage).toString());
            }}
            onBlur={() => submitPage()}
          />
          / {startPage + pages}
          <IconButton
            disabled={currentPage === pages}
            onClick={() => handlePageChange("next")}
          >
            <ChevronRight />
          </IconButton>
        </Box>

        <Box sx={{ display: "flex", gap: "8px" }}>
          <IconButton onClick={() => handleZoomChange("zoom_out")}>
            <ZoomOut />
          </IconButton>
          <IconButton onClick={() => handleZoomChange("zoom_in")}>
            <ZoomIn />
          </IconButton>
        </Box>
      </Box>
    </Box>
  );
};

export default IntegratedPdf;
