import {
  ChevronLeft,
  ChevronRight,
  RotateLeft,
  RotateRight,
  ZoomIn,
  ZoomOut,
} from "@mui/icons-material";
import { Box, CircularProgress, IconButton, Input } from "@mui/material";
import React, { FC, Fragment, useCallback, useEffect, useState } from "react";
import { Document } from "react-pdf";
import { PDFViewerPage } from "./ViewerPage.tsx";

interface DocumentViewerProps {
  file: string & { error?: string };
  loading: boolean;
  size: number;
}

const CustomPDFViewer: FC<DocumentViewerProps> = ({
  file,
  loading,
  size = 0,
}) => {
  const [pages, setPages] = useState<number>();
  const [scrollToPage, setScrollToPage] = useState<number | null>(1);
  const [activePage, setActivePage] = useState<number>(1);
  const [scale, setScale] = useState<number>(1);
  const [angle, setAngle] = useState(0);
  const [inputEditValue, setInputEditValue] = useState<string>("1");
  const [editing, setEditing] = useState<boolean>(false);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setPages(numPages);
  }

  const handleInputChange = useCallback(
    ({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
      if (!editing) setEditing(true);
      setInputEditValue((prevState) => {
        if (Number.isNaN(+value)) return prevState;

        return value;
      });
    },
    [editing],
  );

  const submitPage = useCallback(() => {
    const pageToScroll =
      +inputEditValue < 1 ? 1 : Math.min(+inputEditValue, pages);

    setActivePage(pageToScroll);
    setScrollToPage(pageToScroll);
    setInputEditValue(pageToScroll.toString());
  }, [inputEditValue, pages]);

  const handlePageChange = useCallback(
    (direction: "prev" | "next") => {
      switch (direction) {
        case "prev": {
          const value = Math.max(activePage - 1, 0);

          setInputEditValue(value.toString());
          return setScrollToPage(value);
        }
        case "next": {
          const value = Math.min(activePage + 1, pages);

          setInputEditValue(value.toString());
          return setScrollToPage(value);
        }
      }
    },
    [activePage, pages],
  );

  const handleScaleChange = useCallback((type: "zoom_in" | "zoom_out") => {
    switch (type) {
      case "zoom_in": {
        return setScale(
          (prevState) => +Math.min(prevState + 0.2, 2).toFixed(1),
        );
      }
      case "zoom_out": {
        return setScale(
          (prevState) => +Math.max(prevState - 0.2, 0.2).toFixed(1),
        );
      }
    }
  }, []);

  const handleRotate = useCallback((direction: "positive" | "negative") => {
    switch (direction) {
      case "positive": {
        return setAngle((prevState) => {
          if (prevState + 90 === 360) {
            return 0;
          }

          return prevState + 90;
        });
      }
      case "negative": {
        return setAngle((prevState) => {
          if (prevState - 90 === -360) {
            return 0;
          }

          return prevState - 90;
        });
      }
    }
  }, []);

  const handleEnter = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === "Enter" && editing) {
        submitPage();
        setEditing(false);
      }
    },
    [editing, submitPage],
  );

  useEffect(() => {
    window.addEventListener("keypress", handleEnter);

    return () => {
      window.removeEventListener("keypress", handleEnter);
    };
  }, [handleEnter]);

  if (file?.error) return <div>Server responsed with {file?.error} code</div>;
  if (file === "nofile") return <div>No current file</div>;
  if (loading) return <CircularProgress />;

  return (
    <>
      <Box
        sx={{
          width: "100%",
          height: "100%",
          overflow: "scroll",
          paddingRight: "16px",
          "&::-webkit-scrollbar-corner": {
            display: "none",
          },
        }}
      >
        <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
          <Box
            sx={{
              position: "relative",
              width: "100%",
              display: "flex",
              flexDirection: "column",
              gap: "8px",
            }}
          >
            {Array.from({ length: pages }, (_, i) => i + 1).map((page) => (
              <Fragment key={page}>
                <PDFViewerPage
                  page={page}
                  angle={angle}
                  activeViewPage={activePage}
                  activePage={scrollToPage}
                  size={size}
                  scale={scale}
                  setActivePage={setScrollToPage}
                  setActiveViewPage={setActivePage}
                  setInputEditValue={setInputEditValue}
                />
              </Fragment>
            ))}
          </Box>
        </Document>
      </Box>
      <Box sx={{ width: "100%", display: "flex", alignItems: "center" }}>
        <Box sx={{ width: "88px" }}>
          <IconButton onClick={() => handleRotate("negative")}>
            <RotateLeft />
          </IconButton>
          <IconButton onClick={() => handleRotate("positive")}>
            <RotateRight />
          </IconButton>
        </Box>
        <Box sx={{ margin: "auto", display: "flex", gap: "8px" }}>
          <IconButton
            disabled={activePage === 1}
            onClick={() => handlePageChange("prev")}
          >
            <ChevronLeft />
          </IconButton>
          <Box
            sx={{
              width: "100%",
              display: "flex",
              gap: "4px",
              alignItems: "center",
            }}
          >
            <Input
              sx={{ width: "30px" }}
              value={editing ? inputEditValue : activePage}
              onChange={handleInputChange}
              onFocus={() => setEditing(true)}
              onBlur={() => {
                submitPage();
                setEditing(false);
              }}
              onSubmit={() => {
                submitPage();
                setEditing(false);
              }}
            />
            / {pages}
          </Box>
          <IconButton
            disabled={activePage === pages}
            onClick={() => handlePageChange("next")}
          >
            <ChevronRight />
          </IconButton>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <IconButton onClick={() => handleScaleChange("zoom_out")}>
            <ZoomOut />
          </IconButton>
          <IconButton onClick={() => handleScaleChange("zoom_in")}>
            <ZoomIn />
          </IconButton>
        </Box>
      </Box>
    </>
  );
};

export default CustomPDFViewer;
