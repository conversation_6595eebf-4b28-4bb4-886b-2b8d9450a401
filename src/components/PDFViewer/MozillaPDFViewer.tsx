import { Box, CircularProgress } from "@mui/material";
import { FC, useEffect, useMemo, useRef, useState } from "react";
import { pdfjs } from "react-pdf";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;

interface DocumentViewerProps {
  host: string;
  file: string & { error?: string };
  loading: boolean;
  changeViewer: () => void;
}

export const MozillaPDFViewer: FC<DocumentViewerProps> = ({
  host = "https://mozilla.github.io/pdf.js/web",
  file,
  loading,
  changeViewer,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const [srcValid, setSrcValid] = useState<boolean>(false);

  const src = useMemo(
    () => `${host}/viewer.html?file=${encodeURIComponent(file)}`,
    [file, host],
  );

  useEffect(() => {
    fetch(src)
      .then((res) => {
        if (res.status === 200) {
          setSrcValid(true);
        } else {
          setSrcValid(false);
          changeViewer();
        }
      })
      .catch(() => {
        setSrcValid(false);
        changeViewer();
      });
  }, [changeViewer, src]);

  useEffect(() => {
    if (srcValid) {
      const viewer = document.createElement("iframe");

      viewer.style.width = "100%";
      viewer.style.height = "100%";
      viewer.src = src;

      containerRef.current.innerHTML = "";
      containerRef.current.appendChild(viewer);
    }
  }, [file, src, srcValid]);

  return (
    <Box sx={{ height: "100%", width: "100%" }}>
      {!loading && (
        <div ref={containerRef} style={{ height: "100%", width: "100%" }} />
      )}
      {loading && <CircularProgress />}
    </Box>
  );
};
