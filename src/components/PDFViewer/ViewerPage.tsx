import { Box, CircularProgress } from "@mui/material";
import React, { FC, useMemo, useState } from "react";
import { Page } from "react-pdf";
import { Waypoint } from "react-waypoint";

interface Props {
  page: number;
  activePage: number | null;
  activeViewPage: number;
  size: number;
  scale: number;
  angle: number;
  setActivePage: React.Dispatch<React.SetStateAction<number>>;
  setActiveViewPage: React.Dispatch<React.SetStateAction<number>>;
  setInputEditValue: React.Dispatch<React.SetStateAction<string>>;
}

export const PDFViewerPage: FC<Props> = ({
  page,
  activePage,
  activeViewPage,
  size,
  scale,
  angle,
  setActivePage,
  setActiveViewPage,
  setInputEditValue,
}) => {
  const [render, setRender] = useState<boolean>(false);
  const [pageRendered, setPageRendered] = useState<boolean>(false);

  const page_width = useMemo(() => {
    return size === 0 ? 800 : size - 16 * scale;
  }, [scale, size]);

  const page_container_height = useMemo(() => {
    return page_width * 1.41 * scale;
  }, [page_width, scale]);

  const paddingRight = useMemo(() => {
    if (scale > 1) return (scale - 1) * page_width;

    return 0;
  }, [page_width, scale]);

  return (
    <Waypoint
      onEnter={() => setRender(true)}
      onLeave={() => {
        setRender(false);
        setPageRendered(false);
      }}
      fireOnRapidScroll
    >
      {render || (page >= activeViewPage - 1 && page <= activeViewPage + 1) ? (
        <Box
          sx={{
            position: "relative",
            width: "100%",
            minHeight: pageRendered ? "auto" : `${page_width}px`,
            paddingLeft: `${paddingRight}px`,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Page
            onRenderSuccess={() => setPageRendered(true)}
            canvasBackground={"transparent"}
            width={page_width}
            pageNumber={page}
            renderAnnotationLayer={false}
            renderTextLayer={false}
            scale={scale}
            inputRef={(ref) => {
              if (ref && activePage && activePage === page) {
                ref.scrollIntoView();
                setActiveViewPage(page);
              }
            }}
            rotate={angle}
            loading={<CircularProgress />}
          />
          <Box sx={{ position: "absolute", top: "50%", width: "100%" }}>
            <Waypoint
              key={page}
              onEnter={() => {
                if (render) {
                  setActivePage(null);
                  setActiveViewPage(page);
                  setInputEditValue(page.toString());
                }
              }}
            />
          </Box>
        </Box>
      ) : (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            width: "100%",
            height: `${page_container_height}px`,
          }}
        >
          <CircularProgress />
        </Box>
      )}
    </Waypoint>
  );
};
