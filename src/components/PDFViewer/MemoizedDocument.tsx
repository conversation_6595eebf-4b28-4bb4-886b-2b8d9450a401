import { Box } from "@mui/material";
import { FC, memo, useCallback, useEffect, useRef, useState } from "react";
import { Page } from "react-pdf";

const PageComponent: FC<{
  pageNumber: number;
  setPageRef: (pageElement: any, index: number) => void;
  numPages: number;
}> = memo(
  ({ pageNumber, setPageRef, numPages }) => {
    const setRef = useCallback(
      (el) => {
        setPageRef(el, pageNumber - 1);
      },
      [setPageRef, pageNumber],
    );

    const [scale, setScale] = useState((window.innerWidth / 1080) * 0.85);

    useEffect(() => {
      const handleResize = () => {
        const newScale = (window.innerWidth / 1080) * 0.85;
        setScale(newScale);
      };

      window.addEventListener("resize", handleResize);

      return () => window.removeEventListener("resize", handleResize);
    }, []);

    return (
      <div ref={setRef} style={{ width: "100%" }}>
        <Page
          renderTextLayer={false}
          key={pageNumber}
          pageNumber={pageNumber}
          scale={scale}
        >
          <Box sx={styles.pageNumberBox}>
            {pageNumber} / {numPages}
          </Box>
        </Page>
      </div>
    );
  },
  (prevProps, nextProps) => prevProps.numPages === nextProps.numPages,
);

interface MemoizedDocumentPagesProps {
  numPages: number;
  setPageRef: (pageElement: any, index: number) => void;
}

const CHUNK_SIZE = 20;

const MemoizedDocumentPages: FC<MemoizedDocumentPagesProps> = memo(
  ({ numPages, setPageRef }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const loaderRef = useRef(null);

    const isPageInRange = (pageIndex) => {
      const lowerBound = Math.max(0, currentIndex - CHUNK_SIZE);
      const upperBound = Math.min(numPages, currentIndex + CHUNK_SIZE);
      return pageIndex >= lowerBound && pageIndex < upperBound;
    };

    const onScroll = (entries) => {
      const entry = entries[0];
      if (entry.isIntersecting) {
        setCurrentIndex((prevIndex) => {
          const newIndex = Math.min(numPages, prevIndex + CHUNK_SIZE);
          return newIndex;
        });
      }
    };

    useEffect(() => {
      const observer = new IntersectionObserver(onScroll, {
        root: null,
        threshold: 1.0,
      });

      if (loaderRef.current) {
        observer.observe(loaderRef.current);
      }

      return () => {
        if (loaderRef.current) {
          observer.unobserve(loaderRef.current);
        }
      };
    }, [loaderRef]);

    return (
      <>
        {Array.from({ length: numPages }, (_, index) =>
          isPageInRange(index) ? (
            <PageComponent
              key={`doc_page_${index + 1}`}
              pageNumber={index + 1}
              setPageRef={setPageRef}
              numPages={numPages}
            />
          ) : null,
        )}
        <div ref={loaderRef} />
      </>
    );
  },
);

const styles = {
  pageNumberBox: {
    position: "absolute",
    top: "16px",
    right: "16px",
    backgroundColor: "rgba(0, 0, 0, 0.75)",
    color: "white",
    padding: "2px 4px",
    borderRadius: "4px",
    fontSize: "0.875rem",
    fontWeight: "bold",
    opacity: 0.6,
  },
};

export default MemoizedDocumentPages;
