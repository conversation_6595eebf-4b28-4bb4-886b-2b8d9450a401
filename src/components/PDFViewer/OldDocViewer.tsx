import MenuIcon from "@mui/icons-material/Menu";
import {
  <PERSON>,
  Button,
  CircularProgress,
  Drawer,
  TextField,
} from "@mui/material";
import { debounce } from "lodash";
import React, {
  FC,
  memo,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { Document, pdfjs } from "react-pdf";
import Thumbnails from "../Thumbnails";
import MemoizedDocumentPages from "./MemoizedDocument.tsx";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;

interface DocumentViewerProps {
  file: string & { error?: string };
  loading: boolean;
  size: number;
}

const DocumentViewer: FC<DocumentViewerProps> = memo(
  ({ file, loading, size }) => {
    const [pageNumber, setPageNumber] = useState(1);
    const [zoom, setZoom] = useState(1);
    const [zoomPercentage, setZoomPercentage] = useState<number>(100);
    const [pages, setPages] = useState(size);

    const viewerRef = useRef(null);
    const pagesContainerRef = useRef(null);
    const pageRefs = useRef<Array<HTMLDivElement | null>>([]);

    const [drawerOpen, setDrawerOpen] = useState(false);

    const goToPreviousPage = () => setPageNumber(Math.max(pageNumber - 1, 1));
    const goToNextPage = () => setPageNumber(Math.min(pageNumber + 1, pages));
    const disablePrevious = pageNumber <= 1;
    const disableNext = pageNumber >= pages;
    const disableZoomOut = zoom <= 0.1;

    const onDocumentLoadSuccess = ({ numPages: loadedNumPages }) => {
      setPages(loadedNumPages);
    };

    const toggleDrawer = useCallback(() => {
      setDrawerOpen((prev) => !prev);
    }, []);

    const handleZoomIn = useCallback(() => {
      setZoom((prevZoom) => Math.min(prevZoom * 1.1, 3));
    }, []);

    const handleZoomOut = useCallback(() => {
      setZoom((prevZoom) => Math.max(prevZoom / 1.1, 0.65));
    }, []);

    const handleZoomChange = useCallback((event) => {
      setZoomPercentage(event.target.value);
    }, []);

    const applyZoom = useCallback(
      (event) => {
        if (event.key === "Enter") {
          const newZoom = zoomPercentage
            ? parseFloat(zoomPercentage.toString()) / 100
            : 1;
          setZoom(Math.min(Math.max(newZoom, 0.65), 3));
        }
      },
      [zoomPercentage],
    );

    const handlePageInputChange = useCallback((event) => {
      setPageNumber(event.target.value);
    }, []);

    const debouncedScrollToPage = useCallback(
      debounce((pageNum: number) => {
        const page = pageRefs.current[pageNum - 1];
        if (page) {
          page.scrollIntoView({ behavior: "smooth" });
        }
      }, 100),
      [],
    );

    const applyPageInput = useCallback(
      (event: React.KeyboardEvent) => {
        if (event.key === "Enter") {
          const newPageNumber = Math.max(
            1,
            Math.min(pages, Number(pageNumber)),
          );
          setPageNumber(newPageNumber);
          debouncedScrollToPage(newPageNumber);
        }
      },
      [pages, pageNumber, debouncedScrollToPage],
    );

    useEffect(() => {
      setZoomPercentage(zoom * 100);
    }, [zoom]);

    useEffect(() => {
      debouncedScrollToPage(pageNumber);
    }, [pageNumber]);

    const setRef = useCallback((pageElement, index) => {
      pageRefs.current[index] = pageElement;
    }, []);

    return (
      <Box
        sx={
          loading
            ? {
                ...styles.container,
                justifyContent: "center",
                alignItems: "center",
              }
            : styles.container
        }
      >
        {loading ? (
          <CircularProgress />
        ) : (
          <>
            <Box sx={styles.toolbox}>
              <Button
                variant="contained"
                onClick={goToPreviousPage}
                disabled={disablePrevious}
              >
                Previous
              </Button>
              <TextField
                type="number"
                value={pageNumber}
                onChange={handlePageInputChange}
                onKeyPress={applyPageInput}
                size="small"
                sx={{
                  width: "80px",
                  "& input[type=number]": {
                    "-moz-appearance": "textfield",
                  },
                  "& input[type=number]::-webkit-inner-spin-button, & input[type=number]::-webkit-outer-spin-button":
                    {
                      "-webkit-appearance": "none",
                      margin: 0,
                    },
                }}
              />
              <Button
                variant="contained"
                onClick={goToNextPage}
                disabled={disableNext}
              >
                Next
              </Button>
              <Button
                variant="contained"
                onClick={handleZoomOut}
                disabled={disableZoomOut}
              >
                -
              </Button>
              <TextField
                type="number"
                value={zoomPercentage}
                onChange={handleZoomChange}
                onKeyPress={applyZoom}
                inputProps={{ step: "10" }}
                size="small"
                sx={{
                  width: "80px",
                  "& input[type=number]": {
                    "-moz-appearance": "textfield",
                  },
                  "& input[type=number]::-webkit-inner-spin-button, & input[type=number]::-webkit-outer-spin-button":
                    {
                      "-webkit-appearance": "none",
                      margin: 0,
                    },
                }}
              />
              <Button variant="contained" onClick={handleZoomIn}>
                +
              </Button>
              <Button variant="contained" onClick={toggleDrawer}>
                <MenuIcon />
              </Button>
            </Box>

            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
              ref={viewerRef}
            >
              <Box sx={styles.documentContainer} ref={viewerRef}>
                <Box
                  sx={styles.pageContainer(zoom, zoomPercentage)}
                  ref={pagesContainerRef}
                >
                  <Document file={file} onLoadSuccess={onDocumentLoadSuccess}>
                    <MemoizedDocumentPages
                      numPages={pages}
                      setPageRef={setRef}
                    />
                  </Document>
                </Box>
              </Box>
            </Box>
          </>
        )}
        <Drawer
          anchor="left"
          open={drawerOpen}
          onClose={toggleDrawer}
          variant="persistent"
          sx={{ overflowX: "hidden" }}
        >
          <Box sx={styles.drawerBox}>
            <Document file={file}>
              <Thumbnails
                file={file}
                numPages={pages}
                setPageNumber={setPageNumber}
              />
            </Document>
          </Box>
        </Drawer>
      </Box>
    );
  },
);

const styles = {
  container: {
    display: "flex",
    flexDirection: "column",
    border: "1px solid white",
    width: "100%",
    height: "100%",
  },
  toolbox: {
    display: "flex",
    justifyContent: "center",
    gap: 2,
    p: 2,
    flexWrap: "wrap",
    height: "60px",
  },
  documentContainer: {
    overflow: "auto",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  pageContainer: (zoom: any, zoomPercentage: number) => ({
    overflow: "auto",
    maxHeight: "90vh",
    justifyContent: "center",
    display: "flex",
    transform: `scale(${zoom})`,
    transformOrigin: zoomPercentage < 100 ? "center" : "left top",
  }),
  pageNumberBox: {
    position: "absolute",
    top: "16px",
    right: "16px",
    backgroundColor: "rgba(0, 0, 0, 0.75)",
    color: "white",
    padding: "2px 4px",
    borderRadius: "4px",
    fontSize: "0.875rem",
    fontWeight: "bold",
    opacity: 0.6,
  },
  drawerBox: {
    width: 200,
    overflowY: "auto",
  },
};

export default DocumentViewer;
