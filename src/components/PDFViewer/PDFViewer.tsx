import {
  Box,
  CircularProgress,
  ToggleButton,
  ToggleButtonGroup,
  useTheme,
} from "@mui/material";
import axios from "axios";
import { FC, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { useScreenshotContext } from "../../hooks/useScreenshotContext.ts";
import { DelegatedLabel } from "../DelegatedPackages/DelegatedLabel.tsx";
import { DrawerMenu } from "../Drawer/DrawerMenu.tsx";
import { VersionLabel } from "../Widgets/VersionLabel.tsx";
import CustomPDFViewer from "./CustomPDFViewer.tsx";
import IntegratedPdf from "./IntegratedViewer/IntegratedViewer.tsx";
import { MozillaPDFViewer } from "./MozillaPDFViewer.tsx";
import { ViewerErrorOverlay } from "./ViewerErrorOverlay.tsx";

interface Props {
  file: string & { error?: string };
  loading: boolean;
  size: number;
  startPage?: number;
  setPagesCount?: (value: number) => void;
}

enum Viewers {
  MozillaV1 = 3,
  MozillaV2 = 2,
  Custom = 1,
  Integrated = 4,
}

export const PDFViewer: FC<Props> = (props) => {
  const theme = useTheme();

  const [viewer, setViewer] = useState<Viewers>(Viewers.Integrated);
  const [loading, setLoading] = useState<boolean>(true);
  const [isFetchError, setIsFetchError] = useState<boolean>(false);

  const FileFetched = useRef(false);

  const { isProcessing, ScreenshotContainer } = useScreenshotContext();

  const Viewer = useMemo(() => {
    switch (viewer) {
      case Viewers.Custom: {
        return CustomPDFViewer;
      }
      case Viewers.Integrated: {
        return IntegratedPdf;
      }
      case Viewers.MozillaV1: {
        return MozillaPDFViewer;
      }
      case Viewers.MozillaV2: {
        return MozillaPDFViewer;
      }
      default:
        return MozillaPDFViewer;
    }
  }, [viewer]);

  const isPDFShown = useMemo(
    () =>
      !props.loading &&
      props.file &&
      !isFetchError &&
      !props.file?.error &&
      props.file !== "nofile",
    [isFetchError, props.file, props.loading],
  );

  const host = useMemo(() => {
    switch (viewer) {
      case Viewers.MozillaV1: {
        return "https://dx-pdfjs-viewer-e82kw.ondigitalocean.app/hidden-reader";
      }
      case Viewers.MozillaV2: {
        return "https://mozilla.github.io/pdf.js/web";
      }
      default:
        return "https://mozilla.github.io/pdf.js/web";
    }
  }, [viewer]);

  const changeViewer = useCallback(
    () => setViewer((prevState) => (prevState - 1 === 0 ? 1 : prevState - 1)),
    [],
  );

  useEffect(() => {
    if (props.file && !FileFetched.current) {
      setIsFetchError(false);
      axios
        .get(props.file)
        .catch(() => setIsFetchError(true))
        .finally(() => {
          setLoading(false);
          FileFetched.current = true;
        });
    } else setLoading(false);
  }, [props]);

  useEffect(() => {
    isProcessing && setViewer(Viewers.Integrated);
    return () => {
      setViewer(Viewers.Integrated);
    };
  }, [isProcessing]);

  return (
    <Box
      id={"pdfContainer"}
      sx={{
        display: "flex",
        flexDirection: "column",
        [theme.breakpoints.down(1440)]: {
          flexDirection: "column-reverse",
        },
        gap: "8px",
        width: "100%",
        height: "100%",
        position: "relative",
        maxHeight: "100vh",
      }}
    >
      {!isPDFShown && (
        <Box
          sx={{
            width: "100%",
            display: "flex",
            justifyContent: "center",
            height: "100%",
            alignItems: "center",
          }}
        >
          {!props.loading && !props.file && <div>No PDF file</div>}
          {(props.loading || loading) && <CircularProgress />}
          {isFetchError && !loading && !props.loading && (
            <div>Error occurred during PDF file fetch</div>
          )}
          {props.file?.error && !loading && !props.loading && (
            <div>Server error occurred. Status code: {props.file?.error}</div>
          )}
          {!(props.loading || loading) && props.file === "nofile" && (
            <div>No current file</div>
          )}
        </Box>
      )}
      {isPDFShown && (
        <ErrorBoundary
          fallback={<ViewerErrorOverlay changeViewer={changeViewer} />}
        >
          <Viewer {...props} changeViewer={changeViewer} host={host} />
          <ScreenshotContainer />
        </ErrorBoundary>
      )}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          gap: "8px",
          marginTop: "auto",
        }}
      >
        <DrawerMenu />
        <VersionLabel />
        <DelegatedLabel />
        {isPDFShown && (
          <ToggleButtonGroup
            color="primary"
            value={viewer}
            exclusive
            onChange={(_, value) => {
              if (value) {
                setViewer(value as Viewers);
              }
            }}
            sx={{ marginLeft: "auto" }}
          >
            <ToggleButton sx={{ height: "35px" }} value={Viewers.Integrated}>
              Integrated
            </ToggleButton>
            <ToggleButton
              sx={{ height: "35px" }}
              value={Viewers.MozillaV1}
              disabled={isProcessing}
            >
              Mozilla V1
            </ToggleButton>
            <ToggleButton
              sx={{ height: "35px" }}
              value={Viewers.MozillaV2}
              disabled={isProcessing}
            >
              Mozilla V2
            </ToggleButton>
            <ToggleButton sx={{ height: "35px" }} value={Viewers.Custom}>
              Custom
            </ToggleButton>
          </ToggleButtonGroup>
        )}
      </Box>
    </Box>
  );
};
