import { Box, Typography } from "@mui/material";
import { FC, useEffect, useRef, useState } from "react";
import { useErrorBoundary } from "react-error-boundary";

interface Props {
  changeViewer: () => void;
}

export const ViewerErrorOverlay: FC<Props> = ({ changeViewer }) => {
  const { resetBoundary } = useErrorBoundary();
  const [countdown, setCountdown] = useState(3);
  const countdownRef = useRef(null);

  useEffect(() => {
    countdownRef.current = window.setInterval(() => {
      setCountdown((prevState) => prevState - 1);
    }, 1000);

    return () => {
      window.clearTimeout(countdownRef.current);
    };
  }, []);

  useEffect(() => {
    if (countdown === 0) {
      window.clearInterval(countdownRef.current);
      changeViewer();
      setCountdown(3);
      resetBoundary();
    }
  }, [changeViewer, countdown, resetBoundary]);

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100%",
        width: "100%",
      }}
    >
      <Typography>Ups.... Something gone wrong</Typography>
      <Typography>
        Switching to fallback PDF Viewer in... {countdown}
      </Typography>
    </Box>
  );
};
