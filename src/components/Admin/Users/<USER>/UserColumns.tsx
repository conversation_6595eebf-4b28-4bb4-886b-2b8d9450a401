import { GridColDef } from "@mui/x-data-grid";

export const columns: GridColDef[] = [
  { flex: 1, field: "username", headerName: "Username" },
  { flex: 1, field: "email", headerName: "Email" },
  { flex: 1, field: "given_name", headerName: "First Name" },
  { flex: 1, field: "family_name", headerName: "Last Name" },
  {
    flex: 1,
    field: "allowed_clients",
    headerName: "Allowed Clients",
    sortable: false,
  },
  {
    flex: 1,
    field: "user_type",
    headerName: "Role",
    valueFormatter: (value: string) => {
      switch (value) {
        case "coordinator":
          return "delegate";
        default:
          return value;
      }
    },
  },
];
