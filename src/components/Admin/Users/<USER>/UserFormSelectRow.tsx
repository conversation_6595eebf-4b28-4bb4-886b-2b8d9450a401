import { Undo } from "@mui/icons-material";
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  MenuItem,
  OutlinedInput,
  Select,
  Tooltip,
  Typography,
} from "@mui/material";
import { FC, useCallback } from "react";
import { Controller, useFormContext } from "react-hook-form";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../../hooks/useStyleCreator.ts";

interface Props {
  name: string;
  label: string;
  items: { value: string; text: string }[];
  multiple?: boolean;
}

const styles: StyleCreator<"container" | "label" | "undo"> = (theme) => ({
  container: {
    display: "flex",
    alignItems: "center",
    gap: "12px",
  },
  label: {
    minWidth: "100px",
    width: "fit-content",
  },
  undo: {
    minWidth: "40px",
    minHeight: "40px",
  },
});

export const UserFormSelectRow: FC<Props> = ({
  name,
  label,
  items,
  multiple,
}) => {
  const c = useStyleCreator(styles);
  const { resetField } = useFormContext();

  const handlerReset = useCallback(() => resetField(name), [name, resetField]);

  return (
    <Box sx={c.container}>
      <Typography typography={"h6"} sx={c.label}>
        {label}
      </Typography>
      <Controller
        name={name}
        render={({ field: { value, onChange, ref }, fieldState }) => {
          return (
            <Tooltip title={fieldState.error?.message ?? null}>
              <FormControl fullWidth>
                {!multiple && (
                  <Select
                    value={value}
                    onChange={onChange}
                    ref={ref}
                    id={`${name}-${label}`}
                    input={<OutlinedInput size={"small"} />}
                    error={fieldState.invalid}
                    variant={"outlined"}
                    renderValue={(value) =>
                      items.find((item) => value === item.value).text
                    }
                  >
                    {items.map(({ text, value: rowValue }) => {
                      return (
                        <MenuItem value={rowValue}>
                          <Checkbox
                            disableRipple
                            checked={value === rowValue}
                          />
                          {text}
                        </MenuItem>
                      );
                    })}
                  </Select>
                )}
                {multiple && (
                  <Select
                    variant={"outlined"}
                    value={value}
                    onChange={onChange}
                    ref={ref}
                    id={`${name}-${label}`}
                    multiple
                    input={<OutlinedInput size={"small"} />}
                    renderValue={(selected: string[]) => selected.join(", ")}
                    error={fieldState.invalid}
                  >
                    {items.map(({ text, value: rowValue }) => {
                      return (
                        <MenuItem value={rowValue}>
                          <Checkbox
                            disableRipple
                            checked={value.includes(rowValue)}
                          />
                          {text}
                        </MenuItem>
                      );
                    })}
                  </Select>
                )}
              </FormControl>
            </Tooltip>
          );
        }}
      />
      <Button onClick={handlerReset} sx={c.undo}>
        <Undo />
      </Button>
    </Box>
  );
};
