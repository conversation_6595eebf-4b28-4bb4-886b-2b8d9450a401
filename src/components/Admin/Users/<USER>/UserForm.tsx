import { yup<PERSON>esolver } from "@hookform/resolvers/yup";
import { Close } from "@mui/icons-material";
import { Box, Button, Typography } from "@mui/material";
import * as _ from "lodash";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import toast from "react-hot-toast";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../../hooks/useStyleCreator.ts";
import { PreparedClients } from "../../../../store/queries/clients/types/Client.ts";
import {
  ResponseError,
  User,
} from "../../../../store/queries/users/types/User.ts";
import { useUpdateUser } from "../../../../store/queries/users/users.query.ts";
import { FormValidator } from "../utils/FormValidator.tsx";
import { UserValidationSchema } from "../utils/UserValidationSchema.ts";
import { DeleteUserDialog } from "./DeleteUserDialog.tsx";
import { UserFormSelectRow } from "./UserFormSelectRow.tsx";
import { UserFormSubmit } from "./UserFormSubmit.tsx";
import { UserFormTextRow } from "./UserFormTextRow.tsx";

interface Props {
  user: User;
  closeForm: () => void;
  clients: PreparedClients;
}

const styles: StyleCreator<"container" | "row" | "label" | "undo"> = () => ({
  container: {
    height: "100%",
    display: "flex",
    flexDirection: "column",
    gap: "12px",
  },
  row: {
    display: "flex",
    alignItems: "center",
    gap: "12px",
  },
  label: {
    width: "100px",
  },
  undo: {
    minWidth: "40px",
    minHeight: "40px",
  },
});

function getObjectDiff(obj1, obj2) {
  return _.omitBy(obj1, (value, key) => _.isEqual(value, obj2[key]));
}

export const UserForm: FC<Props> = ({ user, closeForm, clients }) => {
  const c = useStyleCreator(styles);

  const [updateUser, { isLoading, isError, error }] = useUpdateUser();
  const [openDialog, setOpenDialog] = useState(false);

  const FormMethods = useForm({
    defaultValues: user,
    resolver: yupResolver(UserValidationSchema),
  });

  const UserName = useMemo(() => {
    return user.family_name && user.given_name
      ? `${user.given_name} ${user.family_name}`
      : `${user.username}`;
  }, [user]);

  const UserRole = useMemo(() => {
    switch (user.user_type) {
      default:
        return user.user_type;
    }
  }, [user.user_type]);

  const handleSubmit = useCallback(
    (data: User) => {
      const preparedUser = getObjectDiff(
        data,
        FormMethods.formState.defaultValues,
      );

      updateUser({
        user: preparedUser as User,
        user_id: data.user_id,
      })
        .unwrap()
        .then(() =>
          toast.success(`User ${UserName} updated`, {
            position: "bottom-right",
          }),
        );
    },
    [FormMethods.formState.defaultValues, UserName, updateUser],
  );

  useEffect(() => {
    FormMethods.reset(user, { keepDefaultValues: false, keepValues: false });
  }, [FormMethods, user]);

  return (
    <FormProvider {...FormMethods}>
      <Box
        component={"form"}
        onSubmit={FormMethods.handleSubmit(handleSubmit)}
        onReset={() => FormMethods.reset(user)}
        sx={c.container}
      >
        <FormValidator schema={UserValidationSchema} />
        <Box sx={c.row} style={{ marginBottom: "24px" }}>
          <Typography typography={"h4"}>
            {UserName} (<b>{UserRole}</b>)
          </Typography>
          <Button
            onClick={closeForm}
            sx={{ height: "40px", minWidth: "40px", marginLeft: "auto" }}
          >
            <Close />
          </Button>
        </Box>
        <UserFormTextRow name={"username"} label={"Username"} />
        <UserFormTextRow name={"given_name"} label={"First Name"} />
        <UserFormTextRow name={"family_name"} label={"Last Name"} />
        <UserFormTextRow name={"email"} label={"Email"} />
        <UserFormSelectRow
          name={"user_type"}
          label={"Role"}
          items={[
            { text: "User", value: "user" },
            { text: "Delegate", value: "delegate" },
            { text: "Manager", value: "manager" },
          ]}
        />
        <UserFormSelectRow
          name={"allowed_clients"}
          label={"Available clients"}
          items={clients}
          multiple
        />
        <UserFormSubmit
          isError={isError}
          isLoading={isLoading}
          error={error as ResponseError}
          openDeleteDialog={() => {
            setOpenDialog(true);
          }}
        />
        <DeleteUserDialog
          user={user}
          name={UserName}
          role={UserRole}
          handleClose={() => setOpenDialog(false)}
          open={openDialog}
        />
      </Box>
    </FormProvider>
  );
};
