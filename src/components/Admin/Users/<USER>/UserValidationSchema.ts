import * as yup from "yup";

export const BaseString = (label: string) =>
  yup
    .string()
    .label(label)
    .min(4, (params) => `${params.label} should be 4 characters minimum`)
    .max(40, (params) => `${params.label} should be 40 characters maximum`);

export const UserValidationSchema = yup
  .object()
  .shape({
    email: BaseString("Email")
      .matches(
        /^((?!\.)[\w-_.]*[^.])(@\w+)(\.\w+(\.\w+)?[^.\W])$/gim,
        "Value should be valid email format",
      )
      .matches(/^[^.].*[^.]$/, "The email couldn't start or finish with a dot")
      .matches(
        /^\S+\S+\S+$/,
        "The email shouldn't contain spaces into the string",
      )
      .matches(/^[a-zA-Z0-9@._-]+$/, "Email cannot contain special characters")
      .trim()
      .required("Email cannot be empty"),
    given_name: BaseString("First Name")
      .matches(
        /^[a-zA-Z-\s]+$/,
        (params) =>
          `${params.label} can't contain numbers or special characters except -`,
      )
      .trim()
      .required("Field is required"),
    family_name: BaseString("Last Name")
      .matches(
        /^[a-zA-Z-\s]+$/,
        (params) =>
          `${params.label} can't contain numbers or special characters except -`,
      )
      .trim()
      .required("Field is required"),
    username: BaseString("Username")
      .trim()
      .required("Username cannot be empty"),
    user_type: yup
      .string()
      .oneOf(
        ["user", "manager", "delegate"],
        "Must be one of the following roles: user, delegate, manager",
      )
      .required("User Role cannot be empty"),
    allowed_clients: yup
      .array()
      .min(1, "At least 1 client must be selected")
      .required("Allowed clients cannot be empty"),
  })
  .required();
