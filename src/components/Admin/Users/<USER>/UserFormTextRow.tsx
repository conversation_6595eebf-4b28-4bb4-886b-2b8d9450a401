import { Undo } from "@mui/icons-material";
import { Box, Button, TextField, Tooltip, Typography } from "@mui/material";
import { FC, useCallback } from "react";
import { Controller, useFormContext } from "react-hook-form";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../../hooks/useStyleCreator.ts";

interface Props {
  name: string;
  label: string;
}

const styles: StyleCreator<"container" | "label" | "undo"> = (theme) => ({
  container: {
    display: "flex",
    alignItems: "start",
    gap: "12px",
  },
  label: {
    minWidth: "100px",
    width: "fit-content",
  },
  undo: {
    minWidth: "40px",
    minHeight: "40px",
  },
});

export const UserFormTextRow: FC<Props> = ({ name, label }) => {
  const c = useStyleCreator(styles);
  const { resetField } = useFormContext();

  const handlerReset = useCallback(() => resetField(name), [name, resetField]);

  return (
    <Box sx={c.container}>
      <Typography typography={"h6"} sx={c.label}>
        {label}
      </Typography>
      <Controller
        name={name}
        render={({ field: { value, onChange, ref }, fieldState }) => {
          return (
            <Tooltip
              title={fieldState.error?.message ?? null}
              disableInteractive
            >
              <TextField
                value={value}
                onChange={onChange}
                ref={ref}
                size={"small"}
                fullWidth
                error={fieldState.invalid}
              />
            </Tooltip>
          );
        }}
      />
      <Button onClick={handlerReset} sx={c.undo}>
        <Undo />
      </Button>
    </Box>
  );
};
