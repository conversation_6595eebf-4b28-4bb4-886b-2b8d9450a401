import { FiberManualRecord } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import { FC } from "react";
import { CreateUsersErrorResponse } from "./CreateUsersErrorResponse.ts";

export const ErrorTooltip: FC<CreateUsersErrorResponse> = ({
  errors,
  message,
}) => {
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: "8px" }}>
      <Typography typography={"caption"} fontWeight={700} fontSize={14}>
        {message}
      </Typography>
      {errors.non_unique_emails.length > 0 && (
        <Box sx={{ display: "flex", flexDirection: "column" }}>
          <Typography typography={"caption"}>
            Users with next <b>emails</b> already exists
          </Typography>
          {errors.non_unique_emails.map(({ value }) => (
            <Box
              sx={{
                display: "flex",
                gap: "8px",
                alignItems: "center",
                marginLeft: "12px",
              }}
            >
              <FiberManualRecord sx={{ fontSize: "8px" }} />
              <Typography typography={"caption"}>{value}</Typography>
            </Box>
          ))}
        </Box>
      )}
      {errors.non_unique_usernames.length > 0 && (
        <Box>
          <Typography typography={"caption"}>
            Users with next <b>usernames</b> already exists
          </Typography>
          {errors.non_unique_usernames.map(({ value }) => (
            <Box
              sx={{
                display: "flex",
                gap: "8px",
                alignItems: "center",
                marginLeft: "12px",
              }}
            >
              <FiberManualRecord sx={{ fontSize: "8px" }} />
              <Typography typography={"caption"}>{value}</Typography>
            </Box>
          ))}
        </Box>
      )}
    </Box>
  );
};
