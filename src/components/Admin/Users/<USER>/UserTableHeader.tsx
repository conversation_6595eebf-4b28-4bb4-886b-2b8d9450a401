import {
  Box,
  Button,
  ButtonGroup,
  Checkbox,
  Divider,
  FormControl,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
} from "@mui/material";
import { FC, useMemo } from "react";
import { Controller } from "react-hook-form";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../../hooks/useStyleCreator.ts";
import { PreparedClients } from "../../../../store/queries/clients/types/Client.ts";

const styles: StyleCreator<"buttons" | "section" | "container"> = (theme) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: "12px",
  },
  buttons: {
    marginLeft: "auto",
    height: "40px",
  },
  section: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },
});

export const UserTableHeader: FC<{ clients: PreparedClients }> = ({
  clients,
}) => {
  const c = useStyleCreator(styles);

  const roles = useMemo(
    () => [
      { text: "User", value: "user" },
      { text: "Delegate", value: "delegate" },
      { text: "Manager", value: "manager" },
    ],
    [],
  );

  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
      <Box sx={c.container} style={{ width: "100%" }}>
        <Box sx={c.section}>
          <Controller
            name={"query"}
            render={({ field }) => (
              <TextField
                {...field}
                placeholder={"Search by Name, Email etc..."}
                size={"small"}
                fullWidth
              />
            )}
          />
          <Controller
            name={"status"}
            render={({ field: { value, ref, onChange } }) => {
              return (
                <ButtonGroup ref={ref} sx={c.buttons}>
                  <Button
                    onClick={() => onChange("active")}
                    variant={value === "active" ? "contained" : "outlined"}
                  >
                    Online
                  </Button>
                  <Button
                    onClick={() => onChange("inactive")}
                    variant={value === "inactive" ? "contained" : "outlined"}
                  >
                    Offline
                  </Button>
                  <Button
                    onClick={() => onChange(null)}
                    variant={value === null ? "contained" : "outlined"}
                  >
                    Any
                  </Button>
                </ButtonGroup>
              );
            }}
          />
        </Box>
        <Box sx={{ display: "flex", flexDirection: "column", gap: "12px" }}>
          <Box sx={c.section}>
            <Controller
              name={"clients"}
              render={({ field }) => (
                <FormControl fullWidth>
                  <InputLabel size={"small"}>Clients</InputLabel>
                  <Select
                    {...field}
                    labelId="demo-multiple-checkbox-label"
                    id="demo-multiple-checkbox"
                    input={<OutlinedInput size={"small"} label="Clients" />}
                    multiple
                    renderValue={(selected: string[]) =>
                      selected
                        .map(
                          (item) =>
                            clients.find(({ value }) => value === item).text,
                        )
                        .join(", ")
                    }
                  >
                    {clients.map(({ text, value: rowValue }) => {
                      return (
                        <MenuItem value={rowValue}>
                          <Checkbox
                            disableRipple
                            checked={field.value.includes(rowValue)}
                          />
                          {text}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              )}
            />
            <Controller
              name={"user_type"}
              render={({ field }) => (
                <FormControl fullWidth>
                  <InputLabel size={"small"}>Role</InputLabel>
                  <Select
                    {...field}
                    labelId="demo-multiple-checkbox-label"
                    id="demo-multiple-checkbox"
                    input={<OutlinedInput size={"small"} label="Role" />}
                    renderValue={(value) =>
                      roles.find((item) => value === item.value).text
                    }
                  >
                    {roles.map(({ text, value: rowValue }) => {
                      return (
                        <MenuItem value={rowValue}>
                          <Checkbox
                            disableRipple
                            checked={field.value === rowValue}
                          />
                          {text}
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              )}
            />
            <Divider flexItem orientation={"vertical"} variant={"middle"} />
            <Button sx={{ width: "150px" }} variant={"outlined"} type={"reset"}>
              Reset
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
