import { Box } from "@mui/material";
import {
  DataGrid,
  GridRowSelectionModel,
  GridSortModel,
} from "@mui/x-data-grid";
import debounce from "debounce";
import {
  Dispatch,
  FC,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { FormProvider, useForm } from "react-hook-form";

import {
  StyleCreator,
  useStyleCreator,
} from "../../../../hooks/useStyleCreator.ts";
import { PreparedClients } from "../../../../store/queries/clients/types/Client.ts";
import {
  User,
  UserSortFields,
} from "../../../../store/queries/users/types/User.ts";
import { useGetUsers } from "../../../../store/queries/users/users.query.ts";
import { columns } from "./UserColumns.tsx";
import { UserTableHeader } from "./UserTableHeader.tsx";

const styles: StyleCreator<"container" | "textfield" | "buttons"> = () => ({
  container: {
    height: "100%",
    display: "flex",
    flexDirection: "column",
    gap: "24px",
  },
  textfield: {
    height: "40px",
  },
  buttons: {
    marginLeft: "auto",
    height: "40px",
  },
});

interface Props {
  selectUser: Dispatch<SetStateAction<User>>;
  clients: PreparedClients;
}

export const UserTable: FC<Props> = ({ selectUser, clients }) => {
  const c = useStyleCreator(styles);

  const FormMethods = useForm({
    defaultValues: {
      user_type: [],
      status: null,
      clients: [],
      query: "",
    },
  });

  const status = FormMethods.watch("status");
  const user_type = FormMethods.watch("user_type");
  const allowed_clients = FormMethods.watch("clients");
  const query = FormMethods.watch("query");

  const [setIsListening, setSetIsListening] = useState(false);
  const [debouncedQuery, setDebouncedQuery] = useState("");
  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 10,
  });
  const [rowSelectionModel, setRowSelectionModel] =
    useState<GridRowSelectionModel>([]);

  const { data, isFetching, isError } = useGetUsers(
    {
      status,
      user_type,
      allowed_clients: allowed_clients,
      name_query: debouncedQuery,
      page: paginationModel.page,
      per_page: paginationModel.pageSize,
      sort_by: (sortModel[0]?.field as UserSortFields) ?? null,
      sort_order: sortModel[0]?.sort ?? null,
    },
    { refetchOnMountOrArgChange: true },
  );

  const users = useMemo(() => {
    if (isFetching || isError || !data) return [];
    return data.users.map((user) => ({
      ...user,
      allowed_clients: (user.allowed_clients || [])
        .map((client_id) => {
          const client = clients.find(({ id }) => id === client_id);
          return client ? client.text : `Unknown Client (${client_id})`;
        })
        .filter(Boolean), // Remove any falsy values
    }));
  }, [clients, data, isError, isFetching]);

  const handleSortModelChange = useCallback((model) => setSortModel(model), []);
  const handlePaginationModelChange = useCallback(
    (value) => setPaginationModel(value),
    [],
  );

  const setQuery = useCallback(
    debounce((query: string) => {
      setDebouncedQuery(query);
      setSetIsListening(false);
    }, 1500),
    [],
  );

  useEffect(() => {
    setSetIsListening(true);
    setQuery(query);
  }, [query, setQuery]);

  useEffect(
    () =>
      selectUser(
        users.find((user) => user.user_id === rowSelectionModel[0]) ?? null,
      ),
    [rowSelectionModel, selectUser, users],
  );

  return (
    <FormProvider {...FormMethods}>
      <Box
        component={"form"}
        onSubmit={FormMethods.handleSubmit(() => {})}
        onReset={() => FormMethods.reset()}
        sx={c.container}
      >
        <UserTableHeader clients={clients} />
        <DataGrid
          rows={users}
          columns={columns}
          rowSelectionModel={rowSelectionModel}
          onRowSelectionModelChange={(newRowSelectionModel) => {
            setRowSelectionModel((previousState) => {
              if (previousState.includes(newRowSelectionModel[0])) {
                return [];
              }

              return newRowSelectionModel;
            });
          }}
          sx={{
            "& .MuiDataGrid-row:hover": {
              boxShadow: 3,
              cursor: "pointer",
              backgroundColor: "#03595c",
            },
          }}
          getRowId={(row) => row.user_id}
          paginationModel={paginationModel}
          pageSizeOptions={[10, 15, 25]}
          onPaginationModelChange={handlePaginationModelChange}
          sortModel={sortModel}
          onSortModelChange={handleSortModelChange}
          paginationMode="server"
          rowCount={data?.total_count ?? 0}
          paginationMeta={{ hasNextPage: true }}
          disableColumnMenu
          disableColumnFilter
          loading={isFetching || setIsListening}
          // autoHeight
          rowHeight={60}
        />
      </Box>
    </FormProvider>
  );
};
