import { Delete } from "@mui/icons-material";
import { Box, Button, CircularProgress, Tooltip } from "@mui/material";
import { FC, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import { ResponseError } from "../../../../store/queries/users/types/User.ts";

export const UserFormSubmit: FC<{
  openDeleteDialog: () => void;
  isLoading: boolean;
  isError: boolean;
  error: ResponseError;
}> = ({ openDeleteDialog, isLoading, isError, error }) => {
  const {
    formState: { isValid },
  } = useFormContext();

  const errorMessage = useMemo(() => {
    if (!isError) return null;

    const typedError = error as ResponseError;
    if (typedError.data.detail instanceof Array) {
      return typedError.data.detail.map(({ msg }) => msg);
    }

    return typedError.data.detail;
  }, [error, isError]);

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        gap: "12px",
      }}
      style={{ marginTop: "auto" }}
    >
      <Button
        onClick={openDeleteDialog}
        sx={{ height: "40px", minWidth: "40px", marginLeft: "auto" }}
      >
        <Delete color={"error"} />
      </Button>
      <Button fullWidth variant={"outlined"} type={"reset"}>
        Cancel
      </Button>
      <Tooltip title={errorMessage}>
        <Button
          disabled={!isValid || isLoading}
          fullWidth
          variant={"contained"}
          type={"submit"}
          color={isError ? "error" : "primary"}
        >
          {!isLoading && "Update"}
          {isLoading && <CircularProgress size={24} />}
        </Button>
      </Tooltip>
    </Box>
  );
};
