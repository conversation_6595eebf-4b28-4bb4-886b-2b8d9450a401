import { Box, darken, Paper, useTheme } from "@mui/material";
import { useState } from "react";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../../hooks/useStyleCreator.ts";
import { useClients } from "../../../../store/queries/clients/clients.query.ts";
import { User } from "../../../../store/queries/users/types/User.ts";
import { UserForm } from "./UserForm.tsx";
import { UserTable } from "./UserTable.tsx";

const styles: StyleCreator<"container" | "section"> = (theme) => ({
  container: {
    width: "100%",
    display: "flex",
    height: "100%",
    maxHeight: "calc(85vh + 1px)",
    gap: " 12px",
    [theme.breakpoints.down(1440)]: {
      flexDirection: "column",
    },
  },
  section: {
    padding: "12px",
    background: darken("#607274", 0.07),
  },
});

export const UserList = () => {
  const theme = useTheme();
  const c = useStyleCreator(styles);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const { data: clients, isLoading, isError } = useClients();

  return (
    <Box sx={c.container}>
      <Paper
        sx={{
          ...c.section,
          [theme.breakpoints.up(1440)]: {
            width: selectedUser ? "calc(100% / 2 - 12px)" : "100%",
          },
          [theme.breakpoints.down(1440)]: {
            width: "100%",
          },
        }}
      >
        <UserTable
          clients={!isLoading && !isError ? clients : []}
          selectUser={setSelectedUser}
        />
      </Paper>
      {selectedUser && (
        <Paper
          sx={{
            ...c.section,
            [theme.breakpoints.up(1440)]: {
              width: selectedUser ? "calc(100% / 2 - 12px)" : "100%",
            },
            [theme.breakpoints.down(1440)]: {
              width: "100%",
            },
          }}
        >
          <UserForm
            clients={!isLoading ? clients : []}
            user={selectedUser}
            closeForm={() => {
              setSelectedUser(null);
            }}
          />
        </Paper>
      )}
    </Box>
  );
};
