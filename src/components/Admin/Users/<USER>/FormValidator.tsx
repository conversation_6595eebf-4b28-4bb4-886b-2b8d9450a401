import { FC, useEffect } from "react";
import { useFormContext, useWatch } from "react-hook-form";
import { ObjectSchema } from "yup";

interface Props {
  schema: ObjectSchema<any>;
}

export const FormValidator: FC<Props> = ({ schema }) => {
  const { setError, clearErrors, control } = useFormContext();
  const form = useWatch({ control });

  useEffect(() => {
    clearErrors();
    schema.validate(form, { abortEarly: false }).catch((errors) => {
      const processedErrors = errors.inner.reduce(
        (allErrors, currentError) => ({
          ...allErrors,
          [currentError.path]: {
            type: currentError.type ?? "validation",
            message: currentError.message,
          },
        }),
        {},
      );

      Object.entries(processedErrors).forEach(([key, value]) => {
        setError(key, value);
      });
    });
  }, [clearErrors, form, schema, setError]);

  return null;
};
