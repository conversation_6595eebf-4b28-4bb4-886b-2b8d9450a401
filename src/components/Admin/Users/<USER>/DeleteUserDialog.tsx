import {
  <PERSON>ton,
  CircularProgress,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Tooltip,
} from "@mui/material";
import { FC, useCallback, useState } from "react";
import { User } from "../../../../store/queries/users/types/User.ts";
import { useDeleteUser } from "../../../../store/queries/users/users.query.ts";

interface Props {
  open: boolean;
  handleClose: () => void;
  user: User;
  name: string;
  role: string;
}

export const DeleteUserDialog: FC<Props> = ({
  user,
  name,
  role,
  open,
  handleClose,
}) => {
  const [deleteUser] = useDeleteUser();
  const [isPending, setIsPending] = useState<boolean>(false);
  const [serverErrorResponse, setServerErrorResponse] = useState<string | null>(
    null,
  );

  const handleResetPassword = useCallback(() => {
    setIsPending(true);
    deleteUser({ user_id: user.user_id })
      .then(() => handleClose())
      .catch((e) =>
        setServerErrorResponse(
          e.response?.data?.detail ?? "Something went wrong",
        ),
      )
      .finally(() => setIsPending(false));
  }, [deleteUser, handleClose, user]);

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle>
        Confirm delete{" "}
        {user.family_name && user.given_name
          ? `${user.given_name} ${user.family_name}`
          : `${user.username}`}{" "}
        (<b>{role}</b>)
      </DialogTitle>
      <DialogContent>
        <DialogContentText>
          Are you sure you want to delete selected user?
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button
          sx={{ height: "38px" }}
          onClick={handleClose}
          variant={"outlined"}
        >
          No
        </Button>
        <Tooltip title={serverErrorResponse}>
          <Button
            sx={{ height: "38px" }}
            disabled={isPending}
            onClick={handleResetPassword}
            variant={"contained"}
            autoFocus
            color={"error"}
          >
            {isPending && <CircularProgress size={26} />}
            {!isPending && "Yes"}
          </Button>
        </Tooltip>
      </DialogActions>
    </Dialog>
  );
};
