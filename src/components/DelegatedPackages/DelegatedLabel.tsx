import { Typography } from "@mui/material";
import { useAppSelector } from "../../store/store.ts";

export const DelegatedLabel = () => {
  const { status } = useAppSelector((state) => state.data);

  if (status !== "delegated" && status !== "supervisor_review") return null;

  return (
    <Typography
      sx={{
        fontSize: "18px",
        fontWeight: "700",
        color: "red",
        marginLeft: "auto",
        marginRight: "auto",
      }}
    >
      DELEGATED
    </Typography>
  );
};
