import { Box, Button, useTheme } from "@mui/material";
import { FC, useCallback, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { testDelegateAccess } from "../../helpers/Roles.ts";
import { useAuthContext } from "../../hooks/useAuth.ts";
import { Content } from "../../store/queries/chunks/types/Delegates.type.ts";
import { useDelegatedListContext } from "./DelegatedContext.tsx";
import { DelegatedPackagesList } from "./DelegatedPackagesList.tsx";

interface Props {
  onSubmit: (uuid: string | null) => void;
  loading: boolean;
}

export const DelegatedPackages: FC<Props> = ({ onSubmit, loading }) => {
  const theme = useTheme();

  const { role } = useAuthContext();

  const [search, setSearch] = useSearchParams();

  const { handleOpen, handleClose, open, isDelegatedPackage } =
    useDelegatedListContext();

  const fetchDelegatedPackage = useCallback(
    (item: Content) => {
      onSubmit(item.chunk_id);
      handleClose();
    },
    [handleClose, onSubmit],
  );

  const fetchPackage = useCallback(() => {
    onSubmit(null);
    handleClose();
  }, [handleClose, onSubmit]);

  useEffect(() => {
    if (["delegated", "supervisor_review"].includes(search.get("from"))) {
      handleOpen();
    }

    search.delete("from");
    setSearch(search);
  }, [search, setSearch]);

  if (testDelegateAccess(role)) return null;

  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: "8px",
          [theme.breakpoints.down(1730)]: {
            flexDirection: "column",
          },
        }}
      >
        {isDelegatedPackage && (
          <Button
            variant="outlined"
            sx={{ height: "56px" }}
            fullWidth
            onClick={fetchPackage}
          >
            Back to non-delegated packets
          </Button>
        )}
      </Box>
      {open && (
        <DelegatedPackagesList
          open={open}
          handleClose={handleClose}
          onItemClick={fetchDelegatedPackage}
          loading={loading}
        />
      )}
    </Box>
  );
};
