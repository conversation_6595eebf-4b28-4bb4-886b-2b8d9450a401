import React, {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState,
} from "react";
import { useAppSelector } from "../../store/store";

interface DelegatedState {
  open: boolean;
  handleOpen: () => void;
  handleClose: () => void;
  isDelegatedPackage: boolean;
}

const DelegatedContext = createContext<DelegatedState | undefined>(undefined);

export const DelegatedProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [open, setOpen] = useState(false);
  const handleOpen = useCallback(() => setOpen(true), []);
  const handleClose = useCallback(() => setOpen(false), []);

  const { status } = useAppSelector((state) => state.data);

  const isDelegatedPackage = useMemo(
    () => ["delegated", "supervisor_review"].includes(status),
    [status],
  );

  return (
    <DelegatedContext.Provider
      value={{ open, handleOpen, handleClose, isDelegatedPackage }}
    >
      {children}
    </DelegatedContext.Provider>
  );
};

export const useDelegatedListContext = () => {
  const context = useContext(DelegatedContext);
  if (!context) {
    throw new Error(
      "useDelegatedListContext must be used within a DelegatedProvider",
    );
  }
  return context;
};
