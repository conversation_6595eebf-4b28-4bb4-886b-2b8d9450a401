import { ContentCopy } from "@mui/icons-material";
import { Box, IconButton, Tooltip, Typography } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import { format } from "date-fns";

export const columns: GridColDef[] = [
  {
    field: "chunk_id",
    headerName: "UUID",
    flex: 1,
    minWidth: 350,
    sortable: false,
    valueFormatter: (value) => value,
    renderCell: (params) => (
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          height: "100%",
          padding: "0 12px 0 0",
          justifyContent: "space-between",
        }}
      >
        <Typography variant="body2" style={{ marginRight: 8 }}>
          {params.value}
        </Typography>
        <Tooltip disableInteractive title="Copy UUID">
          <IconButton
            edge="end"
            onClick={(e) => {
              e.stopPropagation();
              navigator.clipboard.writeText(params.value);
            }}
          >
            <ContentCopy fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
    ),
  },
  { field: "filename", headerName: "Name", flex: 0.6 },
  { field: "delegated_by_username", headerName: "Delegated By", flex: 1 },
  { field: "delegated_by_user_id", headerName: "Delegate id", flex: 1 },
  {
    field: "delegated_time",
    headerName: "Delegation Time",
    flex: 1,
    valueFormatter: (value) => {
      if (value) return format(value, "dd LLLL yyyy 'at' HH:mm:ss");
      return null;
    },
  },
  { field: "name", headerName: "Client Name", flex: 1 },
];
