import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import { DataGrid, GridRowParams, GridSortModel } from "@mui/x-data-grid";
import { FC, useCallback, useState } from "react";

import { useDelegates } from "../../store/queries/chunks/chunks.query.ts";
import { Content } from "../../store/queries/chunks/types/Delegates.type.ts";
import { columns } from "./columns.tsx";

interface Props {
  open: boolean;
  handleClose: () => void;
  onItemClick: (item: Content) => void;
  loading: boolean;
}

export const DelegatedPackagesList: FC<Props> = ({
  open,
  handleClose,
  onItemClick,
  loading,
}) => {
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 10,
  });
  const [sortModel, setSortModel] = useState<GridSortModel>([]);

  const {
    data: { content, total_count } = {},
    isLoading,
    isFetching,
    isError,
  } = useDelegates(
    {
      page: paginationModel.page,
      per_page: paginationModel.pageSize,
      sort_by: sortModel[0]?.field ?? null,
      sort_order: sortModel[0]?.sort ?? null,
    },
    {
      pollingInterval: 120000,
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true,
    },
  );

  const handleRowClick = useCallback(
    (params: GridRowParams) => onItemClick(params.row),
    [onItemClick],
  );

  const handlePaginationModelChange = useCallback(
    (value) => setPaginationModel(value),
    [],
  );

  const handleSortModelChange = useCallback((model) => setSortModel(model), []);

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth="xl">
      <DialogTitle>Delegated Document Packages</DialogTitle>
      <DialogContent>
        <Box style={{ height: 600, width: "100%" }}>
          <DataGrid
            loading={isLoading || isFetching || isError || loading}
            rows={content ?? []}
            columns={columns}
            onRowClick={handleRowClick}
            sx={{
              "& .MuiDataGrid-row:hover": {
                boxShadow: 3,
                cursor: "pointer",
                backgroundColor: "#03595c",
              },
            }}
            getRowId={(row) => row.chunk_id}
            paginationModel={paginationModel}
            onPaginationModelChange={handlePaginationModelChange}
            sortModel={sortModel}
            onSortModelChange={handleSortModelChange}
            paginationMode="server"
            rowCount={total_count ?? 0}
            paginationMeta={{
              hasNextPage:
                content && content.length === paginationModel.pageSize,
            }}
            disableColumnMenu
            disableColumnFilter
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};
