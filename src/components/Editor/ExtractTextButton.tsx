import { CropFree } from "@mui/icons-material";
import { Button, CircularProgress } from "@mui/material";
import { FC, useCallback, useEffect } from "react";
import toast from "react-hot-toast";
import { useScreenshotContext } from "../../hooks/useScreenshotContext.ts";
import { StyleCreator, useStyleCreator } from "../../hooks/useStyleCreator.ts";
import {
  useExtractText,
  useExtractedText,
} from "../../store/queries/chunks/chunks.query.ts";
import { validateAndFormatString } from "./helpers.ts";

const styles: StyleCreator<"actionButton"> = () => ({
  actionButton: {
    height: "40px",
    width: "40px",
    minWidth: "40px",
    padding: "0",
  },
});

interface Props {
  nestedKey: string;
  onFieldChange: (key: string, value: string) => void;
}

export const ExtractTextButton: FC<Props> = ({ nestedKey, onFieldChange }) => {
  const c = useStyleCreator(styles);

  const { makeScreenshot, base64, dumpImage, rowToUpdate, isProcessing } =
    useScreenshotContext();
  const [extractText, { isLoading: isScreenshotSending }] = useExtractText();
  const [getExtractText, { isLoading: isTextExtracting }] = useExtractedText();

  const getText = useCallback(
    async (base64: string, packet_id: string) => {
      const { screenshot_id } = await extractText({
        base64,
        packet_id,
      }).unwrap();

      const ocrResponse = await getExtractText({ screenshot_id }).unwrap();

      return { ocrResponse };
    },
    [extractText, getExtractText],
  );

  const handleError = useCallback((error: any) => {
    let message = "Error occurred during text extraction";

    if (["TimeoutError", "AbortError"].includes(error?.name)) {
      message =
        "It appears that the content in the selected area wasn’t recognized. Please try again";
    } else if (error?.message) {
      message = error?.message;
    }

    toast.error(message, {
      position: "bottom-right",
      duration: 10_000,
      style: { minWidth: "400px", maxWidth: "400px" },
    });
  }, []);

  useEffect(() => {
    const packet_id = localStorage.getItem("activeFile");

    if (base64 && packet_id && rowToUpdate === nestedKey) {
      getText(base64, packet_id)
        .then(async ({ ocrResponse }) => {
          await validateAndFormatString(
            nestedKey,
            ocrResponse.text_array[0] ?? "",
          )
            .then((trimmedText: string) => {
              onFieldChange(nestedKey, trimmedText);
            })
            .catch(handleError);
        })
        .catch(handleError)
        .finally(dumpImage);
    }
  }, [base64, dumpImage, extractText, getText]);

  return (
    <Button
      variant={"contained"}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
        makeScreenshot(nestedKey);
      }}
      sx={c.actionButton}
      disabled={Boolean(base64) || isProcessing}
    >
      {(isScreenshotSending || isTextExtracting) &&
        rowToUpdate === nestedKey && (
          <CircularProgress size={20} sx={{ "& svg": { color: "black" } }} />
        )}
      {((!isScreenshotSending && !isTextExtracting) ||
        rowToUpdate !== nestedKey) && <CropFree sx={{ color: "black" }} />}
    </Button>
  );
};
