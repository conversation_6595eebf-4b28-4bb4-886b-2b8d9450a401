import { Box } from "@mui/material";
import React from "react";
import { applyMarkingType } from "./helpers";

const requiredColors = {
  req: "red",
  ava: "#733003",
};

const getColorByRequired = (key: string): string => {
  return requiredColors[applyMarkingType(key)];
};

const addMark = (key: string): string => {
  switch (applyMarkingType(key)) {
    case "ava":
      return "'";
    case "req":
      return "*";
    default:
      return "";
  }
};

const addTextDecoration = (key: string): string => {
  switch (applyMarkingType(key)) {
    case "ava":
      return "none";
    case "req":
      return "underline";
    default:
      return "none";
  }
};

interface DisplayNameComponentProps {
  displayKey: string;
  level: number;
  dataKey: string;
}

const DisplayNameComponent: React.FC<DisplayNameComponentProps> = ({
  displayKey,
  level,
  dataKey,
}) => {
  return (
    <Box
      sx={{
        minWidth: "300px",
        color: getColorByRequired(dataKey),
        height: "40px",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        textDecoration: addTextDecoration(dataKey),
      }}
    >
      {displayKey} {addMark(dataKey)}:
    </Box>
  );
};

export default DisplayNameComponent;
