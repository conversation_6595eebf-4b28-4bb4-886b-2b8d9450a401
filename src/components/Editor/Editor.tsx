import { But<PERSON>, Checkbox, Typography } from "@mui/material";
import React, { useCallback, useState } from "react";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import {
  autoPopulatedProps,
  emptyTreatment,
} from "../../helpers/documentTypes.ts";
import { deepCopy } from "../../helpers/helperFunctions.ts";
import useUpdateData from "../../hooks/useUpdateData.ts";
import { updateStoreMetadata } from "../../store/slices/metadata/metadata.slice.ts";
import { CommentSection } from "../Widgets/CommentSection.tsx";
import CancelComponent from "./CancelComponent.tsx";
import EntryViewer from "./EditorViewer.tsx";
import { checkIfNameField, moveUpFromExtracted } from "./helpers.ts";

interface JsonEditorProps {
  submitDoc: (data: any) => void;
  handleDelegate: any;
  setEditing: (state: boolean) => void;
  role: string;
}

const JsonEditor: React.FC<JsonEditorProps> = ({
  submitDoc,
  handleDelegate,
  setEditing,
  role,
}) => {
  const dispatch = useDispatch();

  const metadata = useSelector((state: any) => state.data.metadata);
  const slices = useSelector((state: any) => state.data.slices);
  const comment = useSelector((state: any) => state.data.comment);
  const status = useSelector((state: any) => state.data.status);

  const [editedJson, setEditedJson] = useState(metadata);
  const [checked, setChecked] = useState<boolean>(false);
  const [search, setSearch] = useSearchParams();

  const updateMetadata = useCallback(
    (metadata: any) => {
      console.log("updating metadata");
      if (metadata && metadata.length > 0)
        dispatch(updateStoreMetadata(metadata));
      else dispatch(updateStoreMetadata([]));
    },
    [editedJson],
  );

  useUpdateData({
    predictions: slices,
    predictions_human_time: new Date(),
    metadata: moveUpFromExtracted(editedJson),
    comment: comment,
  });

  const {
    setValue,
    handleSubmit,
    formState: { errors },
    trigger,
    control,
    clearErrors,
    reset,
  } = useForm({ defaultValues: editedJson });

  const claimant = {
    firstName: "",
    middleName: "",
    lastName: "",
  };

  const physician = {
    firstName: "",
    lastName: "",
  };

  const updatePhysicianName = (item: PacketMetadata) => {
    const { firstName, lastName } = physician;
    if (firstName && lastName) {
      item.metaData.physician.physicianName.value =
        `${firstName} ${lastName}`.trim();
    } else {
      item.metaData.physician.physicianName.value =
        `${firstName} ${lastName}`.trim();
    }
  };

  const updatePatientName = (item: {
    metaData: { patientName: { value: string } };
  }) => {
    const { firstName, middleName, lastName } = claimant;
    if (item?.metaData?.patientName) {
      if (firstName && lastName) {
        item.metaData.patientName.value = middleName
          ? `${firstName} ${middleName} ${lastName}`.trim()
          : `${firstName} ${lastName}`.trim();
      } else {
        item.metaData.patientName.value =
          `${firstName} ${middleName} ${lastName}`.trim();
      }
    }
  };

  const validateName = (name: string) => /^[-A-Za-z\s]*$/.test(name);

  const handleInputChange = useCallback(
    (path: string, value: string, doc_pages?: number[][]) => {
      if (
        autoPopulatedProps.some((element) =>
          path.toLowerCase().includes(element.toLowerCase()),
        )
      ) {
        setEditedJson((prev: any) => {
          const obj = JSON.parse(JSON.stringify(prev));
          const keys = path.split(".").slice(1);

          obj.forEach((item: any) => {
            let current = item;
            for (let i = 0; i < keys.length - 1; i++) {
              if (current[keys[i]] === undefined) {
                return;
              }
              current = current[keys[i]];
            }
            if (current[keys[keys.length - 1]] !== undefined) {
              if (checkIfNameField(path)) {
                current[keys[keys.length - 1]].value = current[
                  keys[keys.length - 1]
                ].value = value.replace(/\s/g, "");
              } else {
                current[keys[keys.length - 1]].value = value;
              }
            }
            if (path.toLowerCase().includes("patientname")) {
              item.metaData.patientName.value = value || "";
              const nameParts = value.trim().split(" ");

              if (nameParts.length < 3) {
                if (item?.metaData?.claimant) {
                  const [firstName, ...lastNameParts] = nameParts;
                  const lastName = lastNameParts.join(" ");

                  if (item.metaData.claimant.firstName !== undefined) {
                    claimant.firstName = firstName || "";
                    item.metaData.claimant.firstName.value = claimant.firstName;
                  }
                  if (item.metaData.claimant.lastName !== undefined) {
                    claimant.lastName = lastName || "";
                    item.metaData.claimant.lastName.value = claimant.lastName;
                  }
                  if (item.metaData.claimant.middleName !== undefined) {
                    claimant.middleName = "";
                    item.metaData.claimant.middleName.value =
                      claimant.middleName;
                  }
                }
              } else {
                if (item?.metaData?.claimant) {
                  item.metaData.patientName.value = value || "";

                  const [firstName, middleName, ...lastNameParts] = nameParts;
                  const lastName = lastNameParts.join(" ");

                  if (
                    item.metaData.claimant.firstName !== undefined &&
                    validateName(firstName)
                  ) {
                    claimant.firstName = firstName || "";
                    item.metaData.claimant.firstName.value = claimant.firstName;
                  }
                  if (
                    item.metaData.claimant.middleName !== undefined &&
                    validateName(middleName)
                  ) {
                    claimant.middleName = middleName || "";
                    item.metaData.claimant.middleName.value =
                      claimant.middleName;
                  }
                  if (
                    item.metaData.claimant.lastName !== undefined &&
                    validateName(lastName)
                  ) {
                    claimant.lastName = lastName || "";
                    item.metaData.claimant.lastName.value = claimant.lastName;
                  }
                }
              }
            }

            if (path.toLowerCase().includes("claimant.firstname")) {
              if (validateName(value)) {
                claimant.firstName = value || "";
                if (item?.metaData?.claimant?.firstName) {
                  item.metaData.claimant.firstName.value = value || "";
                  if (item?.metaData?.claimant?.lastName && claimant.lastName) {
                    item.metaData.claimant.lastName.value = claimant.lastName;
                  }
                  if (
                    item?.metaData?.claimant?.middleName &&
                    claimant.middleName
                  ) {
                    item.metaData.claimant.middleName.value =
                      claimant.middleName;
                  }
                }
                updatePatientName(item);
              }
            }

            if (path.toLowerCase().includes("claimant.middlename")) {
              if (validateName(value)) {
                claimant.middleName = value || "";
                if (item?.metaData?.claimant?.middleName) {
                  item.metaData.claimant.middleName.value = value || "";
                  if (item?.metaData?.claimant?.firstName) {
                    claimant.firstName =
                      item?.metaData?.claimant?.firstName?.value || "";
                  }
                  if (item?.metaData?.claimant?.lastName) {
                    claimant.lastName =
                      item?.metaData?.claimant?.lastName?.value || "";
                  }
                }
                updatePatientName(item);
              }
            }

            if (path.toLowerCase().includes("claimant.lastname")) {
              if (validateName(value)) {
                claimant.lastName = value || "";
                if (item?.metaData?.claimant?.lastName) {
                  item.metaData.claimant.lastName.value = value || "";
                  if (item?.metaData?.claimant?.firstName) {
                    claimant.firstName =
                      item?.metaData?.claimant?.firstName?.value || "";
                  }
                  if (item?.metaData?.claimant?.middleName) {
                    claimant.middleName =
                      item?.metaData?.claimant?.middleName?.value || "";
                  }
                }
                updatePatientName(item);
              }
            }
          });
          reset([...Object.values(obj)], {
            keepValues: true,
          });
          return obj;
        });
      } else {
        setEditedJson((prev: any) => {
          const obj = JSON.parse(JSON.stringify(prev));
          const keys = path.split(".");
          try {
            let current = obj;
            for (let i = 0; i < keys.length - 1; i++) {
              if (current[keys[i]] === undefined) {
                current[keys[i]] = {};
              }
              current = current[keys[i]];
            }
            current[keys[keys.length - 1]].value = value;

            obj.forEach((item: PacketMetadata) => {
              const pageRanges = item?.pagesRef;
              const documentType = item.docType;

              if (
                documentType.toLowerCase() === "fax" &&
                doc_pages.toString() === pageRanges.toString()
              ) {
                if (
                  path.toLowerCase().includes("docdate") &&
                  item?.metaData?.docReceivedDate
                ) {
                  item.metaData.docReceivedDate.value = value;
                } else if (
                  path.toLowerCase().includes("docreceiveddate") &&
                  item?.metaData?.docDate
                ) {
                  item.metaData.docDate.value = value;
                }
              }
              if (doc_pages.toString() === pageRanges.toString()) {
                if (
                  path.toLowerCase().includes("physician.firstname") &&
                  item?.metaData?.physician
                ) {
                  if (validateName(value)) {
                    physician.firstName = value || "";
                    if (item?.metaData?.physician?.physicianName) {
                      if (item?.metaData?.physician?.physicianName?.value) {
                        const physicianNameParts =
                          item.metaData.physician.physicianName.value.split(
                            /\s+/,
                          );
                        const lastName =
                          physicianNameParts?.[1] ||
                          item.metaData.physician.lastName?.value;
                        item.metaData.physician.physicianName.value =
                          `${physician.firstName} ${lastName}`.trim();
                      }
                      updatePhysicianName(item);
                    }
                  }
                }

                if (
                  path.toLowerCase().includes("physician.lastname") &&
                  item?.metaData?.physician
                ) {
                  if (validateName(value)) {
                    physician.lastName = value || "";
                    if (item?.metaData?.physician?.physicianName) {
                      if (item?.metaData?.physician?.physicianName?.value) {
                        const physicianNameParts =
                          item.metaData.physician.physicianName.value.split(
                            /\s+/,
                          );
                        const firstName =
                          physicianNameParts?.[0] ||
                          item.metaData.physician.firstName?.value;
                        item.metaData.physician.physicianName.value =
                          `${firstName} ${physician.lastName}`.trim();
                      }
                      updatePhysicianName(item);
                    }
                  }
                }
              }
            });
            return obj;
          } catch (error) {
            console.log("err", error);
          }
        });
      }
    },
    [],
  );

  const addItem = useCallback((e: any, path: string, item: any) => {
    e.preventDefault();
    setEditedJson((prev: any) => {
      const obj = JSON.parse(JSON.stringify(prev));
      const keys = path.split(".");
      let current = obj;
      for (let i = 0; i < keys.length - 1; i++) {
        if (current[keys[i]] === undefined) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }
      const arrayKey = keys[keys.length - 1];
      if (!current[arrayKey]) {
        current[arrayKey] = [];
      }
      current[arrayKey].push(emptyTreatment);

      return obj;
    });
  }, []);

  const deleteItem = (e: any, path: string) => {
    e.preventDefault();
    setEditedJson((prev: any) => {
      const obj = JSON.parse(JSON.stringify(prev));
      const keys = path.split(".");
      let current = obj;
      for (let i = 0; i < keys.length - 1; i++) {
        if (current[keys[i]] === undefined) {
          return prev;
        }
        current = current[keys[i]];
      }
      const keyToDelete = keys[keys.length - 1];
      current.splice(keyToDelete, 1);
      return obj;
    });
  };

  const handleRevert = useCallback(
    (nestedKey: string, all?: boolean) => {
      if (all) {
        setValue("", [...Object.values(metadata)]);
        setEditedJson(metadata);
        return;
      }
      setEditedJson((prev) => {
        const keys = nestedKey.split(".");
        const newState = deepCopy(prev);

        let current = newState;
        let currentOrig = metadata;

        for (let i = 0; i < keys.length - 1; i++) {
          current = current[keys[i]];
          currentOrig = currentOrig[keys[i]];
        }

        try {
          current[keys[keys.length - 1]] = currentOrig[keys[keys.length - 1]];
        } catch (error) {
          current[keys[keys.length - 1]] = {
            value: "",
            confidence: 1,
            required: true,
            valid: true,
          };
        }

        setValue("", [...Object.values(newState)], { shouldValidate: true });
        return [...Object.values(newState)];
      });
    },
    [metadata, setValue],
  );

  const getErrorMessage = useCallback(
    (name: string) => {
      const keys = name.split(".");

      const result = keys.reduce(
        (acc: { [x: string]: any }, key: string | number) => {
          return acc && acc[key] ? acc[key] : null;
        },
        errors,
      );

      return result && result.message ? result.message : null;
    },
    [errors],
  );

  const onFormSubmit = async () => {
    console.log("data to submit", editedJson);
    console.log(editedJson);

    search.set("from", status);
    setSearch(search);
    submitDoc(editedJson);
  };

  const onErrors = async () => {
    console.log("data to submit", editedJson);

    search.set("from", status);
    setSearch(search);
    submitDoc(editedJson);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLFormElement>): void => {
    if (e.key === "Enter") {
      e.preventDefault();
    }
  };

  const handleCheckEnter = (event) => {
    if (event.key === "Enter") {
      setChecked(!checked);
    }
  };

  const handleReturnToSlises = (): void => {
    setEditing(false);
    updateMetadata(editedJson);
  };

  return (
    <form
      onSubmit={handleSubmit(onFormSubmit, onErrors)}
      style={{
        width: "100%",
        display: "flex",
        height: "100%",
        flexDirection: "column",
        // minHeight: "calc(100% - 120px)",
      }}
      onKeyDown={(e) => handleKeyDown(e)}
    >
      <div
        style={{
          // minHeight: "calc(100% - 120px)",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          gap: "4px",
        }}
      >
        <EntryViewer
          metadata={editedJson}
          handleInputChange={handleInputChange}
          getErrorMessage={getErrorMessage}
          handleRevert={handleRevert}
          trigger={trigger}
          control={control}
          addItem={addItem}
          deleteItem={deleteItem}
          slices={slices}
        />
        <Button onClick={handleReturnToSlises}>Back to slicing</Button>
      </div>
      <div style={{ display: "flex", gap: 4 }}>
        <CommentSection loading={false} role={role} />
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 8,
            padding: 8,
            paddingBottom: 0,
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "8px",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Checkbox
              checked={checked}
              onChange={() => setChecked(!checked)}
              tabIndex={2}
              onKeyDown={handleCheckEnter}
            />
            <Typography onClick={() => setChecked(!checked)}>
              I approve that metadata has been reviewed and is correct
            </Typography>
          </div>
          <div
            style={{
              display: "flex",
              gap: "16px",
              justifyContent: "center",
              padding: 8,
            }}
          >
            <CancelComponent
              onButtonClick={(e) => {
                e.preventDefault();
                clearErrors();
                setEditedJson(metadata);
                handleRevert("", true);
              }}
              styles={{
                ...styles.saveButton,
                width: 80,
                backgroundColor: "gray",
              }}
            />
            {role && role === "user" && (
              <Button
                onClick={(e) => handleDelegate(e, editedJson)}
                variant="outlined"
              >
                Delegate
              </Button>
            )}
            <Button
              type="submit"
              disabled={!checked}
              style={
                !checked
                  ? { ...styles.saveButton, ...styles.disabled }
                  : styles.saveButton
              }
              tabIndex={2}
            >
              Save and Submit
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
};

const styles = {
  key: {
    marginRight: "10px",
    color: "#007bff",
    width: "300px",
  },
  nestedKey: {
    color: "#007bff",
    marginBottom: "5px",
    width: "300px",
  },
  saveButton: {
    backgroundColor: "#28a745",
    color: "white",
    border: "none",
    padding: "10px 20px",
    borderRadius: "4px",
    fontSize: "16px",
    cursor: "pointer",
    height: 40,
    alignSelf: "center",
    width: "200px",
  },
  disabled: {
    backgroundColor: "#ccc",
    color: "gray",
    cursor: "not-allowed",
  },
};

export default JsonEditor;
