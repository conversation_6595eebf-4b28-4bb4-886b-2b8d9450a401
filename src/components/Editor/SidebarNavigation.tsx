import { Box, Button } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { stringFields } from "../../helpers/documentTypes";

interface SidebarNavigationProps {
  topLevelKeys: string[];
  handleSidebarNavigation: (key: string) => void;
}

const SidebarNavigation: React.FC<SidebarNavigationProps> = ({
  topLevelKeys,
  handleSidebarNavigation,
}) => {
  const [open, setOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      containerRef.current &&
      !containerRef.current.contains(event.target as Node)
    ) {
      setOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      <Button
        onClick={() => setOpen(true)}
        sx={{
          backgroundColor: "gray",
          color: "black",
          padding: "10px",
          cursor: "pointer",
          zIndex: 0,
          height: 50,
          width: 50,
        }}
      >
        ☰
      </Button>
      {open && (
        <Box
          ref={containerRef}
          sx={{
            position: open ? "absolute" : "static",
            left: 0,
            top: 0,
            width: "200px",
            height: "80%",
            backgroundColor: "#fff",
            transform: open ? "translateX(0)" : "translateX(-100%)",
            transition: "transform 0.3s ease-in-out",
            boxShadow: open ? "2px 0 5px rgba(0, 0, 0, 0.3)" : "none",
            zIndex: 100,
            display: "flex",
            flexDirection: "column",
            gap: "8px",
            overflow: "auto",
            color: "black",
            borderRadius: 2,
          }}
        >
          <Box
            key={"Start"}
            sx={{
              padding: "8px",
              cursor: "pointer",
            }}
            onClick={() => {
              handleSidebarNavigation("start");
              setOpen(false);
            }}
          >
            Start
          </Box>
          {topLevelKeys.map((key) => {
            const isStringField = stringFields.some(
              (element) => element.toLowerCase() === key.toLowerCase(),
            );
            if (!isStringField) {
              return (
                <Box
                  key={key}
                  sx={{
                    padding: "8px",
                    cursor: "pointer",
                  }}
                  onClick={() => {
                    handleSidebarNavigation(key);
                    setOpen(false);
                  }}
                >
                  {key.charAt(0).toUpperCase() +
                    key.slice(1).split("_").join(" ")}
                </Box>
              );
            } else {
              return null;
            }
          })}
          {open && (
            <Button
              onClick={() => setOpen(false)}
              sx={{
                backgroundColor: "gray",
                color: "black",
                padding: "10px",
                cursor: "pointer",
                width: "100%",
                "&:hover": {
                  backgroundColor: "#f0f0f0",
                },
              }}
            >
              Close
            </Button>
          )}
        </Box>
      )}
    </>
  );
};

export default SidebarNavigation;
