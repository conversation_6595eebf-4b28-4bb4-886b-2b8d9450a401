import {
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import React, { InputHTMLAttributes, useEffect, useState } from "react";
import { processGender } from "./helpers.ts";

interface GenderSelectProps extends InputHTMLAttributes<HTMLInputElement> {
  value?: string;
  onChange?: (event: { target: { value: string } }) => void;
  style?: React.CSSProperties;
}

const GenderSelect: React.FC<GenderSelectProps> = ({
  value,
  onChange,
  style,
}) => {
  const [gender, setGender] = useState("unknown");
  const [open, setOpen] = useState(false);

  useEffect(() => {
    setGender(processGender(value));
  }, [value]);

  const handleChange = (event: SelectChangeEvent<string>) => {
    const newValue = event.target.value as string;
    setGender(newValue);
    if (onChange) {
      onChange({ target: { value: newValue } });
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (open && event.key === "Enter") {
      setOpen((prevOpen) => !prevOpen);
    }
    if (event.key === "Enter") {
      setOpen((prevOpen) => !prevOpen);
    } else if (event.key === "Escape") {
      event.preventDefault();
      setOpen(false);
    }
  };

  return (
    <FormControl style={{ ...style, padding: 0 }} onKeyDown={handleKeyDown}>
      <Select
        value={gender}
        onChange={handleChange}
        style={{ ...style, padding: 0, border: "none" }}
        open={open}
        onOpen={() => setOpen(true)}
        onClose={() => setOpen(false)}
        sx={{
          ".MuiSelect-icon": {
            color: "black",
          },
          color: "black",
        }}
        tabIndex={1}
      >
        <MenuItem value="M" tabIndex={1}>
          Male
        </MenuItem>
        <MenuItem value="F" tabIndex={1}>
          Female
        </MenuItem>
        <MenuItem value="unknown" tabIndex={1}>
          Unknown
        </MenuItem>
      </Select>
    </FormControl>
  );
};

export { GenderSelect };
