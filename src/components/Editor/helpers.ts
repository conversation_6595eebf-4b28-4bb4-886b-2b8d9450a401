import { isValid, parse } from "date-fns";
import {
  nonVisibleProps,
  requiredFieldsToMark,
  requiredIfAvailable,
} from "../../helpers/documentTypes";
import { deepCopy } from "../../helpers/helperFunctions";

const hasSpecialCharacters = /[^a-zA-Z0-9 \-,.&#@]/;

export const getValidations = (key: string) => {
  const displayKey = key.toLowerCase();

  if (displayKey.includes("claimnumber")) {
    return {
      validate: {
        claimNumber: (value: string) => {
          if (!value || value === "") return true;
          if (value.includes("-001") || value.includes("-1AC")) {
            return true;
          }
          if (hasSpecialCharacters.test(value)) {
            return "Claim Number cannot contain special characters.";
          }
          if (value.length < 6 || value.length > 20) {
            return "Claim Number is suspicious. It should be between 6 and 20 characters.";
          }
          return true;
        },
      },
    };
  } else if (displayKey.includes("date")) {
    return {
      validate: {
        date: (value: string) => {
          if (!value || value === "") return true;
          const year = parseInt(value.substring(0, 4), 10);
          const month = parseInt(value.substring(4, 6), 10) - 1;
          const day = parseInt(value.substring(6, 8), 10);
          const dateObj = new Date(year, month, day);
          const current_year = new Date().getUTCFullYear();

          const lastDayOfMonth = new Date(year, month + 1, 0).getDate();
          if (day < 1 || day > lastDayOfMonth) {
            return `Day must be between 01 and ${lastDayOfMonth}.`;
          }

          if (
            !isValid(parse(value, "yyyyMMdd", new Date())) ||
            value.length < 8
          ) {
            return "Enter date in correct format: YYYY-MM-DD";
          }

          if (
            dateObj.getDate() !== day ||
            dateObj.getMonth() !== month ||
            dateObj.getFullYear() !== year
          ) {
            return "Invalid day, month, or year.";
          }

          if (year < 1900 || year > current_year) {
            return `Year must be between 1900 and ${current_year}.`;
          }

          if (month < 0 || month > 11) {
            return "Month must be between 01 and 12.";
          }

          return true;
        },
      },
    };
  } else if (displayKey.includes("patientname")) {
    return {
      validate: {
        patientName: (value: string) => {
          if (!value || value === "") return true;
          if (value.length < 6 || value.length > 30)
            return "Patient name length must be between 6 and 30 characters.";
          else if (value.split(" ").length === 1)
            return "Patient name must include both name and surname.";
          else if (hasSpecialCharacters.test(value))
            return "Patient name cannot contain special characters.";
          else if (/\d/.test(value))
            return "Patient name cannot contain numbers.";
          return true;
        },
      },
    };
  } else if (displayKey.includes("number") || displayKey.includes("zip")) {
    return {
      validate: {
        phoneNumber: (value: string) => {
          if (!value || value === "") return true;
          if (!value.match(/\d+/g))
            return "Number field should contain numbers.";
          return true;
        },
      },
    };
  } else if (displayKey.includes("email")) {
    return {
      validate: {
        default: (value: string) => {
          if (!value || value === "") return true;
          if (!value.includes("@")) return "Email should contain '@'.";
          if (!value.includes(".")) return "Email should contain '.";
          if (!/^[a-zA-Z0-9.-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value))
            return "Invalid email format.";

          return true;
        },
      },
    };
  } else if (
    [
      "expeditedFlag",
      "rushFlag",
      "newSubmission",
      "resubmission",
      "writtenConfirmPriorOralRequest",
    ].some((item) => item.toLowerCase() === displayKey.toLowerCase())
  ) {
    return {
      validate: {
        boolean: (value: string) => {
          return true;
        },
      },
    };
  } else if (
    ["address1", "address2"].some((item) => displayKey.includes(item))
  ) {
    return {
      validate: {
        default: (value: string) => {
          if (!value || value === "") return true;
          if (/[^a-zA-Z0-9 \-,.&#@/]/.test(value))
            return "Field shouldn't contain special characters.";
          return true;
        },
      },
    };
  } else if (["body", "summary"].some((item) => displayKey.includes(item))) {
    return {
      validate: {
        default: (value: string) => {
          if (!value || value === "") return true;
          // eslint-disable-next-line no-control-regex
          if (/[^\x00-\x7F]/.test(value))
            return "Field shouldn't contain special characters.";
          return true;
        },
      },
    };
  } else if (displayKey === "quantity") {
    return {
      validate: {
        default: (value: string) => {
          if (!value || value === "") return true;
          if (!/^\d+$/.test(value)) return "Field should contain only numbers";
          return true;
        },
      },
    };
  } else {
    return {
      validate: {
        default: (value: string) => {
          if (!value || value === "") return true;
          if (hasSpecialCharacters.test(value))
            return "Field shouldn't contain special characters.";
          return true;
        },
      },
    };
  }
};

export async function validateAndFormatString(
  key: string,
  value: string,
): Promise<string | Error> {
  const trimmedValue = value.trim();
  const badDataError = new Error("Bad data selected");

  const displayKey = key.toLowerCase();

  if (displayKey.includes("claimnumber")) {
    if (trimmedValue.includes("-001") || trimmedValue.includes("-1AC"))
      return trimmedValue;
    if (hasSpecialCharacters.test(trimmedValue))
      throw new Error("Claim Number cannot contain special characters.");
    if (trimmedValue.length < 6 || trimmedValue.length > 20)
      throw new Error("Claim Number must be between 6 and 20 characters.");
    return trimmedValue;
  }

  if (displayKey.includes("date")) {
    if (trimmedValue === "") throw badDataError;

    const year = parseInt(trimmedValue.substring(0, 4), 10);
    const month = parseInt(trimmedValue.substring(4, 6), 10) - 1;
    const day = parseInt(trimmedValue.substring(6, 8), 10);
    const currentYear = new Date().getFullYear();

    if (isNaN(year) || year < 1900 || year > currentYear) throw badDataError;
    if (isNaN(month) || month < 0 || month > 11) throw badDataError;
    if (isNaN(day) || day < 1 || day > new Date(year, month + 1, 0).getDate())
      throw badDataError;

    return `${year}-${String(month + 1).padStart(2, "0")}-${String(day).padStart(2, "0")}`;
  }

  if (displayKey.includes("patientname")) {
    if (trimmedValue.length < 6 || trimmedValue.length > 30)
      throw new Error("Patient name must be between 6 and 30 characters.");
    if (trimmedValue.split(" ").length < 2)
      throw new Error("Patient name must include both first and last names.");
    if (hasSpecialCharacters.test(trimmedValue))
      throw new Error("Patient name cannot contain special characters.");
    if (/\d/.test(trimmedValue))
      throw new Error("Patient name cannot contain numbers.");
    return trimmedValue;
  }

  if (["faxnumber", "phonenumber"].some((item) => displayKey.includes(item))) {
    const digitsOnly = trimmedValue.replace(/\D/g, "");
    return digitsOnly;
  }

  if (displayKey.includes("zip")) {
    const digitsOnly = trimmedValue.replace(/\D/g, "");
    if (digitsOnly.length < 5)
      throw new Error("ZIP code must have at least 5 digits.");
    if (digitsOnly.length > 9) return digitsOnly.slice(0, 9);
    return digitsOnly;
  }

  if (displayKey.includes("ssn")) {
    const digitsOnly = trimmedValue.replace(/\D/g, "");
    if (digitsOnly.length > 9) return digitsOnly.slice(0, 9);
    return digitsOnly;
  }

  if (displayKey.includes("email")) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(trimmedValue))
      throw new Error("Invalid email format.");
    return trimmedValue;
  }

  if (["address1", "address2"].some((item) => displayKey.includes(item))) {
    if (/[^a-zA-Z0-9 \-,.&#@/]/.test(trimmedValue))
      throw new Error("Address contains invalid special characters.");
    return trimmedValue;
  }

  if (["body", "summary"].some((item) => displayKey.includes(item))) {
    if (/[^\p{ASCII}]/u.test(trimmedValue))
      throw new Error("Field contains unsupported characters.");
    return trimmedValue;
  }

  if (displayKey === "quantity") {
    if (!/^\d+$/.test(trimmedValue))
      throw new Error("Quantity must be a numeric value.");
    return trimmedValue;
  }

  if (displayKey.includes("state")) {
    let state = trimmedValue.toUpperCase();
    state = state.slice(0, 2);
    const stateRegex = /^[A-Za-z]{2}$/;
    if (!stateRegex.test(state))
      throw new Error("State code must be exactly two letters (e.g., 'CA').");
    return state;
  }

  return trimmedValue;
}

export const checkIfEligibleForEditing = (
  displayKey: string,
  level: number,
) => {
  return (
    level !== 0 ||
    ["claimnumber", "patientname"].includes(displayKey.toLowerCase())
  );
};

export const checkIfEligibleForViewing = (
  displayKey: string,
  level: number,
) => {
  return nonVisibleProps.includes(displayKey) && level === 0;
};

export const formatPageRanges = (arr: number[][]): string => {
  if (!arr || arr.length < 1) {
    return null;
  }

  const sortedArr = [...arr].sort((a, b) => a[0] - b[0] || a[1] - b[1]);

  const formattedPairs = sortedArr.map((pair) => `${pair[0]}-${pair[1]}`);
  return formattedPairs.join(", ");
};

export const sortPages = (arr: EntryListItem[]) => {
  return [...arr].sort((a, b) => a.page_ranges[0][0] - b.page_ranges[0][0]);
};

export const moveUpFromExtracted = (documents: PacketMetadata[]): any[] => {
  const processedBooleanDocs = updateBooleanKeys(deepCopy(documents));
  return processedBooleanDocs.map(({ metaData, namingData, ...rest }) => {
    return { ...rest, metaData, namingData };
  });
};

export const processGender = (value: string) => {
  if (value?.toLowerCase() === "m" || value?.toLowerCase() === "male") {
    return "M";
  } else if (
    value?.toLowerCase() === "f" ||
    value?.toLowerCase() === "female"
  ) {
    return "F";
  } else {
    return "unknown";
  }
};

export function processFaxAndPhone(value: string): string {
  let cleanedValue = value ? value?.replace(/\D/g, "") : "";
  if (cleanedValue?.length > 11) {
    cleanedValue = cleanedValue.substring(0, 11);
  }

  return cleanedValue;
}

export const checkIfBooleanField = (key: string) => {
  return [
    "expeditedFlag",
    "rushFlag",
    "newSubmission",
    "resubmission",
    "writtenConfirmPriorOralRequest",
    "medicationFlag",
    "physicalTherapyFlag",
  ].some((item) => item.toLowerCase() === key.toLowerCase());
};

export const checkIfRequiredToMark = (key: string) => {
  return requiredFieldsToMark.some((item) => {
    const [key_base, key_field] = key.toLowerCase().split(".").slice(2, 4);
    const [item_base, item_field] = item.toLowerCase().split(".");

    return key_base === item_base && key_field === item_field;
  });
};

export const checkIfRequiredIfAvailableToMark = (key: string) => {
  return requiredIfAvailable.some((item) =>
    key.toLowerCase().includes(item.toLowerCase()),
  );
};

export const checkIfNameField = (key: string): boolean => {
  const nameFields = ["firstname", "lastname", "middlename"];
  return nameFields.some((field) => key.toLowerCase().includes(field));
};

export const applyMarkingType = (key: string): string | null => {
  if (checkIfRequiredToMark(key)) {
    return "req";
  }
  return checkIfRequiredIfAvailableToMark(key) ? "ava" : null;
};

export const isPageRangeComplete = (
  pageRanges: number[][],
  numPages: number,
): boolean => {
  const pageAvailability = Array(numPages + 1).fill(false);
  for (const [start, end] of pageRanges) {
    for (let i = start; i <= end; i++) {
      pageAvailability[i] = true;
    }
  }
  for (let i = 1; i <= numPages; i++) {
    if (!pageAvailability[i]) {
      return false;
    }
  }

  return true;
};

export const preprocessSlices = (objects: EntryListItem[]): EntryListItem[] => {
  return objects.map((obj) => {
    if (!obj.page_ranges) {
      throw new Error(
        "Each object must have at least one of 'pagesRef' or 'document_pages' properties.",
      );
    }

    const newObj: EntryListItem = { ...obj };

    if (obj.page_ranges) {
      newObj.page_ranges = obj.page_ranges.map((innerArray) =>
        innerArray.map((value) => {
          return typeof value === "string" ? parseInt(value, 10) : value;
        }),
      );
    }

    if (obj.page_ranges) {
      newObj.page_ranges = obj.page_ranges.map((innerArray) =>
        innerArray.map((value) => {
          return typeof value === "string" ? parseInt(value, 10) : value;
        }),
      );
    }

    return newObj;
  });
};

export function updateBooleanKeys(obj: any): PacketMetadata[] {
  const booleanMapping = {
    true: "Y",
    false: "N",
    "": null,
    Y: "Y",
    N: "N",
    y: "Y",
    n: "N",
    undefined: null,
    null: null,
    "n/a": null,
  };
  const getBooleanValue = (value: any): string => {
    const v = `${value}`.toLowerCase();
    return booleanMapping[v];
  };
  const genderMapping = { M: "M", F: "F", unknown: null };

  function recursiveUpdate(o: any): any {
    if (Array.isArray(o)) {
      for (let i = 0; i < o.length; i++) {
        if (typeof o[i] === "object" && o[i] !== null) {
          recursiveUpdate(o[i]);
        }
      }
    } else if (typeof o === "object" && o !== null) {
      for (const key in o) {
        if (Object.prototype.hasOwnProperty.call(o, key)) {
          if (typeof o[key] === "object" && o[key] !== null) {
            recursiveUpdate(o[key]);
          }
          if (checkIfBooleanField(key)) {
            o[key].value = getBooleanValue(o[key].value);
          }
          if (key.toLowerCase().includes("gender")) {
            o[key].value = genderMapping[o[key].value];
          }
        }
      }
    }
    return o;
  }
  return recursiveUpdate(obj);
}
