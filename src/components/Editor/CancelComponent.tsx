import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from "@mui/material";
import React, { useState } from "react";

interface CancelComponentProps {
  onButtonClick: (e: any) => void;
  styles: any;
}

const CancelComponent: React.FC<CancelComponentProps> = ({
  onButtonClick,
  styles,
}) => {
  const [open, setOpen] = useState(false);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  const handleYes = (e: any) => {
    handleClose();
    onButtonClick(e);
  };

  return (
    <>
      <Button
        onClick={(e) => {
          e.preventDefault();
          handleOpen();
        }}
        style={{
          ...styles.saveButton,
          width: 80,
          backgroundColor: "gray",
        }}
      >
        Cancel
      </Button>
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle>Revert Changes</DialogTitle>
        <DialogContent>Do you want to revert all the changes?</DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary">
            No
          </Button>
          <Button onClick={handleYes} color="primary">
            Yes
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default CancelComponent;
