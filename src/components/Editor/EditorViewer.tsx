import UndoIcon from "@mui/icons-material/Undo";
import { Box, Button, Typography } from "@mui/material";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Controller, useWatch } from "react-hook-form";
import { treatmentsFields } from "../../helpers/documentTypes";
import {
  hasRequiredFields,
  isMetadataEntry,
} from "../../helpers/helperFunctions";
import BooleanSelector from "../Inputs/BooleanSelector";
import DateInput from "../Inputs/DateInput";
import PhoneFaxInput from "../Inputs/PhoneMaskInput";
import SsnInput from "../Inputs/SsnMaskedInput";
import StateInput from "../Inputs/StateInput";
import ZipCodeInput from "../Inputs/ZipCodeMaskedInput";
import DisplayNameComponent from "./DisplayNameComponent";
import { ExtractTextButton } from "./ExtractTextButton.tsx";
import { GenderSelect } from "./GenderSelect.tsx";
import { QuantityInput } from "./QuantityInput.tsx";
import SidebarNavigation from "./SidebarNavigation";
import {
  checkIfBooleanField,
  formatPageRanges,
  getValidations,
} from "./helpers";

interface EntryViewerProps {
  metadata: PacketMetadata[];
  handleInputChange: (
    path: string,
    value: string,
    doc_pages?: number[][],
  ) => void;
  getErrorMessage: (name: string) => any;
  handleRevert: (nestedKey: string) => void;
  trigger: any;
  control: any;
  addItem: any;
  slices: EntryListItem[];
  deleteItem: any;
}

const EntryViewer: React.FC<EntryViewerProps> = React.memo(
  ({
    metadata,
    handleInputChange,
    getErrorMessage,
    handleRevert,
    trigger,
    control,
    addItem,
    deleteItem,
    slices,
  }) => {
    const [pageIndex, setPageIndex] = useState(0);

    const containerRef = useRef<HTMLDivElement>(null);
    const watchedFields = useWatch({ control, defaultValue: metadata });

    const handleNavigation = (e: any, direction: string) => {
      e.preventDefault();
      e.stopPropagation();
      setPageIndex((prev) => {
        if (direction === "next" && prev < totalPages - 1) return prev + 1;
        if (direction === "prev" && prev > 0) return prev - 1;
        return prev;
      });
    };

    const onChangeHandler = (nestedKey: string, value: string) => {
      const { pagesRef } = metadata[pageIndex];
      handleInputChange(nestedKey, value, pagesRef);
      trigger(nestedKey);
    };

    const handleSidebarNavigation = (key: string) => {
      if (key === "start") {
        containerRef.current.scrollTo({ top: 0, left: 0, behavior: "smooth" });
        return;
      }
      const element = Array.from(document.querySelectorAll("[id]")).find((el) =>
        el.id.toLowerCase().includes(key.toLowerCase()),
      );
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    };

    const renderInput = useCallback(
      (obj: PacketMetadata | AnyRecord, prefix: string, level: number) => {
        return Object.entries(obj).map(([key, dataVal], index) => {
          const nestedKey1 = prefix ? `${prefix}.${key}` : `${key}`;
          const nestedKey =
            level === 0 ? `${pageIndex}.${nestedKey1}` : `${nestedKey1}`;
          const displayKey =
            key.charAt(0).toUpperCase() + key.slice(1).split("_").join(" ");

          if (dataVal && Array.isArray(dataVal)) {
            return (
              <Box
                key={nestedKey1}
                sx={{
                  marginLeft: `${16 * level}px`,
                  color: "black",
                }}
              >
                <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                  <Box>{`${displayKey}:`}</Box>
                </Box>
                {dataVal?.map((item, idx) => {
                  return (
                    <Box key={item?.id || `${nestedKey}[${idx}]`}>
                      <>
                        {renderInput(
                          { [`${idx}`]: item },
                          `${nestedKey}`,
                          level + 1,
                        )}
                        <Box
                          sx={{
                            display: "flex",
                            gap: "16px",
                          }}
                        >
                          {idx > 0 && (
                            <button
                              style={{
                                ...styles.button,
                                backgroundColor: "#ff776e",
                              }}
                              onClick={(e) =>
                                deleteItem(e, `${nestedKey}.${idx}`)
                              }
                            >
                              Delete{" "}
                              {displayKey.slice(0, displayKey.length - 1)}
                            </button>
                          )}
                          {idx === dataVal.length - 1 && (
                            <button
                              style={{
                                ...styles.button,
                                backgroundColor: "#9dfaad",
                              }}
                              onClick={(e) => addItem(e, nestedKey, item)}
                            >
                              Add {displayKey.slice(0, displayKey.length - 1)}
                            </button>
                          )}
                        </Box>
                      </>
                    </Box>
                  );
                })}
              </Box>
            );
          }

          if (dataVal && isMetadataEntry(dataVal)) {
            if (dataVal.required) {
              const watchedValue = watchedFields[nestedKey] || dataVal.value;
              return (
                <Box
                  key={nestedKey}
                  id={nestedKey}
                  sx={{
                    marginLeft: `${16 * level}px`,
                    color: "black",
                    display: "flex",
                    alignItems: "center",
                    ...styles.inputContainer,
                    // height: 80,
                    justifyContent: "space-between",
                    fontWeight:
                      !checkIfBooleanField(displayKey) && watchedValue !== ""
                        ? 300
                        : 900,
                    marginBottom: "16px",
                  }}
                >
                  <DisplayNameComponent
                    displayKey={displayKey}
                    level={level}
                    dataKey={nestedKey1}
                  />
                  <Box style={{ display: "flex", gap: "8px", width: "100%" }}>
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "start",
                        alignItems: "start",
                        width: "100%",
                        // height: "52px",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          gap: "16px",
                          width: "100%",
                        }}
                      >
                        <Controller
                          name={nestedKey}
                          control={control}
                          rules={getValidations(displayKey)}
                          defaultValue={dataVal.value}
                          render={({ field: { onChange, onBlur, ref } }) => {
                            if (displayKey.toLowerCase().includes("date")) {
                              return (
                                <DateInput
                                  key={nestedKey}
                                  id={nestedKey + "_field"}
                                  value={watchedValue}
                                  onChange={(e) => {
                                    onChange(e.target.value);
                                    onChangeHandler(nestedKey, e.target.value);
                                  }}
                                  onBlur={() => {
                                    onBlur();
                                    trigger(nestedKey);
                                  }}
                                  style={{
                                    ...styles.input,
                                    border: getErrorMessage(nestedKey)
                                      ? "1px solid red"
                                      : "1px solid #607274",
                                  }}
                                  autoFocus={index === 0 && level === 0}
                                  title={`Confidence: ${
                                    dataVal.confidence
                                      ? Math.round(dataVal.confidence * 100) /
                                        100
                                      : "Not evaluated"
                                  }`}
                                />
                              );
                            } else if (
                              displayKey.toLowerCase().includes("state") ||
                              displayKey.toLowerCase() === "jurisdiction"
                            ) {
                              return (
                                <StateInput
                                  key={nestedKey}
                                  id={nestedKey + "_field"}
                                  value={watchedValue}
                                  onChange={(e) => {
                                    onChange(e.target.value);
                                    onChangeHandler(nestedKey, e.target.value);
                                  }}
                                  onBlur={() => {
                                    onBlur();
                                    trigger(nestedKey);
                                  }}
                                  style={{
                                    ...styles.input,
                                    border: getErrorMessage(nestedKey)
                                      ? "1px solid red"
                                      : "1px solid #607274",
                                  }}
                                  tabIndex={1}
                                  autoFocus={index === 0 && level === 0}
                                  title={`Confidence: ${
                                    dataVal.confidence
                                      ? Math.round(dataVal.confidence * 100) /
                                        100
                                      : "Not evaluated"
                                  }`}
                                />
                              );
                            } else if (
                              displayKey
                                .toLowerCase()
                                .includes("phonenumber") ||
                              displayKey.toLowerCase().includes("faxnumber")
                            ) {
                              return (
                                <PhoneFaxInput
                                  key={nestedKey}
                                  id={nestedKey + "_field"}
                                  value={watchedValue}
                                  onChange={(e) => {
                                    onChange(e.target.value);
                                    onChangeHandler(nestedKey, e.target.value);
                                  }}
                                  onBlur={() => {
                                    onBlur();
                                    trigger(nestedKey);
                                  }}
                                  style={{
                                    ...styles.input,
                                    border: getErrorMessage(nestedKey)
                                      ? "1px solid red"
                                      : "1px solid #607274",
                                  }}
                                  tabIndex={1}
                                  autoFocus={index === 0 && level === 0}
                                  title={`Confidence: ${
                                    dataVal.confidence
                                      ? Math.round(dataVal.confidence * 100) /
                                        100
                                      : "Not evaluated"
                                  }`}
                                />
                              );
                            } else if (
                              displayKey.toLowerCase().includes("zip")
                            ) {
                              return (
                                <ZipCodeInput
                                  key={nestedKey}
                                  id={nestedKey + "_field"}
                                  value={watchedValue}
                                  onChange={(e) => {
                                    onChange(e.target.value);
                                    onChangeHandler(nestedKey, e.target.value);
                                  }}
                                  onBlur={() => {
                                    onBlur();
                                    trigger(nestedKey);
                                  }}
                                  style={{
                                    ...styles.input,
                                    border: getErrorMessage(nestedKey)
                                      ? "1px solid red"
                                      : "1px solid #607274",
                                  }}
                                  tabIndex={1}
                                  autoFocus={index === 0 && level === 0}
                                  title={`Confidence: ${
                                    dataVal.confidence
                                      ? Math.round(dataVal.confidence * 100) /
                                        100
                                      : "Not evaluated"
                                  }`}
                                />
                              );
                            } else if (checkIfBooleanField(displayKey)) {
                              return (
                                <BooleanSelector
                                  key={nestedKey}
                                  id={nestedKey + "_field"}
                                  isTreatmentField={nestedKey.includes(
                                    "treatments",
                                  )}
                                  value={watchedValue}
                                  onChange={(e) => {
                                    onChange(e.target.value);
                                    onChangeHandler(nestedKey, e.target.value);
                                  }}
                                  onBlur={() => {
                                    onBlur();
                                    trigger(nestedKey);
                                  }}
                                  style={{
                                    ...styles.input,
                                    border: getErrorMessage(nestedKey)
                                      ? "1px solid red"
                                      : "1px solid #607274",
                                  }}
                                  tabIndex={1}
                                  autoFocus={index === 0 && level === 0}
                                  title={`Confidence: ${
                                    dataVal.confidence
                                      ? Math.round(dataVal.confidence * 100) /
                                        100
                                      : "Not evaluated"
                                  }`}
                                />
                              );
                            } else if (
                              displayKey.toLowerCase().includes("gender")
                            ) {
                              return (
                                <GenderSelect
                                  key={nestedKey}
                                  id={nestedKey + "_field"}
                                  value={watchedValue}
                                  onChange={(e) => {
                                    onChange(e.target.value);
                                    onChangeHandler(nestedKey, e.target.value);
                                  }}
                                  onBlur={() => {
                                    onBlur();
                                    trigger(nestedKey);
                                  }}
                                  style={{
                                    ...styles.input,
                                    border: getErrorMessage(nestedKey)
                                      ? "1px solid red"
                                      : "1px solid #607274",
                                  }}
                                  tabIndex={1}
                                  autoFocus={index === 0 && level === 0}
                                  title={`Confidence: ${
                                    dataVal.confidence
                                      ? Math.round(dataVal.confidence * 100) /
                                        100
                                      : "Not evaluated"
                                  }`}
                                />
                              );
                            } else if (
                              displayKey.toLowerCase().includes("ssn")
                            ) {
                              return (
                                <SsnInput
                                  key={nestedKey}
                                  id={nestedKey + "_field"}
                                  value={watchedValue}
                                  onChange={(e) => {
                                    onChange(e.target.value);
                                    onChangeHandler(nestedKey, e.target.value);
                                  }}
                                  onBlur={() => {
                                    onBlur();
                                    trigger(nestedKey);
                                  }}
                                  style={{
                                    ...styles.input,
                                    border: getErrorMessage(nestedKey)
                                      ? "1px solid red"
                                      : "1px solid #607274",
                                  }}
                                  tabIndex={1}
                                  autoFocus={index === 0 && level === 0}
                                  title={`Confidence: ${
                                    dataVal.confidence
                                      ? Math.round(dataVal.confidence * 100) /
                                        100
                                      : "Not evaluated"
                                  }`}
                                />
                              );
                            } else if (key.toLowerCase() === "quantity") {
                              return (
                                <QuantityInput
                                  key={nestedKey}
                                  id={nestedKey + "_field"}
                                  value={watchedValue}
                                  onChange={(e) => {
                                    onChange(e.target.value);
                                    onChangeHandler(nestedKey, e.target.value);
                                  }}
                                  style={{
                                    ...styles.input,
                                    border: getErrorMessage(nestedKey)
                                      ? "1px solid red"
                                      : "1px solid #607274",
                                  }}
                                />
                              );
                            } else if (
                              treatmentsFields.some(
                                (item) =>
                                  item.toLowerCase() === key.toLowerCase(),
                              ) &&
                              key !== "quantity"
                            ) {
                              return (
                                <textarea
                                  autoComplete={"off"}
                                  key={nestedKey}
                                  id={nestedKey + "_field"}
                                  value={watchedValue}
                                  onChange={(e) => {
                                    onChange(e.target.value);
                                    onChangeHandler(nestedKey, e.target.value);
                                    // e.target.style.height = "auto";
                                    // e.target.style.height = `${e.target.scrollHeight}px`;
                                  }}
                                  onBlur={() => {
                                    onBlur();
                                    trigger(nestedKey);
                                  }}
                                  ref={ref}
                                  style={{
                                    ...styles.input,
                                    border: getErrorMessage(nestedKey)
                                      ? "1px solid red"
                                      : "1px solid #607274",
                                    overflowY: "auto",
                                    resize: "none",
                                    minHeight: "75px",
                                  }}
                                  tabIndex={1}
                                  autoFocus={index === 0 && level === 0}
                                  placeholder={`Enter ${displayKey}`}
                                  title={`Confidence: ${
                                    dataVal.confidence
                                      ? Math.round(dataVal.confidence * 100) /
                                        100
                                      : "Not evaluated"
                                  }`}
                                  rows={1}
                                />
                              );
                            } else {
                              return (
                                <input
                                  autoComplete={"off"}
                                  key={nestedKey}
                                  id={nestedKey + "_field"}
                                  value={watchedValue}
                                  onChange={(e) => {
                                    onChange(e.target.value);
                                    onChangeHandler(nestedKey, e.target.value);
                                  }}
                                  onBlur={() => {
                                    onBlur();
                                    trigger(nestedKey);
                                  }}
                                  ref={ref}
                                  style={{
                                    ...styles.input,
                                    border: getErrorMessage(nestedKey)
                                      ? "1px solid red"
                                      : "1px solid #607274",
                                  }}
                                  tabIndex={1}
                                  autoFocus={index === 0 && level === 0}
                                  placeholder={`Enter ${displayKey}`}
                                  title={`Confidence: ${
                                    dataVal.confidence
                                      ? Math.round(dataVal.confidence * 100) /
                                        100
                                      : "Not evaluated"
                                  }`}
                                />
                              );
                            }
                          }}
                        />
                        {!checkIfBooleanField(displayKey) && (
                          <ExtractTextButton
                            nestedKey={nestedKey}
                            onFieldChange={onChangeHandler}
                          />
                        )}
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            handleRevert(nestedKey);
                          }}
                          variant={"contained"}
                          style={styles.actionButton}
                        >
                          <UndoIcon />
                        </Button>
                      </Box>
                      {getErrorMessage(nestedKey) && (
                        <span
                          style={{
                            color: "red",
                            fontSize: "12px",
                            display: "flex",
                            justifyContent: "center",
                            width: "100%",
                          }}
                        >
                          {getErrorMessage(nestedKey)}
                        </span>
                      )}
                    </Box>
                  </Box>
                </Box>
              );
            }
            return undefined;
          }

          if (typeof dataVal === "object" && dataVal !== null) {
            if (!hasRequiredFields(dataVal)) return undefined;
            return (
              <Box
                key={nestedKey}
                id={nestedKey}
                sx={{
                  marginLeft: `${16 * level}px`,
                  color: "black",
                }}
              >
                <DisplayNameComponent
                  displayKey={displayKey}
                  level={level}
                  dataKey={nestedKey1}
                />
                {renderInput(dataVal, nestedKey, level + 1)}
              </Box>
            );
          }
        });
      },
      [pageIndex, watchedFields],
    );

    const handleKeyDown = (
      e: React.KeyboardEvent<HTMLButtonElement>,
      direction: string,
    ): void => {
      if (e.key === "Enter") {
        e.preventDefault();
        handleNavigation(e, direction);
      }
    };

    useEffect(() => {
      containerRef.current.scrollTo(0, 0);
    }, [pageIndex]);

    const { metaData, docType, pagesRef } = metadata[pageIndex];
    const { duplicate_of } = useMemo(
      () => slices[pageIndex],
      [slices, pageIndex],
    );

    const totalPages = useMemo(() => metadata.length, [metadata.length]);

    const topLevelKeys = useMemo(() => {
      return Object.keys(metaData || {});
    }, [metaData]);

    return (
      <>
        <Box
          style={{
            fontFamily: "Arial, sans-serif",
            backgroundColor: "#f9f9f9",
            borderRadius: "8px",
            padding: "8px",
            minHeight: "400px",
            maxHeight: "calc(100%)",
            display: "flex",
            flexDirection: "column",
            alignItems: "stretch",
            justifyContent: "space-between",
            position: "relative",
          }}
        >
          <Box sx={{ display: "flex", gap: 2 }}>
            <SidebarNavigation
              topLevelKeys={topLevelKeys}
              handleSidebarNavigation={handleSidebarNavigation}
            />
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                width: "100%",
              }}
            >
              <Box>
                <Typography color={"black"} typography={"h5"}>
                  Document type: {docType || "Not detected"}
                </Typography>
                <Typography color={"black"} typography={"h7"}>
                  Pages: {formatPageRanges(pagesRef)}
                </Typography>
              </Box>
              <Box>
                {duplicate_of && (
                  <Typography color={"error"} sx={{ fontSize: 18 }}>
                    Duplicated
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
          <Box sx={{ ...styles.container, display: "flex" }} ref={containerRef}>
            <>
              {renderInput(
                {
                  metaData: metaData,
                },
                "",
                0,
              )}
            </>
          </Box>
          <Box
            style={{
              display: "flex",
              justifyContent: "center",
              marginTop: "20px",
              zIndex: 100,
              right: "20px",
              alignItems: "center",
              gap: "16px",
            }}
          >
            <Button
              onClick={(e) => handleNavigation(e, "prev")}
              style={{
                ...styles.button,
                ...(pageIndex === 0 ? styles.disabled : {}),
                marginTop: 0,
              }}
              disabled={pageIndex === 0}
              tabIndex={1}
              onKeyDown={(e) => handleKeyDown(e, "prev")}
            >
              Previous
            </Button>
            <Box
              style={{
                color: "black",
                height: "40px",
                display: "flex",
                alignItems: "center",
              }}
            >
              {pageIndex + 1} of {totalPages}
            </Box>
            <Button
              onClick={(e) => handleNavigation(e, "next")}
              style={{
                ...styles.button,
                marginTop: 0,
                ...(pageIndex === totalPages - 1 ? styles.disabled : {}),
              }}
              disabled={pageIndex === totalPages - 1}
              tabIndex={1}
              onKeyDown={(e) => handleKeyDown(e, "next")}
            >
              Next
            </Button>
          </Box>
        </Box>
      </>
    );
  },
);

const styles = {
  container: {
    fontFamily: "Arial, sans-serif",
    backgroundColor: "#f9f9f9",
    borderRadius: "8px",
    padding: "8px",
    minWidth: `calc(100%)`,
    margin: "0 auto",
    height: "calc(80vh - 160px)",
    overflowY: "auto",
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
  title: {
    color: "#333",
    fontSize: "24px",
    marginBottom: "20px",
    textAlign: "center",
  },
  inputContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gridTemplateColumns: "360px 70% 20%",
    gap: "10px",
  },
  input: {
    padding: "8px",
    borderRadius: "4px",
    border: "1px solid #ccc",
    width: `calc(100%)`,
    height: "40px",
    fontFamily: "Arial, sans-serif",
    fontSize: "16px",
  },
  actionButton: {
    height: "40px",
    width: "40px",
    minWidth: "40px",
  },
  button: {
    color: "black",
    border: "1px solid black",
    padding: "10px 20px",
    borderRadius: "4px",
    height: "40px",
    fontSize: "16px",
    cursor: "pointer",
    alignSelf: "center",
    width: "200px",
    backgroundColor: "transparent",
  },
  pageInput: {
    color: "black",
    height: "40px",
    width: "40px",
  },
  disabled: {
    backgroundColor: "#ccc",
    color: "gray",
    cursor: "not-allowed",
  },
};

export default EntryViewer;
