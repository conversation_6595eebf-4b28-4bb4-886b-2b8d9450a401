import UndoIcon from "@mui/icons-material/Undo";
import { Box, Button } from "@mui/material";
import { debounce } from "lodash";
import React, { useCallback } from "react";
import { Controller } from "react-hook-form";
import {
  getColor,
  hasRequiredFields,
  isMetadataEntry,
} from "../../helpers/helperFunctions";
import DateInput from "../Inputs/DateInput";
import PhoneFaxInput from "../Inputs/PhoneMaskInput";
import StateInput from "../Inputs/StateInput";
import ZipCodeInput from "../Inputs/ZipCodeMaskedInput";
import { getValidations } from "./helpers";

interface InputRendererProps {
  obj: PacketMetadata | any;
  prefix: string;
  level: number;
  pageIndex: number;
  control: any;
  getErrorMessage: (name: string) => any;
  handleInputChange: (path: string, value: string) => void;
  handleRevert: (nestedKey: string) => void;
  trigger: any;
  addItem: any;
}

const InputRenderer: React.FC<InputRendererProps> = ({
  obj,
  prefix,
  level,
  pageIndex,
  control,
  getErrorMessage,
  handleInputChange,
  handleRevert,
  trigger,
  addItem,
}) => {
  const onChangeHandler = useCallback(
    debounce((nestedKey: string, value: any) => {
      handleInputChange(nestedKey, value);
      trigger(nestedKey);
    }, 50),
    [handleInputChange, trigger],
  );

  return (
    <>
      {Object.entries(obj).map(([key, dataVal], index) => {
        const nestedKey1 = prefix ? `${prefix}.${key}` : `${key}`;
        const nestedKey =
          level === 0 ? `${pageIndex}.${nestedKey1}` : `${nestedKey1}`;
        const displayKey =
          key.charAt(0).toUpperCase() + key.slice(1).split("_").join(" ");

        if (dataVal && Array.isArray(dataVal)) {
          return (
            <Box
              key={`${nestedKey1}_page_${pageIndex}`}
              sx={{
                marginLeft: `${16 * level}px`,
                color: getColor(level),
              }}
            >
              <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
                <Box>{`${displayKey}:`}</Box>
              </Box>
              {dataVal.map((item, idx) => (
                <Box
                  key={`${item.id || `${nestedKey}[${idx}]`}_page_${pageIndex}`}
                >
                  <InputRenderer
                    obj={{ [`${idx}`]: item }}
                    prefix={nestedKey1}
                    level={level + 1}
                    pageIndex={pageIndex}
                    control={control}
                    getErrorMessage={getErrorMessage}
                    handleInputChange={handleInputChange}
                    handleRevert={handleRevert}
                    trigger={trigger}
                    addItem={addItem}
                  />
                  {idx === dataVal.length - 1 && (
                    <button
                      style={{ ...styles.button }}
                      onClick={(e) => addItem(e, nestedKey, item)}
                    >
                      Add item
                    </button>
                  )}
                </Box>
              ))}
            </Box>
          );
        }

        if (dataVal && isMetadataEntry(dataVal)) {
          if (dataVal.required) {
            return (
              <Box
                key={`${nestedKey}_page_${pageIndex}`}
                style={{
                  marginLeft: `${16 * level}px`,
                  color: getColor(level),
                  display: "flex",
                  alignItems: "center",
                  ...styles.inputContainer,
                  height: 80,
                  justifyContent: "space-between",
                  fontWeight: dataVal.value !== "" ? 300 : 900,
                }}
              >
                <Box
                  sx={{
                    minWidth: "300px",
                    color: getColor(level),
                    height: "40px",
                    display: level === 0 ? "flex" : "block",
                    flexDirection: level === 0 ? "column" : "initial",
                    justifyContent: level === 0 ? "center" : "initial",
                  }}
                >
                  {displayKey}:
                </Box>
                <Box style={{ display: "flex", gap: "8px", width: "100%" }}>
                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "start",
                      alignItems: "start",
                      width: "100%",
                      height: "52px",
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        gap: "16px",
                        width: "100%",
                      }}
                    >
                      <Controller
                        key={`${nestedKey}_page_${pageIndex}_${displayKey}`}
                        name={`${nestedKey}_page_${pageIndex}_${displayKey}`}
                        control={control}
                        rules={getValidations(displayKey)}
                        defaultValue={dataVal.value}
                        render={({
                          field: { onChange, onBlur, value, ref },
                        }) => {
                          if (displayKey.toLowerCase().includes("date")) {
                            return (
                              <DateInput
                                key={`${nestedKey}_page_${pageIndex}_${displayKey}_input`}
                                id={`${nestedKey}_page_${pageIndex}_${displayKey}_input`}
                                value={
                                  typeof value === "string"
                                    ? value
                                    : value?.value || ""
                                }
                                onChange={(e) => {
                                  onChange(e.target.value);
                                  onChangeHandler(nestedKey, e.target.value);
                                }}
                                onBlur={() => {
                                  onBlur();
                                  trigger(nestedKey);
                                }}
                                style={{
                                  ...styles.input,
                                  border: getErrorMessage(nestedKey)
                                    ? "1px solid red"
                                    : "1px solid #607274",
                                }}
                                tabIndex={1}
                                autoFocus={index === 0 && level === 0}
                                title={`Confidence: ${dataVal.confidence}`}
                              />
                            );
                          } else if (
                            displayKey.toLowerCase().includes("state")
                          ) {
                            return (
                              <StateInput
                                key={`${nestedKey}_page_${pageIndex}_${displayKey}_input`}
                                id={`${nestedKey}_page_${pageIndex}_${displayKey}_input`}
                                value={
                                  typeof value === "string"
                                    ? value
                                    : value?.value || ""
                                }
                                onChange={(e) => {
                                  onChange(e.target.value);
                                  onChangeHandler(nestedKey, e.target.value);
                                }}
                                onBlur={() => {
                                  onBlur();
                                  trigger(nestedKey);
                                }}
                                style={{
                                  ...styles.input,
                                  border: getErrorMessage(nestedKey)
                                    ? "1px solid red"
                                    : "1px solid #607274",
                                }}
                                tabIndex={1}
                                autoFocus={index === 0 && level === 0}
                                title={`Confidence: ${dataVal.confidence}`}
                              />
                            );
                          } else if (
                            displayKey.toLowerCase().includes("zipcode")
                          ) {
                            return (
                              <ZipCodeInput
                                id={`${nestedKey}_page_${pageIndex}_${displayKey}_input`}
                                key={`${nestedKey}_page_${pageIndex}_${displayKey}_input`}
                                value={
                                  typeof value === "string"
                                    ? value
                                    : value?.value || ""
                                }
                                onChange={(e) => {
                                  onChange(e.target.value);
                                  onChangeHandler(nestedKey, e.target.value);
                                }}
                                onBlur={() => {
                                  onBlur();
                                  trigger(nestedKey);
                                }}
                                style={{
                                  ...styles.input,
                                  border: getErrorMessage(nestedKey)
                                    ? "1px solid red"
                                    : "1px solid #607274",
                                }}
                                tabIndex={1}
                                autoFocus={index === 0 && level === 0}
                                title={`Confidence: ${dataVal.confidence}`}
                              />
                            );
                          } else if (
                            displayKey.toLowerCase().includes("phone")
                          ) {
                            return (
                              <PhoneFaxInput
                                id={`${nestedKey}_page_${pageIndex}_${displayKey}_input`}
                                key={`${nestedKey}_page_${pageIndex}_${displayKey}_input`}
                                value={
                                  typeof value === "string"
                                    ? value
                                    : value?.value || ""
                                }
                                onChange={(e) => {
                                  onChange(e.target.value);
                                  onChangeHandler(nestedKey, e.target.value);
                                }}
                                onBlur={() => {
                                  onBlur();
                                  trigger(nestedKey);
                                }}
                                style={{
                                  ...styles.input,
                                  border: getErrorMessage(nestedKey)
                                    ? "1px solid red"
                                    : "1px solid #607274",
                                }}
                                tabIndex={1}
                                autoFocus={index === 0 && level === 0}
                                title={`Confidence: ${dataVal.confidence}`}
                              />
                            );
                          } else {
                            return (
                              <input
                                id={`${nestedKey}_page_${pageIndex}_${displayKey}_input`}
                                key={`${nestedKey}_page_${pageIndex}_${displayKey}_input`}
                                value={
                                  typeof value === "string"
                                    ? value
                                    : value?.value || ""
                                }
                                onChange={(e) => {
                                  onChange(e.target.value);
                                  onChangeHandler(nestedKey, e.target.value);
                                }}
                                onBlur={() => {
                                  onBlur();
                                  trigger(nestedKey);
                                }}
                                ref={ref}
                                style={{
                                  ...styles.input,
                                  border: getErrorMessage(nestedKey)
                                    ? "1px solid red"
                                    : "1px solid #607274",
                                }}
                                tabIndex={1}
                                autoFocus={index === 0 && level === 0}
                                placeholder={`Enter ${displayKey}`}
                                title={`Confidence: ${
                                  dataVal.confidence
                                    ? dataVal.confidence
                                    : "Not evaluated"
                                }`}
                              />
                            );
                          }
                        }}
                      />
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          handleRevert(nestedKey);
                        }}
                        style={styles.revertButton}
                      >
                        <UndoIcon sx={{ color: "black" }} />
                      </Button>
                    </Box>
                    {getErrorMessage(nestedKey) && (
                      <span
                        style={{
                          color: "red",
                          fontSize: "12px",
                          display: "flex",
                          justifyContent: "center",
                          width: "100%",
                        }}
                      >
                        {getErrorMessage(nestedKey)}
                      </span>
                    )}
                  </Box>
                </Box>
              </Box>
            );
          }
          return undefined;
        }

        if (typeof dataVal === "object" && dataVal !== null) {
          if (!hasRequiredFields(dataVal)) return undefined;
          return (
            <Box
              key={`${nestedKey}_page_${pageIndex}`}
              sx={{
                marginLeft: `${16 * level}px`,
                color: getColor(level),
              }}
            >
              <Box>{`${displayKey}:`}</Box>
              <InputRenderer
                obj={dataVal}
                prefix={nestedKey}
                level={level + 1}
                pageIndex={pageIndex}
                control={control}
                getErrorMessage={getErrorMessage}
                handleInputChange={handleInputChange}
                handleRevert={handleRevert}
                trigger={trigger}
                addItem={addItem}
              />
            </Box>
          );
        }
      })}
    </>
  );
};

const styles = {
  container: {
    fontFamily: "Arial, sans-serif",
    backgroundColor: "#f9f9f9",
    borderRadius: "8px",
    padding: "8px",
    minWidth: `calc(100%)`,
    margin: "0 auto",
    height: "calc(80vh - 160px)",
    overflowY: "auto",
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
  title: {
    color: "#333",
    fontSize: "24px",
    marginBottom: "20px",
    textAlign: "center",
  },
  inputContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gridTemplateColumns: "360px 70% 20%",
    gap: "10px",
    color: "red",
  },
  input: {
    padding: "8px",
    borderRadius: "4px",
    border: "1px solid #ccc",
    width: `calc(100%)`,
    height: "40px",
  },
  revertButton: {
    padding: "6px",
    borderRadius: "4px",
    backgroundColor: "#f0f0f0",
    border: "none",
    cursor: "pointer",
    height: "40px",
  },
  button: {
    color: "black",
    border: "1px solid black",
    padding: "10px 20px",
    borderRadius: "4px",
    height: "40px",
    fontSize: "16px",
    cursor: "pointer",
    alignSelf: "center",
    width: "200px",
    backgroundColor: "transparent",
  },
  pageInput: {
    color: "black",
    height: "40px",
    width: "40px",
  },
  disabled: {
    backgroundColor: "#ccc",
    color: "gray",
    cursor: "not-allowed",
  },
};

export default InputRenderer;
