import React, { CSSProperties, InputHTMLAttributes } from "react";
import { NumericFormat } from "react-number-format";

interface Props extends InputHTMLAttributes<HTMLInputElement> {
  value: string;
  style?: CSSProperties;
  onChange: (event: { target: { value: string } }) => void;
}

export const QuantityInput: React.FC<Props> = ({ value, onChange, style }) => {
  return (
    <NumericFormat
      value={value}
      onChange={onChange}
      style={style}
      placeholder={"Enter Quantity"}
      allowNegative={false}
      allowLeadingZeros
      allowedDecimalSeparators={[]}
      decimalScale={0}
    />
  );
};
