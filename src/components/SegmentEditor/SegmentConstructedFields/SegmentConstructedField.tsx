import { FC, useMemo } from "react";
import {
  isPatientNameField,
  isPhysicianNameField,
} from "../SegmentFields/utils/constructed.utils.ts";
import { getValidationSchema } from "../SegmentFields/utils/getValidationSchema.ts";
import { SegmentPatientNameField } from "./SegmentPatientNameField.tsx";
import { SegmentPhysicianNameField } from "./SegmentPhysicianNameField.tsx";

interface Props {
  index?: number;
  base: string;
  name: string;
  label: string;
  level?: number;
  treatment?: boolean;
}

export const SegmentConstructedField: FC<Props> = (props) => {
  const key = useMemo(() => `${props.base}.${props.name}`, [props]);

  const validationSchema = useMemo(() => getValidationSchema(key), [key]);

  const Field = useMemo(() => {
    switch (true) {
      case isPatientNameField(key):
        return SegmentPatientNameField;
      case isPhysicianNameField(key):
        return SegmentPhysicianNameField;
      default:
        return null;
    }
  }, [key]);

  if (!Field) return null;
  return <Field key={key} validation={validationSchema} {...props} />;
};
