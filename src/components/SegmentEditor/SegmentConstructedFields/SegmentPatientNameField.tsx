import UndoIcon from "@mui/icons-material/Undo";
import { Box, Button, TextField, Tooltip, Typography } from "@mui/material";
import { FC, useCallback, useMemo } from "react";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { ExtractTextButton } from "../SegmentConstructions/ExtractTextButton.tsx";
import { SegmentFieldValidator } from "../SegmentConstructions/SegmentFieldValidator.tsx";
import {
  addMark,
  addTextDecoration,
  getColorByRequired,
} from "../SegmentFields/utils/fields.utils.ts";
import { FieldProps, PackageToUpdate } from "../segment.types.ts";

const styles: StyleCreator<"container" | "button" | "input"> = () => ({
  container: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "24px",
  },
  input: {
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    gap: "8px",
  },
  button: {
    height: "40px",
    width: "40px",
    minWidth: "40px",
  },
});

export const SegmentPatientNameField: FC<FieldProps> = ({
  index,
  base,
  name,
  label,
  level = 0,
  validation,
}) => {
  const c = useStyleCreator(styles);
  const fieldKey = useMemo(() => `${base}.${name}.value`, [base, name]);
  const { control, setValue } = useFormContext();

  const { patientFirstName, patientMiddleName, patientLastName } = useWatch({
    name: `metadata.${index}.namingData`,
    control,
  });

  const constructedValue = useMemo(() => {
    return [
      patientFirstName.value,
      patientMiddleName.value,
      patientLastName.value,
    ]
      .filter(Boolean)
      .join(" ");
  }, [patientFirstName, patientLastName, patientMiddleName]);

  const handleTextExtract = useCallback(
    (value: string) => {
      const nameArray = value.split(" ");
      const length = nameArray.length;

      setValue(fieldKey, value);
      switch (true) {
        case length === 1: {
          const [firstName] = nameArray;
          setValue(
            `metadata.${index}.namingData.patientFirstName.value`,
            firstName,
          );
          setValue(`metadata.${index}.namingData.patientMiddleName.value`, "");
          setValue(`metadata.${index}.namingData.patientLastName.value`, "");
          break;
        }
        case length === 2: {
          const [firstName, lastName] = nameArray;
          setValue(
            `metadata.${index}.namingData.patientFirstName.value`,
            firstName,
          );
          setValue(`metadata.${index}.namingData.patientMiddleName.value`, "");
          setValue(
            `metadata.${index}.namingData.patientLastName.value`,
            lastName,
          );
          break;
        }
        case length >= 3: {
          const [firstName, middleName, lastName] = nameArray;
          setValue(
            `metadata.${index}.namingData.patientFirstName.value`,
            firstName,
          );
          setValue(
            `metadata.${index}.namingData.patientMiddleName.value`,
            middleName,
          );
          setValue(
            `metadata.${index}.namingData.patientLastName.value`,
            lastName,
          );
          break;
        }
      }
    },
    [fieldKey, index, setValue],
  );

  const { resetField } = useFormContext<PackageToUpdate>();
  const handleReset = useCallback(() => {
    resetField(fieldKey as "metadata");
    resetField(`metadata.${index}.namingData.patientFirstName.value`);
    resetField(`metadata.${index}.namingData.patientMiddleName.value`);
    resetField(`metadata.${index}.namingData.patientLastName.value`);
  }, [fieldKey, index, resetField]);

  return (
    <Controller
      control={control}
      name={fieldKey}
      render={({ field: { ref }, fieldState }) => {
        return (
          <Box sx={c.container}>
            <SegmentFieldValidator
              validation={validation}
              fieldKey={fieldKey}
              value={constructedValue}
            />
            <Typography
              textTransform={"capitalize"}
              style={{
                minWidth: "35%",
                paddingLeft: `${level * 40}px`,
                color: getColorByRequired(fieldKey),
                textDecoration: addTextDecoration(fieldKey),
              }}
              fontWeight={constructedValue ? 500 : 700}
            >
              {label}
              {addMark(fieldKey)}:
            </Typography>
            <Box sx={c.input}>
              <Tooltip title={fieldState.error?.message ?? null}>
                <TextField
                  type={"text"}
                  value={constructedValue}
                  ref={ref}
                  size={"small"}
                  fullWidth
                  error={fieldState.invalid}
                  placeholder={`Enter ${label}`}
                  InputProps={{ readOnly: true }}
                  helperText={"readonly"}
                  sx={{
                    "& .MuiFormHelperText-root": {
                      width: "fit-content",
                      margin: "0",
                      position: "absolute",
                      bottom: 0,
                      right: "10px",
                    },
                  }}
                />
              </Tooltip>
              <ExtractTextButton
                onChange={handleTextExtract}
                fieldKey={fieldKey}
              />
              <Button
                tabIndex={-1}
                sx={c.button}
                variant={"outlined"}
                onClick={handleReset}
              >
                <UndoIcon />
              </Button>
            </Box>
          </Box>
        );
      }}
    />
  );
};
