import { Close } from "@mui/icons-material";
import { Box, Button } from "@mui/material";
import { FC, useCallback } from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import {
  emptyBilling,
  emptyDiagnosis,
  emptyOtherProcedure,
  emptyPayer,
  emptyPhysician,
  emptyProvider,
  emptyTreatment,
} from "../../../helpers/documentTypes.ts";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { SegmentFieldDrawer } from "./SegmentFieldDrawer.tsx";
import { SegmentObjectField } from "./SegmentObjectField.tsx";

const styles: StyleCreator<"fields"> = () => ({
  fields: {
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
});

interface Props {
  base: string;
  name: string;
  level?: number;
}

export const SegmentArrayField: FC<Props> = ({ base, name, level }) => {
  const c = useStyleCreator(styles);

  const { control } = useFormContext();
  const { fields, append, remove } = useFieldArray({
    control,
    name: base,
  });

  const handleAddItem = useCallback(() => {
    switch (name) {
      case "treatments":
        return append(emptyTreatment);
      case "billing":
        return append(emptyBilling);
      case "diagnosis":
        return append(emptyDiagnosis);
      case "physician":
        return append(emptyPhysician);
      case "payer":
        return append(emptyPayer);
      case "providers":
        return append(emptyProvider);
      case "otherProcedure":
        return append(emptyOtherProcedure);
    }
  }, [append, name]);
  const handleRemoveItem = useCallback(
    (index: number) => () => remove(index),
    [remove],
  );

  return (
    <Box sx={c.fields}>
      {fields.map((field, index) => {
        const keys = Object.keys(field);
        return (
          <SegmentFieldDrawer
            key={field.id}
            label={index.toString()}
            level={level / 2}
            endComponent={
              <Button
                color={"error"}
                variant={"outlined"}
                sx={{ height: "32px", minWidth: "32px", padding: "0" }}
                onClick={handleRemoveItem(index)}
              >
                <Close />
              </Button>
            }
          >
            {keys.map((key) => {
              if (key === "id") return null;
              const { id, ...metadata } = field;

              return (
                <SegmentObjectField
                  key={`${id}-${key}`}
                  index={index}
                  activeData={"metaData"}
                  metadata={metadata}
                  base={`${base}.${index}`}
                  name={key}
                  label={key}
                  level={level / 2 + 1}
                  treatment={name === "treatments"}
                />
              );
            })}
          </SegmentFieldDrawer>
        );
      })}
      <Button
        sx={{ marginLeft: `${(level / 2) * 40}px`, marginBottom: "8px" }}
        variant={"outlined"}
        onClick={handleAddItem}
      >
        Add new {name}
      </Button>
    </Box>
  );
};
