import { Crop<PERSON>ree } from "@mui/icons-material";
import { Button, CircularProgress } from "@mui/material";
import { FC, useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useScreenshotContext } from "../../../hooks/useScreenshotContext.ts";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import {
  useExtractText,
  useExtractedText,
} from "../../../store/queries/chunks/chunks.query.ts";
import { validateAndFormatString } from "../../Editor/helpers.ts";

const styles: StyleCreator<"button"> = () => ({
  button: {
    height: "40px",
    width: "40px",
    minWidth: "40px",
    padding: "0",
  },
});

interface Props {
  onChange: (...event: any[]) => void;
  fieldKey: string;
}

export const ExtractTextButton: FC<Props> = ({ onChange, fieldKey }) => {
  const c = useStyleCreator(styles);

  const [loading, setLoading] = useState(false);

  const { makeScreenshot, base64, dumpImage, rowToUpdate, isProcessing } =
    useScreenshotContext();

  const [extractText] = useExtractText();
  const [getExtractText] = useExtractedText();

  const getText = useCallback(
    async (base64: string, packet_id: string) => {
      const { screenshot_id } = await extractText({
        base64,
        packet_id,
      }).unwrap();

      const ocrResponse = await getExtractText({ screenshot_id }).unwrap();

      return { ocrResponse };
    },
    [extractText, getExtractText],
  );

  const validateResponse = useCallback(
    ({ ocrResponse }) =>
      validateAndFormatString(fieldKey, ocrResponse.text_array.join(". ")),
    [fieldKey],
  );

  const raiseError = useCallback((error) => {
    let message = "Error occurred during text extraction";

    if (["TimeoutError", "AbortError"].includes(error?.name)) {
      message =
        "It appears that the content in the selected area wasn’t recognized. Please try again";
    } else if (error?.message) {
      message = error?.message;
    }

    toast.error(message, {
      position: "bottom-right",
      duration: 10_000,
      style: { minWidth: "400px", maxWidth: "400px" },
    });
  }, []);

  useEffect(() => {
    const packet_id = localStorage.getItem("activeFile");

    if (base64 && packet_id && rowToUpdate === fieldKey) {
      setLoading(true);
      getText(base64, packet_id)
        .then(validateResponse)
        .then(onChange)
        .catch(raiseError)
        .finally(() => {
          dumpImage();
          setLoading(false);
        });
    }
  }, [
    base64,
    dumpImage,
    fieldKey,
    getText,
    onChange,
    raiseError,
    rowToUpdate,
    validateResponse,
  ]);

  return (
    <Button
      tabIndex={-1}
      sx={c.button}
      variant={"outlined"}
      onClick={() => makeScreenshot(fieldKey)}
      disabled={isProcessing || Boolean(base64)}
    >
      {!loading && <CropFree />}
      {loading && <CircularProgress size={20} />}
    </Button>
  );
};
