import { Box, CircularProgress } from "@mui/material";
import { FC } from "react";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";

const styles: StyleCreator<"container"> = () => ({
  container: {
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    position: "absolute",
    backgroundColor: "rgba(0,0,0,0.5)",
    zIndex: "1000",
    transition: "all 0.4s ease-in-out",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
});

export const SegmentLoadingOverlay: FC = () => {
  const c = useStyleCreator(styles);

  return (
    <Box sx={c.container}>
      <CircularProgress />
    </Box>
  );
};
