import { Box, Typography } from "@mui/material";
import { FC, useCallback, useMemo } from "react";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { SegmentConstructedField } from "../SegmentConstructedFields/SegmentConstructedField.tsx";
import { SegmentField } from "../SegmentFields/SegmentField.tsx";
import { isConstructedField } from "../SegmentFields/utils/constructed.utils.ts";
import { MetaData, NamingData, NestedField } from "../segment.types.ts";
import { SegmentArrayField } from "./SegmentArrayField.tsx";
import { SegmentFieldDrawer } from "./SegmentFieldDrawer.tsx";
import { SegmentWatchers } from "./SegmentWatchers.tsx";

interface Props {
  index: number;
  activeData: "namingData" | "metaData";
  metadata: NamingData | MetaData | NestedField;
  base: string;
  name: string;
  label: string;
  level?: number;
  treatment?: boolean;
}

const styles: StyleCreator<"container" | "fields"> = () => ({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: "24px",
  },
  fields: {
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
});

export const SegmentObjectField: FC<Props> = ({
  index,
  activeData,
  metadata,
  base,
  name,
  label,
  level,
  treatment,
}) => {
  const c = useStyleCreator(styles);

  const nestedField = useMemo(
    () => metadata[name] as NamingData | MetaData | NestedField,
    [metadata, name],
  );

  const getObjectKeys = useCallback((object) => Object.keys(object), []);

  if (Object.keys(nestedField).includes("value")) {
    const isConstructed = isConstructedField(`${base}.${name}`);
    const Field = isConstructed ? SegmentConstructedField : SegmentField;

    return (
      <>
        <SegmentWatchers fieldKey={`${base}.${name}`} index={index} />
        <Field
          base={base}
          name={name}
          label={name}
          level={level}
          index={index}
          treatment={treatment}
        />
      </>
    );
  }

  if (Array.isArray(nestedField))
    return (
      <Box sx={c.container}>
        <Typography
          textTransform={"capitalize"}
          style={{
            marginLeft: `${level * 40}px`,
          }}
        >
          {label}:
        </Typography>
        <Box sx={c.fields}>
          <SegmentArrayField
            key={`${base}.${name}`}
            base={`${base}.${name}`}
            name={name}
            level={level + 1}
          />
        </Box>
      </Box>
    );

  return (
    <SegmentFieldDrawer label={label} level={level}>
      {getObjectKeys(nestedField).map((key) => {
        return (
          <SegmentObjectField
            key={key}
            index={index}
            activeData={activeData}
            metadata={nestedField}
            base={`${base}.${name}`}
            name={key}
            label={key}
            level={level + 1}
          />
        );
      })}
    </SegmentFieldDrawer>
  );
};
