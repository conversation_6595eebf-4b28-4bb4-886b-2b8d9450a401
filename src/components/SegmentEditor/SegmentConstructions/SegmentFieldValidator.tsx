import { debounce } from "lodash";
import { FC, useEffect, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import { StringSchema } from "yup";

interface Props {
  fieldKey: string;
  value: string;
  validation: StringSchema;
}

export const SegmentFieldValidator: FC<Props> = ({
  fieldKey,
  value,
  validation,
}) => {
  const { setError, clearErrors } = useFormContext();

  const cachedValidation = useMemo(
    () =>
      debounce((value: string) => {
        validation
          .validate(value)
          .catch((error) => setError(fieldKey, { message: error.message }));
      }, 200),
    [fieldKey, setError, validation],
  );

  useEffect(() => {
    clearErrors(fieldKey);
    if (value) cachedValidation(value);
  }, [cachedValidation, clearErrors, fieldKey, value]);

  return null;
};
