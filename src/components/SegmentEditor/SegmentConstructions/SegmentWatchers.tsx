import { FC, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import { SegmentPatientNameWatcher } from "../SegmentWatchers/SegmentPatientNameWatcher.tsx";
import { SegmentPhysicianNameWatcher } from "../SegmentWatchers/SegmentPhysicianNameWatcher.tsx";
import { getSegmentWatcher } from "../SegmentWatchers/getSegmentWatcher.tsx";
import { PackageToUpdate } from "../segment.types.ts";

interface Props {
  fieldKey: string;
  index: number;
}

export const SegmentWatchers: FC<Props> = ({ fieldKey, index }) => {
  const { formState } = useFormContext<PackageToUpdate>();

  const watchersToSubscribe = useMemo(() => {
    const watchers = [];
    const index_to_skip = formState.defaultValues.predictions.reduce(
      (acc, item, index) => {
        if (item.other_patient) return [...acc, index];
        return acc;
      },
      [] as number[],
    );

    if (fieldKey.includes("physicianName"))
      watchers.push(SegmentPhysicianNameWatcher);

    if (fieldKey.includes("patientName"))
      watchers.push(SegmentPatientNameWatcher);

    if (!index_to_skip.includes(index)) {
      if (fieldKey.includes("namingData.claimNumber"))
        watchers.push(
          getSegmentWatcher({
            field: "claimNumber",
            path_to_watch: "namingData",
            path_to_update: ["namingData", "metaData.claim"],
            index_to_skip,
          }),
        );
      if (fieldKey.includes("metaData.claim.claimNumber"))
        watchers.push(
          getSegmentWatcher({
            field: "claimNumber",
            path_to_watch: "metaData.claim",
            path_to_update: ["namingData", "metaData.claim"],
            index_to_skip,
          }),
        );

      if (fieldKey.includes("namingData.senderName"))
        watchers.push(
          getSegmentWatcher({
            field: "senderName",
            path_to_watch: "namingData",
            path_to_update: ["namingData", "metaData"],
            index_to_skip,
          }),
        );
      if (fieldKey.includes("metaData.senderName"))
        watchers.push(
          getSegmentWatcher({
            field: "senderName",
            path_to_watch: "metaData",
            path_to_update: ["namingData", "metaData"],
            index_to_skip,
          }),
        );

      if (fieldKey.includes("namingData.docReceivedDate"))
        watchers.push(
          getSegmentWatcher({
            field: "docReceivedDate",
            path_to_watch: "namingData",
            path_to_update: ["namingData", "metaData"],
            index_to_skip,
          }),
        );
      if (fieldKey.includes("metaData.docReceivedDate"))
        watchers.push(
          getSegmentWatcher({
            field: "docReceivedDate",
            path_to_watch: "metaData",
            path_to_update: ["namingData", "metaData"],
            index_to_skip,
          }),
        );

      // patientName
      if (fieldKey.includes("namingData.patientFirstName")) {
        watchers.push(
          getSegmentWatcher({
            field: "patientFirstName",
            path_to_watch: "namingData",
            path_to_update: ["namingData"],
            index_to_skip,
          }),
        );
      }
      if (fieldKey.includes("namingData.patientMiddleName")) {
        watchers.push(
          getSegmentWatcher({
            field: "patientMiddleName",
            path_to_watch: "namingData",
            path_to_update: ["namingData"],
            index_to_skip,
          }),
        );
      }
      if (fieldKey.includes("namingData.patientLastName")) {
        watchers.push(
          getSegmentWatcher({
            field: "patientLastName",
            path_to_watch: "namingData",
            path_to_update: ["namingData"],
            index_to_skip,
          }),
        );
      }
      // if (fieldKey.includes("namingData.patientName")) {
      //   watchers.push(
      //     getSegmentWatcher({
      //       field: "patientName",
      //       path_to_watch: "namingData",
      //       path_to_update: ["namingData", "metaData"],
      //       index_to_skip,
      //     }),
      //   );
      // }

      // claimant
      if (fieldKey.includes("claimant.firstName")) {
        watchers.push(
          getSegmentWatcher({
            field: "firstName",
            path_to_watch: "metaData.claimant",
            path_to_update: ["metaData.claimant"],
            index_to_skip,
          }),
        );
      }
      if (fieldKey.includes("claimant.middleName")) {
        watchers.push(
          getSegmentWatcher({
            field: "middleName",
            path_to_watch: "metaData.claimant",
            path_to_update: ["metaData.claimant"],
            index_to_skip,
          }),
        );
      }
      if (fieldKey.includes("claimant.lastName")) {
        watchers.push(
          getSegmentWatcher({
            field: "lastName",
            path_to_watch: "metaData.claimant",
            path_to_update: ["metaData.claimant"],
            index_to_skip,
          }),
        );
      }
      if (fieldKey.includes("claimant.dateOfBirth")) {
        watchers.push(
          getSegmentWatcher({
            field: "dateOfBirth",
            path_to_watch: "metaData.claimant",
            path_to_update: ["metaData.claimant"],
            index_to_skip,
          }),
        );
      }

      if (fieldKey.includes("dateOfInjuryFrom")) {
        watchers.push(
          getSegmentWatcher({
            field: "dateOfInjuryFrom",
            path_to_watch: "metaData.claim",
            path_to_update: ["metaData.claim"],
            index_to_skip,
          }),
        );
      }
      if (fieldKey.includes("dateOfInjuryThrough")) {
        watchers.push(
          getSegmentWatcher({
            field: "dateOfInjuryThrough",
            path_to_watch: "metaData.claim",
            path_to_update: ["metaData.claim"],
            index_to_skip,
          }),
        );
      }

      // attorney
      if (fieldKey.includes("attorney.firstName")) {
        watchers.push(
          getSegmentWatcher({
            field: "firstName",
            path_to_watch: "metaData.attorney",
            path_to_update: ["metaData.attorney"],
            index_to_skip,
          }),
        );
      }
      if (fieldKey.includes("attorney.lastName")) {
        watchers.push(
          getSegmentWatcher({
            field: "lastName",
            path_to_watch: "metaData.attorney",
            path_to_update: ["metaData.attorney"],
            index_to_skip,
          }),
        );
      }
      if (fieldKey.includes("attorney.type")) {
        watchers.push(
          getSegmentWatcher({
            field: "type",
            path_to_watch: "metaData.attorney",
            path_to_update: ["metaData.attorney"],
            index_to_skip,
          }),
        );
      }
      if (fieldKey.includes("attorney.company")) {
        watchers.push(
          getSegmentWatcher({
            field: "company",
            path_to_watch: "metaData.attorney",
            path_to_update: ["metaData.attorney"],
            index_to_skip,
          }),
        );
      }
    }

    return watchers;
  }, [fieldKey]);

  if (watchersToSubscribe.length > 0) {
    return watchersToSubscribe.map((Watcher, i) => (
      <Watcher
        key={`${fieldKey}-${i}`}
        fieldKey={`${fieldKey}-${i}`}
        index={index}
      />
    ));
  } else return null;
};
