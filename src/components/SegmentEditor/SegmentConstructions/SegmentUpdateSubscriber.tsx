import { useCallback, useEffect, useRef } from "react";
import { useFormContext } from "react-hook-form";
import { useDispatch } from "react-redux";
import { useChunkUpdate } from "../../../store/queries/chunks/chunks.query.ts";
import { updateStoreMetadata } from "../../../store/slices/metadata/metadata.slice.ts";
import { WSCloseStatus, WSWorkStatus } from "../../WSContext/types.ts";
import { useWS } from "../../WSContext/useWS.ts";
import { PackageToUpdate } from "../segment.types.ts";

export const SegmentUpdateSubscriber = () => {
  const { getValues } = useFormContext<PackageToUpdate>();
  const dispatch = useDispatch();
  const WS = useWS();

  const [part_update, { isLoading }] = useChunkUpdate();
  const UpdateTimerRef = useRef(null);

  const update = useCallback(() => {
    const package_id = localStorage.getItem("activeFile");
    const state = getValues();
    const metadata = JSON.parse(JSON.stringify(state.metadata));

    if (!isLoading) {
      part_update({
        package_id,
        payload: state,
        update_type: "part_update",
      })
        .unwrap()
        .then(() => dispatch(updateStoreMetadata(metadata)))
        .catch(() => {
          WS.openWSModal({
            workStatus: WSWorkStatus.ERROR,
            closeStatus: WSCloseStatus.InternalError,
          });
        });
    }
  }, [WS, dispatch, getValues, isLoading, part_update]);

  useEffect(() => {
    UpdateTimerRef.current = window.setInterval(update, 60000);
    return () => {
      window.clearTimeout(UpdateTimerRef.current);
    };
  }, [update]);

  useEffect(() => update(), []);

  return null;
};
