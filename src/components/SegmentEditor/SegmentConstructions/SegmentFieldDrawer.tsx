import { KeyboardArrowDown, KeyboardArrowUp } from "@mui/icons-material";
import { Box, Collapse, darken, Typography } from "@mui/material";
import { FC, ReactNode, useState } from "react";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";

interface Props {
  children: ReactNode;
  endComponent?: ReactNode;
  label: string;
  level?: number;
}

const styles: StyleCreator<"container" | "fields" | "header"> = (theme) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: darken("#607274", 0.1),
    padding: "4px 8px",
    borderRadius: "6px",
    cursor: "pointer",
    width: "100%",
  },
  fields: {
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
});

export const SegmentFieldDrawer: FC<Props> = ({
  children,
  endComponent,
  level = 0,
  label,
}) => {
  const c = useStyleCreator(styles);

  const [isFolded, setIsFolded] = useState(true);

  return (
    <Box sx={c.container}>
      <Box
        sx={{
          display: "flex",
          gap: "8px",
          alignItems: "center",
        }}
      >
        <Box
          sx={c.header}
          style={{
            marginLeft: `${level * 40}px`,
          }}
          onClick={() => setIsFolded((prev) => !prev)}
        >
          <Typography textTransform={"capitalize"}>{label}:</Typography>
          {isFolded && <KeyboardArrowUp />}
          {!isFolded && <KeyboardArrowDown />}
        </Box>
        {endComponent}
      </Box>
      <Collapse unmountOnExit in={!isFolded} orientation={"vertical"}>
        <Box sx={c.fields}>{children}</Box>
      </Collapse>
    </Box>
  );
};
