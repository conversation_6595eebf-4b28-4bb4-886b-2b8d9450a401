import { string } from "yup";

export const patientnameSchema = string()
  .trim()
  .nullable()
  .optional()
  .min(6, "Patient name should be at least 6 characters.")
  .max(30, "Patient name should be maximum 30 characters.")
  .matches(/^[^0-9]*$/, "Patient name cannot contain numbers.")
  .matches(
    /^[a-zA-Z0-9 \-,.&#@]*$/,
    "Patient name cannot contain special characters.",
  )
  .test({
    name: "patientname_field_test",
    message: "Patient name must include both name and surname.",
    test: (value) => value.split(" ").length >= 2,
  });
