import { Box, List, Slide } from "@mui/material";
import * as _ from "lodash";
import { Dispatch, FC, SetStateAction, useCallback, useMemo } from "react";
import Scrollbar from "react-scrollbars-custom";
import {
  StyleCreator,
  useStyleCreator,
} from "../../../hooks/useStyleCreator.ts";
import { SegmentDrawerGroup } from "./SegmentDrawerGroup.tsx";

interface Props {
  slices: EntryListItem[];
  open: boolean;
  sliceToShow: number;
  setOpen: Dispatch<SetStateAction<boolean>>;
  setSliceToShow: Dispatch<SetStateAction<number>>;
}

const styles: StyleCreator<"container" | "drawer" | "overlay"> = (theme) => ({
  container: {
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    position: "absolute",
    backgroundColor: "rgba(0,0,0,0.5)",
    zIndex: "1000",
    transition: "all 0.4s ease-in-out",
  },
  drawer: {
    height: "100%",
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    position: "absolute",
    width: "250px",
    backgroundColor: theme.palette.background.paper,
    padding: "0 2px",
  },
  overlay: {},
});

export const SegmentDrawer: FC<Props> = ({
  open,
  setOpen,
  sliceToShow,
  setSliceToShow,
  slices,
}) => {
  const c = useStyleCreator(styles);

  const { to_edit, to_hide } = useMemo(() => {
    const initialSlices = {
      to_edit: [] as EntryListItem[],
      to_hide: [] as EntryListItem[],
    };

    return slices.reduce((acc, slice, index) => {
      if (slice.incomplete || slice.duplicate_of) {
        return {
          ...acc,
          to_hide: [...acc.to_hide, { ...slice, index }],
        };
      }

      return {
        ...acc,
        to_edit: [...acc.to_edit, { ...slice, index }],
      };
    }, initialSlices);
  }, [slices]);

  const isHighlighted = useCallback(
    (slice: EntryListItem) => {
      return _.isEqual(slices[sliceToShow], slice);
    },
    [sliceToShow, slices],
  );

  return (
    <Box
      sx={c.container}
      style={{
        background: open ? "rgba(0,0,0,0.5)" : "transparent",
        pointerEvents: open ? "all" : "none",
      }}
      onClick={() => setOpen(false)}
    >
      <Slide timeout={400} in={open} direction={"right"}>
        <Box sx={c.drawer}>
          <Scrollbar>
            <List>
              <SegmentDrawerGroup
                label={"Slices to edit"}
                slices={to_edit}
                sliceToShow={sliceToShow}
                setSliceToShow={setSliceToShow}
                checkIsHighlighted={isHighlighted}
                defaultOpened
              />
              {to_hide.length > 0 && (
                <SegmentDrawerGroup
                  label={"Optional slices"}
                  slices={to_hide}
                  sliceToShow={sliceToShow}
                  setSliceToShow={setSliceToShow}
                  checkIsHighlighted={isHighlighted}
                />
              )}
            </List>
          </Scrollbar>
        </Box>
      </Slide>
    </Box>
  );
};
