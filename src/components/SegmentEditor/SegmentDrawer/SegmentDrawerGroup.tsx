import { ExpandLess, ExpandMore } from "@mui/icons-material";
import {
  Collapse,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from "@mui/material";
import {
  Dispatch,
  FC,
  SetStateAction,
  SyntheticEvent,
  useCallback,
  useState,
} from "react";

interface Props {
  label: string;
  slices: EntryListItem[];
  sliceToShow: number;
  setSliceToShow: Dispatch<SetStateAction<number>>;
  defaultOpened?: boolean;
  checkIsHighlighted: (slice: EntryListItem) => boolean;
}

export const SegmentDrawerGroup: FC<Props> = ({
  label,
  slices,
  setSliceToShow,
  defaultOpened,
  checkIsHighlighted,
}) => {
  const [isSegmentOpen, setIsSegmentOpen] = useState(defaultOpened);

  const slicePageRanges = useCallback((ranges: number[][]) => {
    return ranges.map((range) => range.join("-")).join(" | ");
  }, []);

  const handleClick = useCallback(
    (event: SyntheticEvent) => {
      event.stopPropagation();
      setIsSegmentOpen(!isSegmentOpen);
    },
    [isSegmentOpen],
  );

  return (
    <>
      <ListItemButton onClick={handleClick}>
        <ListItemText primary={label} />
        {isSegmentOpen ? <ExpandLess /> : <ExpandMore />}
      </ListItemButton>
      <Collapse in={isSegmentOpen} timeout="auto" unmountOnExit>
        {slices.map(({ packet_type, page_ranges, index, ...rest }, i) => (
          <ListItemButton
            sx={{ padding: "0 0 0 24px", gap: "14px" }}
            selected={checkIsHighlighted({ packet_type, page_ranges, ...rest })}
            key={`${packet_type}-${slicePageRanges(page_ranges)}`}
            onClick={() => setSliceToShow(index)}
          >
            <ListItemIcon sx={{ minWidth: 0 }}>
              <Typography typography={"h6"}>-</Typography>
            </ListItemIcon>
            <ListItemText
              primary={packet_type}
              secondary={slicePageRanges(page_ranges)}
            />
          </ListItemButton>
        ))}
      </Collapse>
    </>
  );
};
