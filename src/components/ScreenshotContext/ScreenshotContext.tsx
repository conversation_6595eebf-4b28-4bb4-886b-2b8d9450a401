import { Box, Button } from "@mui/material";
import html2canvas from "html2canvas";
import {
  FC,
  MouseEventHandler,
  ReactNode,
  createContext,
  useCallback,
  useEffect,
  useReducer,
  useRef,
  useState,
} from "react";
import { StyleCreator, useStyleCreator } from "../../hooks/useStyleCreator.ts";

export interface ScreenshotContext {
  base64: string;
  makeScreenshot: (row_id: string) => Promise<void>;
  dumpImage: () => void;
  rowToUpdate: string;
  ScreenshotContainer: FC;
  isProcessing: boolean;
}

export const ScreenshotContext = createContext<ScreenshotContext>(null);

const styles: StyleCreator<"container"> = () => ({
  container: {
    boxShadow: "inset 0px 0px 11px 5px rgba(255,0,0,1)",
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    zIndex: 1000,
  },
});

type Point = {
  x: number;
  y: number;
};
type Coordinates = {
  start: Point;
  end: Point;
};
type Offset = {
  offsetX: number;
  offsetY: number;
};

type CoordinatesReducerPayload =
  | { action: "set_start_end"; payload: Coordinates }
  | { action: "set_start"; payload: Point }
  | { action: "set_end"; payload: Point }
  | { action: "reset"; payload: Coordinates }
  | { action: "move_figure"; payload: Offset };

const InitialCoordinates: Coordinates = {
  start: {
    x: null,
    y: null,
  },
  end: {
    x: null,
    y: null,
  },
};

const CoordinatesReducer = (
  state: Coordinates,
  { payload, action }: CoordinatesReducerPayload,
) => {
  switch (action) {
    case "set_start_end":
      return payload;
    case "set_start":
      return {
        ...state,
        start: payload,
      };
    case "set_end":
      return {
        ...state,
        end: payload,
      };
    case "reset":
      return InitialCoordinates;
    case "move_figure":
      return {
        start: {
          x: state.start.x + payload.offsetX,
          y: state.start.y + payload.offsetY,
        },
        end: {
          x: state.end.x + payload.offsetX,
          y: state.end.y + payload.offsetY,
        },
      };
    default:
      return state;
  }
};

export const ScreenshotContextProvider: FC<{ children: ReactNode }> = ({
  children,
}) => {
  const c = useStyleCreator(styles);

  const [rowToUpdate, setRowToUpdate] = useState<string | null>(null);
  const [base64Image, setBase64Image] = useState<string | null>(null);
  const [canvasToCrop, setCanvasToCrop] = useState<HTMLCanvasElement | null>(
    null,
  );

  const SelectionCanvas = useRef<HTMLCanvasElement | null>(null);

  const [isProcessing, setIsProcessing] = useState(false);

  const [coordinates, dispatch] = useReducer(
    CoordinatesReducer,
    InitialCoordinates,
  );

  const updScreenshot = useCallback(async () => {
    const pdfContainer = await new Promise<any>((resolve) => {
      const element = document.getElementById("pdfContainer");
      resolve(element);
    });

    if (!pdfContainer) {
      console.error("pdfContainer not found!");
      setIsProcessing(false);
      return;
    }

    try {
      const canvas = await html2canvas(pdfContainer, { scale: 2 });
      setCanvasToCrop(canvas);
      return canvas;
    } catch (error) {
      console.error("Error while capturing screenshot:", error);
    }
  }, []);

  const makeScreenshot = useCallback(
    async (row_id: string) => {
      setRowToUpdate(row_id);
      await updScreenshot();
      setIsProcessing(true);
    },
    [updScreenshot],
  );

  const dumpImage = useCallback(() => {
    setBase64Image(null);
    setRowToUpdate(null);
    setIsProcessing(false);
  }, []);

  const extractScreenshot = useCallback(async () => {
    const updatedCanvasToCrop = await updScreenshot();
    const { start, end } = coordinates;
    const ScreenshotCanvas = document.createElement("canvas");

    if (!updatedCanvasToCrop) {
      console.error("Canvas or container rect not found");
      return;
    }

    ScreenshotCanvas.width = (end.x - start.x) * 2;
    ScreenshotCanvas.height = (end.y - start.y) * 2;

    try {
      const context = ScreenshotCanvas.getContext("2d");

      context.drawImage(
        updatedCanvasToCrop,
        start.x * 2,
        start.y * 2,
        (end.x - start.x) * 2,
        (end.y - start.y) * 2,
        0,
        0,
        (end.x - start.x) * 2,
        (end.y - start.y) * 2,
      );

      setBase64Image(ScreenshotCanvas.toDataURL());
    } catch (err) {
      console.error("Error capturing screen:", err);
    }
    handleCancelEditing();
  }, [canvasToCrop, coordinates, makeScreenshot]);

  useEffect(() => {
    const exitScreenshotMode = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsProcessing(false);
      }
    };

    window.addEventListener("keydown", exitScreenshotMode);
    return () => window.addEventListener("keydown", exitScreenshotMode);
  }, []);

  const onMouseDown = useCallback<MouseEventHandler<HTMLCanvasElement>>(
    (event) => {
      const canvas = SelectionCanvas.current;
      if (!canvas) return;
      const rect = canvas.getBoundingClientRect();
      const x = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
      const y = Math.max(0, Math.min(event.clientY - rect.top, rect.height));

      dispatch({
        action: "set_start_end",
        payload: { start: { x, y }, end: { x, y } },
      });
    },
    [coordinates],
  );

  const onMouseMove = useCallback<MouseEventHandler<HTMLCanvasElement>>(
    (event) => {
      const canvas = SelectionCanvas.current;
      if (!canvas) return;
      const rect = canvas.getBoundingClientRect();

      if (event.buttons === 1) {
        const x = Math.max(0, Math.min(event.clientX - rect.left, rect.width));
        const y = Math.max(0, Math.min(event.clientY - rect.top, rect.height));

        if (!coordinates.start.x && !coordinates.start.y) {
          dispatch({
            action: "set_start_end",
            payload: { start: { x, y }, end: { x, y } },
          });
        }

        dispatch({
          action: "set_end",
          payload: { x, y },
        });
      }
    },
    [coordinates, onMouseDown],
  );

  const handleCancelEditing = () => {
    SelectionCanvas.current = null;
    dispatch({
      action: "reset",
      payload: InitialCoordinates,
    });
    setIsProcessing(false);
  };

  const ScreenshotContainer = () => {
    const { start, end } = coordinates;
    const width = Math.abs(end.x - start.x);
    const height = Math.abs(end.y - start.y);
    const top = Math.max(0, Math.min(start.y, end.y));
    const left = Math.max(0, Math.min(start.x, end.x));

    const shouldRenderSelection = width > 0 && height > 0;

    return (
      isProcessing && (
        <Box sx={c.container}>
          <canvas
            style={{
              position: "absolute",
              width: "100%",
              height: "100%",
            }}
            ref={SelectionCanvas}
            onMouseDown={onMouseDown}
            onMouseMove={onMouseMove}
            onContextMenu={(event) => event.preventDefault()}
          />
          {shouldRenderSelection && (
            <Box
              sx={{
                position: "absolute",
                top,
                left,
                width: `${width}px`,
                height: `${height}px`,
              }}
            >
              <Box
                component="canvas"
                sx={{
                  top,
                  left,
                  width: `${width}px`,
                  height: `${height}px`,
                  border: "2px solid red",
                  boxSizing: "border-box",
                  "&:hover": {
                    cursor: "nwse-resize",
                  },
                }}
                onMouseDown={onMouseDown}
              />
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  position: "absolute",
                  bottom: "-50px",
                  width: "100%",
                }}
              >
                <Button variant="contained" onClick={handleCancelEditing}>
                  X
                </Button>
                <Button variant="contained" onClick={extractScreenshot}>
                  Send
                </Button>
              </Box>
            </Box>
          )}
          <Button
            sx={{
              position: "absolute",
              top: 0,
              right: 0,
              zIndex: 10001,
              cursor: "pointer",
            }}
            variant="contained"
            onClick={handleCancelEditing}
          >
            Close
          </Button>
        </Box>
      )
    );
  };

  return (
    <ScreenshotContext.Provider
      value={{
        base64: base64Image,
        makeScreenshot,
        dumpImage,
        rowToUpdate,
        ScreenshotContainer,
        isProcessing,
      }}
    >
      {children}
    </ScreenshotContext.Provider>
  );
};
