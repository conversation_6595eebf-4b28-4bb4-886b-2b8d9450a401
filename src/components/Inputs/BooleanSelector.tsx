import { styled } from "@mui/material/styles";
import React, { InputHTMLAttributes } from "react";

interface BooleanSelectorProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, "value"> {
  value: string;
  isTreatmentField: boolean;
}

const RadioInput = styled("input")({
  transform: "scale(1.5)",
  marginRight: "12px",
  cursor: "pointer",
  width: "auto !important",
});

const Label = styled("label")({
  display: "flex",
  alignItems: "center",
  fontSize: "14px",
  color: "black",
});

const Container = styled("div")({
  display: "flex",
  alignItems: "start",
  gap: "12px",
  flexWrap: "wrap",
});

const BooleanSelector: React.FC<BooleanSelectorProps> = ({
  value,
  isTreatmentField,
  ...rest
}) => {
  const processedValue = value.toString().toLowerCase();
  let checkedValue = "";

  if (
    processedValue === "y" ||
    processedValue === "yes" ||
    processedValue === "true" ||
    processedValue === "t"
  ) {
    checkedValue = "true";
  } else if (
    processedValue === "n" ||
    processedValue === "no" ||
    processedValue === "false" ||
    processedValue === "f"
  ) {
    checkedValue = "false";
  }

  if (isTreatmentField && processedValue === "") checkedValue = "false";

  const handleOptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (rest.onChange) {
      rest.onChange(event);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      const target = event.target as HTMLInputElement;
      if (rest.onChange) {
        rest.onChange({
          ...event,
          target: {
            ...target,
            value: target.value,
          },
        } as React.ChangeEvent<HTMLInputElement>);
      }
    }
  };

  return (
    <Container>
      <Label tabIndex={1}>
        <RadioInput
          type="radio"
          value="true"
          checked={checkedValue === "true"}
          onChange={handleOptionChange}
          onKeyDown={handleKeyDown}
          {...rest}
        />
        True
      </Label>
      <Label tabIndex={1}>
        <RadioInput
          type="radio"
          value="false"
          checked={checkedValue === "false"}
          onChange={handleOptionChange}
          onKeyDown={handleKeyDown}
          {...rest}
        />
        False
      </Label>
      {!isTreatmentField && (
        <Label tabIndex={1}>
          <RadioInput
            type="radio"
            value={null}
            checked={checkedValue === ""}
            onChange={handleOptionChange}
            onKeyDown={handleKeyDown}
            {...rest}
          />
          N/a
        </Label>
      )}
    </Container>
  );
};

export default BooleanSelector;

// import React, { useEffect, InputHTMLAttributes } from "react";
// import { styled } from "@mui/material/styles";

// interface BooleanSelectorProps
//   extends Omit<InputHTMLAttributes<HTMLInputElement>, "value"> {
//   value: string;
// }

// const RadioInput = styled("input")({
//   transform: "scale(2)",
//   marginRight: "24px",
// });

// const Label = styled("label")({
//   display: "flex",
//   alignItems: "center",
//   fontSize: "14px",
//   color: "black",
// });

// const Container = styled("div")({
//   display: "flex",
//   justifyContent: "start",
//   alignItems: "start",
//   gap: "40px",
//   width: "calc(100%)",
// });

// const BooleanSelector: React.FC<BooleanSelectorProps> = ({
//   value,
//   ...rest
// }) => {
//   const processedValue = value.toString().toLowerCase();
//   let checkedValue = null;

//   console.log({ checkedValue, processedValue });

//   if (
//     processedValue === "y" ||
//     processedValue === "yes" ||
//     processedValue === "true" ||
//     processedValue === "t"
//   ) {
//     checkedValue = "Y";
//   } else if (
//     processedValue === "n" ||
//     processedValue === "no" ||
//     processedValue === "false" ||
//     processedValue === "f"
//   ) {
//     checkedValue = "N";
//   }

//   const handleOptionChange = (event: React.ChangeEvent<HTMLInputElement>) => {
//     if (rest.onChange) {
//       rest.onChange(event);
//     }
//   };

//   const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
//     if (event.key === "Enter") {
//       const target = event.target as HTMLInputElement;
//       if (rest.onChange) {
//         rest.onChange({
//           ...event,
//           target: {
//             ...target,
//             value: target.value,
//           },
//         } as React.ChangeEvent<HTMLInputElement>);
//       }
//     }
//   };

//   return (
//     <Container>
//       <Label tabIndex={1}>
//         <RadioInput
//           type="radio"
//           value="Y"
//           checked={checkedValue === "Y"}
//           onChange={handleOptionChange}
//           onKeyDown={handleKeyDown}
//           {...rest}
//         />
//         Yes
//       </Label>
//       <Label tabIndex={1}>
//         <RadioInput
//           type="radio"
//           value="false"
//           checked={checkedValue === "N"}
//           onChange={handleOptionChange}
//           onKeyDown={handleKeyDown}
//           {...rest}
//         />
//         No
//       </Label>
//       <Label tabIndex={1}>
//         <RadioInput
//           type="radio"
//           value={null}
//           checked={checkedValue === null}
//           onChange={handleOptionChange}
//           onKeyDown={handleKeyDown}
//           {...rest}
//         />
//         N/a
//       </Label>
//     </Container>
//   );
// };

// export default BooleanSelector;
