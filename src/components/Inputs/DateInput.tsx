import React, { InputHTMLAttributes } from "react";
import { PatternFormat } from "react-number-format";

interface DateInputProps extends InputHTMLAttributes<HTMLInputElement> {
  value: string;
  onChange: (event: { target: { value: string } }) => void;
  style?: React.CSSProperties;
}

const DateInput: React.FC<DateInputProps> = ({ value, onChange, style }) => {
  return (
    <PatternFormat
      value={value}
      onValueChange={(values) => {
        if (onChange) {
          onChange({ target: { value: values.value } });
        }
      }}
      format="####-##-##"
      placeholder="Enter date as YYYY-MM-DD"
      tabIndex={1}
      mask="_"
      style={style}
    />
  );
};

export default DateInput;
