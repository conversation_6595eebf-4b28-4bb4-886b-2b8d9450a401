import React, { InputHTMLAttributes, useEffect, useState } from "react";

interface SsnInputProps extends InputHTMLAttributes<HTMLInputElement> {
  value: string;
  onChange: (event: { target: { value: string } }) => void;
  style?: React.CSSProperties;
}

const SsnInput: React.FC<SsnInputProps> = ({ value, onChange, style }) => {
  const [displayValue, setDisplayValue] = useState("");

  useEffect(() => {
    setDisplayValue(applyMask(value));
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value.replace(/[^a-zA-Z0-9]/g, "");
    if (rawValue.length <= 9) {
      const formattedValue = applyMask(rawValue);
      setDisplayValue(formattedValue);
      if (onChange) {
        onChange({ target: { value: rawValue } });
      }
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    const pasteData = e.clipboardData.getData("text");
    const cleanedPasteData = pasteData.replace(/[^a-zA-Z0-9]/g, "").slice(0, 9);
    const currentValue = e.currentTarget.value.replace(/[^a-zA-Z0-9]/g, "");

    const newValue = (currentValue + cleanedPasteData).slice(0, 9);

    e.preventDefault();
    if (onChange) {
      onChange({ target: { value: newValue } });
    }
    setDisplayValue(applyMask(newValue));
  };

  const applyMask = (value: string) => {
    const cleaned = value.replace(/[^a-zA-Z0-9]/g, "");
    const match = cleaned.match(
      /^([a-zA-Z0-9]{0,3})([a-zA-Z0-9]{0,2})([a-zA-Z0-9]{0,4})$/,
    );

    if (match) {
      const maskedValue = `${match[1]}${match[2] ? "-" : ""}${match[2]}${
        match[3] ? "-" : ""
      }${match[3]}`;
      return maskedValue;
    }
    return value;
  };

  return (
    <input
      value={displayValue}
      onChange={handleChange}
      onPaste={handlePaste}
      placeholder="###-##-####"
      style={{ ...style }}
      tabIndex={1}
      type="text"
      maxLength={11}
      autoComplete={"off"}
    />
  );
};

export default SsnInput;
