import React, { InputHTMLAttributes } from "react";
import { PatternFormat } from "react-number-format";

interface PhoneFaxInputProps extends InputHTMLAttributes<HTMLInputElement> {
  value?: string;
  onChange?: (event: { target: { value: string } }) => void;
  style?: React.CSSProperties;
}

const PhoneFaxInput: React.FC<PhoneFaxInputProps> = ({
  value,
  onChange,
  style,
}) => {
  return (
    <PatternFormat
      value={value}
      onValueChange={(values) => {
        if (onChange) {
          onChange({ target: { value: values.value } });
        }
      }}
      format="##########"
      placeholder="Enter phone/fax number"
      mask="_"
      style={{ ...style }}
      tabIndex={1}
    />
  );
};

export default PhoneFaxInput;
