import React, { InputHTMLAttributes } from "react";

interface StateInputProps extends InputHTMLAttributes<HTMLInputElement> {
  value: string;
  onChange: (event: { target: { value: string } }) => void;
  style: React.CSSProperties;
}

const StateInput: React.FC<StateInputProps> = ({ value, onChange, style }) => {
  return (
    <input
      value={value || ""}
      onChange={onChange}
      placeholder="Enter state (e.g., CA, AZ)"
      style={{ ...style }}
      maxLength={2}
      tabIndex={1}
      autoComplete={"off"}
    />
  );
};

export default StateInput;
