import React, { InputHTMLAttributes, useMemo } from "react";
import { PatternFormat } from "react-number-format";

interface ZipCodeInputProps extends InputHTMLAttributes<HTMLInputElement> {
  value: string;
  onChange: (event: { target: { value: string } }) => void;
  style?: React.CSSProperties;
  ssn?: boolean;
}

const ZipCodeInput: React.FC<ZipCodeInputProps> = ({
  value,
  onChange,
  style,
}) => {
  const preparedValue = useMemo(() => {
    if (!value) return "";

    return value.split("-").join("").split(" ").filter(Boolean).join("");
  }, [value]);

  return (
    <PatternFormat
      value={preparedValue}
      onValueChange={(values) => {
        if (onChange) {
          onChange({ target: { value: values.value } });
        }
      }}
      format={"#########"}
      placeholder={"Enter ZIP code"}
      style={{ ...style }}
      valueIsNumericString
      tabIndex={1}
      type="text"
    />
  );
};

export default ZipCodeInput;
