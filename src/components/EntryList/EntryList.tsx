import DeleteIcon from "@mui/icons-material/Delete";
import DeleteForeverIcon from "@mui/icons-material/DeleteForever";
import EditIcon from "@mui/icons-material/Edit";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Paper,
  TextField,
  Typography,
} from "@mui/material";
import { FC, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateSlices } from "../../store/slices/metadata/metadata.slice.ts";
import { formatPageRanges } from "../Editor/helpers.ts";

interface EntryListProps {
  numPages: number | string;
}

export const EntryList: FC<EntryListProps> = ({ numPages }) => {
  const dispatch = useDispatch();
  const slices = useSelector((state: any) => state.data.slices);

  const [editOpen, setEditOpen] = useState(false);
  const [newPageRanges, setNewPageRanges] = useState<number[][]>([]);
  const [pageErrors, setPageErrors] = useState<string[]>([]);
  const [editErrorOverlapping, setEditErrorOverlapping] = useState<string>("");
  const [activeEntry, setActiveEntry] = useState<EntryListItem | null>(null);

  const updateSlicesAction = (slices: EntryListItem[]) => {
    dispatch(updateSlices(slices));
  };

  const handleDelete = (startPage: number, endPage: number) => {
    const updatedSlices = slices.filter(
      (entry: EntryListItem) =>
        entry.page_ranges[0][0] !== startPage ||
        entry.page_ranges[0][1] !== endPage,
    );
    updateSlicesAction(updatedSlices);
  };

  const handleEditOpen = (entry: EntryListItem) => {
    setPageErrors(new Array(entry.page_ranges.length).fill(""));
    setActiveEntry(entry);
    setEditOpen(true);
    setNewPageRanges([...entry.page_ranges]);
  };

  const deletePageRange = (index: number) => {
    const newRanges = [...newPageRanges];
    newRanges.splice(index, 1);

    const newErrors = [...pageErrors];
    newErrors.splice(index, 1);

    setNewPageRanges(newRanges);
    setPageErrors(newErrors);
  };

  const handleEditClose = () => {
    setEditOpen(false);
    setNewPageRanges([]);
    setPageErrors([]);
    setEditErrorOverlapping("");
  };

  const handlePageRangeChange = (
    index: number,
    field: "start" | "end",
    value: string,
  ) => {
    const intValue = parseInt(value);
    const newRanges = [...newPageRanges].map((range) => [...range]);
    const newErrors = [...pageErrors];

    if (field === "start") {
      newRanges[index][0] = intValue;
    } else {
      newRanges[index][1] = intValue;
    }

    if (
      isNaN(newRanges[index][0]) ||
      newRanges[index][0] < 1 ||
      newRanges[index][0] > +numPages
    ) {
      newErrors[index] =
        `Start page must be a number between 1 and ${numPages}`;
    } else if (newRanges[index][0] >= newRanges[index][1]) {
      newErrors[index] =
        "Start page cannot be greater than or equal to end page";
    } else {
      newErrors[index] = "";
    }

    if (
      isNaN(newRanges[index][1]) ||
      newRanges[index][1] < 1 ||
      newRanges[index][1] > +numPages
    ) {
      newErrors[index] = "End page must be a number between 1 and " + numPages;
    } else if (newRanges[index][1] < newRanges[index][0]) {
      newErrors[index] = "End page cannot be less than start page";
    } else {
      newErrors[index] = "";
    }

    setNewPageRanges(newRanges);
    setPageErrors(newErrors);
  };

  const addNewPageRange = () => {
    if (
      newPageRanges.some(
        (range) => range.length !== 2 || range.some((val) => val <= 0),
      )
    ) {
      setEditErrorOverlapping(
        "Please enter valid page ranges before adding a new one.",
      );
      return;
    }
    setNewPageRanges([...newPageRanges, [0, 0]]);
    setPageErrors([...pageErrors, ""]);
    setEditErrorOverlapping("");
  };

  const removePageRanges = (
    activePageRanges: number[][],
    allPageRanges: number[][],
  ): number[][] => {
    const set1 = new Set(activePageRanges.map((item) => JSON.stringify(item)));
    const set2 = new Set(allPageRanges.map((item) => JSON.stringify(item)));

    const difference = Array.from(set2).filter((item) => !set1.has(item));

    const result = difference.map((item) => JSON.parse(item));

    return result;
  };

  const getAllPageRanges = (
    activePageRanges: number[][],
    allPageRanges: number[][],
  ): number[][] => {
    return removePageRanges(activePageRanges, allPageRanges);
  };

  const isRangeOverlappingWithRange = (
    newRange: number[],
    existingRange: number[],
  ): boolean => {
    const [newStart, newEnd] = newRange;
    const [start, end] = existingRange;

    return (
      (newStart <= end && newStart >= start) ||
      (newEnd <= end && newEnd >= start) ||
      (newStart <= start && newEnd >= end)
    );
  };

  const getOverlappingRanges = (
    newRange: number[],
    allPageRanges: number[][],
  ): string[] => {
    const overlappingRanges: string[] = [];

    for (let i = 0; i < allPageRanges.length; i++) {
      if (isRangeOverlappingWithRange(newRange, allPageRanges[i])) {
        const [start, end] = allPageRanges[i];
        overlappingRanges.push(`[${start}-${end}]`);
      }
    }

    return overlappingRanges;
  };

  const cantAddNewRange = (newRange: number[], ranges: number[][]): boolean => {
    const [checkStart, checkEnd] = newRange;
    return !ranges.some(([start, end]) => {
      const check = checkStart >= start && checkEnd <= end;
      return check;
    });
  };

  const checkActiveEntryOverlaps = (newRange: number[]): string[] => {
    const overlappingRanges: string[] = [];

    if (newPageRanges && newPageRanges.length > 0) {
      const countOccurrences = newPageRanges.filter(
        ([start, end]) => start === newRange[0] && end === newRange[1],
      ).length;

      const newPageRangesFiltered = getAllPageRanges(
        activeEntry.page_ranges,
        newPageRanges,
      );

      if (countOccurrences > 1) {
        overlappingRanges.push(`[${newRange[0]}-${newRange[1]}]`);
      } else if (cantAddNewRange(newRange, newPageRanges)) {
        overlappingRanges.push(`[${newRange[0]}-${newRange[1]}]`);
      }
      // else if (
      //   newPageRanges.length >= activeEntry.page_ranges.length &&
      //   (newPageRangesFiltered.some((item) => {
      //     for (let i = 0; i < newPageRangesFiltered.length; i++) {
      //       return item.toString() === newPageRangesFiltered[i].toString();
      //     }
      //   }) ||
      //     newPageRangesFiltered.length > 0)
      // ) {
      //   for (let i = 0; i < newPageRangesFiltered.length; i++) {
      //     overlappingRanges = overlappingRanges.concat(
      //       getOverlappingRanges(
      //         newPageRangesFiltered[i],
      //         activeEntry.page_ranges
      //       )
      //     );
      //   }
      // }
    }

    return overlappingRanges;
  };

  const isRangeOverlapping = (ranges: number[][]): string[] => {
    const slicesPageRanges: number[][] = slices.flatMap(
      (slice: { page_ranges: number[][] }) => slice.page_ranges,
    );

    const allPageRanges = getAllPageRanges(
      activeEntry.page_ranges,
      slicesPageRanges,
    );
    let overlappingRanges: string[] = [];

    for (let i = 0; i < ranges.length; i++) {
      overlappingRanges = overlappingRanges.concat(
        getOverlappingRanges(ranges[i], allPageRanges),
      );
      overlappingRanges = overlappingRanges.concat(
        checkActiveEntryOverlaps(ranges[i]),
      );
    }

    return Array.from(new Set(overlappingRanges));
  };

  const handleSavePageRanges = () => {
    if (pageErrors.some((error) => error !== "")) {
      return;
    }

    if (newPageRanges.length === 0) {
      handleDelete(
        activeEntry.page_ranges[0][0],
        activeEntry.page_ranges[0][1],
      );
      handleEditClose();
      return;
    }

    const overlappingRanges = isRangeOverlapping(newPageRanges);
    if (overlappingRanges.length > 0) {
      setEditErrorOverlapping(
        `Overlapping range(s): ${overlappingRanges.join(", ")}`,
      );
      return;
    }

    const updatedEntry = {
      ...activeEntry,
      page_ranges: newPageRanges,
    };

    const updatedSlices = slices.map((slice: EntryListItem) =>
      slice === activeEntry ? updatedEntry : slice,
    );

    const sortedSlices = updatedSlices.sort(
      (a: { page_ranges: number[][] }, b: { page_ranges: number[][] }) =>
        a.page_ranges[0][0] - b.page_ranges[0][0],
    );

    setNewPageRanges(sortedSlices);
    updateSlicesAction(sortedSlices);

    handleEditClose();
  };

  const isSaveButtonDisabled = () => {
    return (
      pageErrors.some((error) => error !== "") ||
      newPageRanges.some(
        (range) => range.length !== 2 || range.some((val) => val <= 0),
      )
    );
  };

  return slices && slices.length > 0 ? (
    <>
      <Dialog open={editOpen} onClose={handleEditClose}>
        <DialogTitle>Edit Page Ranges</DialogTitle>
        <DialogContent
          sx={{
            maxHeight: "400px",
            overflow: "auto",
            gap: 2,
          }}
        >
          {newPageRanges.map((pageRange, index) => (
            <Box
              key={index}
              sx={{
                display: "flex",
                flexDirection: "row",
                flexWrap: "wrap",
                justifyContent: "space-evenly",
                alignItems: "start",
                gap: "6px",
                marginTop: "6px",
              }}
            >
              <TextField
                label="Start Page"
                type="number"
                value={pageRange[0] || ""}
                onChange={(e) =>
                  handlePageRangeChange(index, "start", e.target.value)
                }
                error={pageErrors[index] !== ""}
                helperText={pageErrors[index]}
                sx={{ width: 150 }}
                InputProps={{ inputProps: { min: 1 } }}
              />
              <TextField
                label="End Page"
                type="number"
                value={pageRange[1] || ""}
                onChange={(e) =>
                  handlePageRangeChange(index, "end", e.target.value)
                }
                error={pageErrors[index] !== ""}
                helperText={pageErrors[index]}
                sx={{ width: 150 }}
                InputProps={{ inputProps: { min: 1 } }}
              />
              <IconButton
                edge="end"
                aria-label="delete"
                onClick={() => deletePageRange(index)}
                sx={{
                  width: 30,
                  height: 30,
                  marginTop: "16px",
                  color: pageErrors[index] ? "red" : "inherit",
                }}
              >
                <DeleteForeverIcon />
              </IconButton>
            </Box>
          ))}
          <Button
            onClick={addNewPageRange}
            sx={{
              marginTop: "10px",
              width: "100%",
            }}
            disabled={newPageRanges.some(
              (range) => range.length !== 2 || range.some((val) => val <= 0),
            )}
          >
            Add new page range
          </Button>
          <Box sx={{ paddingTop: "6px" }} color={"red"} textAlign={"start"}>
            {editErrorOverlapping !== "" ? (
              <Typography sx={{ fontSize: 10, color: "purple" }}>
                {editErrorOverlapping}
              </Typography>
            ) : null}
          </Box>
        </DialogContent>
        <DialogActions
          sx={{
            display: "flex",
            width: "100%",
            justifyContent: "space-between",
          }}
        >
          <Button onClick={handleEditClose} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleSavePageRanges}
            color="primary"
            disabled={isSaveButtonDisabled()}
          >
            Save page ranges
          </Button>
        </DialogActions>
      </Dialog>

      <Box
        sx={{
          display: "flex",
          gap: 2,
          overflowX: "auto",
          flexDirection: "column",
          width: "100%",
        }}
      >
        {slices.map((slice: EntryListItem) => (
          <Paper
            key={`slice-${slice.page_ranges?.toString() || "slice"}`}
            sx={{
              minWidth: "300px",
              padding: 2,
              backgroundColor: "#333",
              flex: "0 0 auto",
              position: "relative",
            }}
          >
            <List
              dense
              sx={{
                color: "#fff",
                gap: 2,
                display: "flex",
                flexDirection: "column",
                maxHeight: "500px",
                minWidth: "100%",
                justifyContent: "start",
                overflow: "auto",
              }}
            >
              <ListItem
                secondaryAction={
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <IconButton
                      edge="end"
                      aria-label="add"
                      onClick={() => handleEditOpen(slice)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      edge="end"
                      aria-label="delete"
                      onClick={() => {
                        handleDelete(
                          slice.page_ranges[0][0],
                          slice.page_ranges[0][1],
                        );
                      }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                }
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 2,
                  border: "1px solid white",
                  borderRadius: 10,
                  alignItems: "flex-start",
                }}
              >
                <ListItemText
                  primary={`Document type: ${slice.packet_type}`}
                  secondary={`Pages: ${
                    formatPageRanges(slice.page_ranges) || " "
                  }, Overall Conf: ${
                    Math.round(slice.overall_confidence * 100) / 100 || 0
                  }, Classification Conf: ${
                    Math.round(slice.classification_confidence * 100) / 100 || 0
                  }`}
                  sx={{
                    color: "white",
                    textAlign: "start",
                    width: "90%",
                  }}
                  secondaryTypographyProps={{
                    color: "white",
                    textAlign: "start",
                  }}
                />
              </ListItem>
            </List>
          </Paper>
        ))}
      </Box>
    </>
  ) : (
    <Typography sx={{ width: "100%", textAlign: "center" }}>
      No predictions
    </Typography>
  );
};
