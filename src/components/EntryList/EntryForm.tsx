import ClearIcon from "@mui/icons-material/Clear";
import {
  Autocomplete,
  Box,
  Button,
  FormControl,
  Paper,
  Popper,
  TextField,
  styled,
} from "@mui/material";
import React, { FC, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateSlices } from "../../store/slices/metadata/metadata.slice.ts";

const types = [
  "Other",
  "RFA",
  "Medical Records",
  "Misc Correspondence",
  "Legal Correspondence",
  "State forms",
  "Ratings",
  "Subpoena",
  "EOB/EOR",
  "IMR/IME/QME",
  "Supplemental/Work Status",
  "Physician Bill (HCFA)",
  "Hospital Bill (UB)",
  "Translation Bills",
  "Determination - Med Auth",
  "Vocational Evaluations",
  "Case Management Notes",
  "Fax",
  "Injury/Illness/FROI",
];

interface EntryFormProps {
  numPages: any;
  loading: boolean;
}

export const EntryForm: FC<EntryFormProps> = ({ loading, numPages }) => {
  const dispatch = useDispatch();
  const slices = useSelector((state: any) => state.data.slices);
  const updateSlicesAction = (slices: EntryListItem[]) => {
    dispatch(updateSlices(slices));
  };

  const [startPage, setStartPage] = useState<string>("");
  const [endPage, setEndPage] = useState<string>("");
  const [startError, setStartError] = useState<string>("");
  const [endError, setEndError] = useState<string>("");
  const [errorOverlaping, setErrorOverlaping] = useState<string>("");

  const [documentType, setDocumentType] = useState("");
  const [submitAttempted, setSubmitAttempted] = useState(false);

  const addEntry = async (event: any) => {
    if (
      !documentType ||
      !endPage ||
      !startPage ||
      startError !== "" ||
      endError !== ""
    ) {
      setSubmitAttempted(true);
      return;
    }

    if (isRangeOverlapping(startPage, endPage, slices)) {
      setErrorOverlaping("Overlapping range. Enter a different range.");
      return;
    }

    const newEntry = {
      page_ranges: [[+startPage, +endPage]],
      packet_type: documentType,
      classification_confidence: 1,
      overall_confidence: 1,
      patient_name: "Unset",
    };

    event.stopPropagation();
    event.preventDefault();
    updateSlicesAction([...slices, newEntry]);

    setStartPage("");
    setEndPage("");
    setDocumentType("");
  };

  useEffect(() => {
    if (documentType !== "" && endPage !== "" && startPage)
      setSubmitAttempted(false);
  }, [documentType, endPage, startPage]);

  const handleStartPageChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const value = event.target.value.trim();
    setStartPage(value);
    setErrorOverlaping("");

    if (value === "") {
      setStartError("Page could not be empty");
    } else {
      const intValue = Number(value);
      if (isNaN(intValue)) {
        setStartError("Start page must be a number");
      } else if (intValue < 1) {
        setStartError("Page cannot be less than 1");
      } else if (intValue > numPages) {
        setStartError(`End page cannot exceed ${numPages}`);
      } else if (intValue > Number(endPage)) {
        if (!(endPage !== ""))
          setStartError("Start page cannot be greater than end page");
        setStartError("");
      } else {
        setStartError("");
      }
    }
  };

  const handleEndPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value.trim();
    setEndPage(value);
    setErrorOverlaping("");

    if (value === "") {
      setEndError("Page could not be empty");
    } else {
      const intValue = Number(value);
      if (isNaN(intValue)) {
        setEndError("End page must be a number");
      } else if (intValue < Number(startPage)) {
        setEndError("End page cannot be lower than start page");
      } else if (intValue > numPages) {
        setEndError(`End page cannot exceed ${numPages}`);
      } else if (intValue < 1) {
        setStartError("Page cannot be less than 1");
      } else {
        setEndError("");
      }
    }
  };

  const isRangeOverlapping = (newStart, newEnd, entries) => {
    const newStartInt = parseInt(newStart);
    const newEndInt = parseInt(newEnd);

    return entries.some((entry: EntryListItem) => {
      const startInt = entry.page_ranges[0][0];
      const endInt = entry.page_ranges[0][1];
      return newStartInt <= endInt && newEndInt >= startInt;
    });
  };

  const handleChangeDocType = (type: string) => {
    setDocumentType(type);
  };

  useEffect(() => {
    setStartPage("");
    setEndPage("");
    setDocumentType("");
  }, [loading]);

  const StyledPopper = styled(Popper)({
    width: "200px !important",
  });

  const filterOptions = (
    options: string[],
    { inputValue }: { inputValue: string },
  ) => {
    return options.filter((option) => {
      const substrings = option.split(" ");
      return substrings.some((substring) =>
        substring.toLowerCase().startsWith(inputValue.toLowerCase()),
      );
    });
  };

  return (
    <>
      <Paper sx={{ padding: 2, color: "black" }}>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            flexWrap: "wrap",
            justifyContent: "space-evenly",
            alignItems: "center",
            gap: "6px",
          }}
        >
          <TextField
            label="Start Page"
            type="number"
            value={startPage || ""}
            onChange={handleStartPageChange}
            error={startError !== ""}
            disabled={!numPages || numPages <= 0}
            sx={{ width: 150 }}
          />
          <TextField
            label="End Page"
            type="number"
            value={endPage || ""}
            onChange={handleEndPageChange}
            error={endError !== ""}
            disabled={!numPages || numPages <= 0}
            sx={{ width: 150 }}
          />
          <FormControl margin="normal" style={{ margin: 0 }}>
            <Autocomplete
              id="document-type-autocomplete"
              options={types}
              value={documentType}
              onChange={(event, newValue) => handleChangeDocType(newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Pick Type"
                  variant="outlined"
                  style={{ fontSize: 16, width: 150 }}
                />
              )}
              filterOptions={filterOptions}
              PopperComponent={(props) => <StyledPopper {...props} />}
              clearIcon={documentType ? <ClearIcon /> : null}
              sx={{
                "& .MuiAutocomplete-listbox": {
                  maxHeight: 200,
                },
              }}
            />
          </FormControl>
          <Button
            variant="contained"
            color="primary"
            type="submit"
            onClick={(e) => addEntry(e)}
            sx={{ height: 56, width: 150 }}
            disabled={submitAttempted || errorOverlaping !== ""}
          >
            {errorOverlaping !== "" ? (
              <Box sx={{ fontSize: 10, color: "purple" }}>
                {errorOverlaping}
              </Box>
            ) : submitAttempted || startError !== "" || endError !== "" ? (
              <Box sx={{ fontSize: 10, color: "purple" }}>Fill all fields</Box>
            ) : (
              <>Add</>
            )}
          </Button>
        </Box>
        <Box sx={{ paddingTop: "6px" }} color={"red"} textAlign={"center"}>
          {startError !== "" || endError !== "" ? startError || endError : null}
        </Box>
      </Paper>
    </>
  );
};
