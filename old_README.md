# dx_record_ranger_ml_pipeline
modules: downloader, classifier, splitter, metadata_extractor, uploader

### Requiremenrs
- docker or conda for environment managment
- GPU-powered host

### For gpu capability support inside docker
```
sudo apt-get install nvidia-container-toolkit
sudo systemctl restart docker
```

# Run pipeline inside environments using conda
### Install conda for easy environment management
###### details: https://docs.anaconda.com/free/miniconda/index.html
```
mkdir -p ~/miniconda3
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O ~/miniconda3/miniconda.sh
bash ~/miniconda3/miniconda.sh -b -u -p ~/miniconda3
rm -rf ~/miniconda3/miniconda.sh
source ~/.bashrc
```


### Clone repo
```
<NAME_EMAIL>:DeepXHub/dx_record_ranger_ml_pipeline.git
cd dx_record_ranger_ml_pipeline
```
###### Optional switch to development branch:
```
git checkout development
```

### Install dvc (for pulling models)
```
python3 -m pip install dvc dvc[gdrive]
```
###### Download models for classifier and metadata_extractor. At first run login is needed, if run on remote server, can be solved with 8080 port redirection from your machine to remote machine:
```
python3 -m dvc pull 
```

### Run database, rabbitmq, minio, sftp
```
cd containers
cp docker-compose.yml.sample docker-compose.yml
```
###### Edit docker-compose.yml as needed and run:
```
ne docker-compose.yml
docker compose up -d
```

###### Change owner to prevent permission mismatch when pipeline will operate:
```
chown -R <your_user>:<your_group> sftp # example: chown -R pipeline:pipeline sftp
```
###### Now you shouild be able to join your 2 sftp servers from remote host


### Initialize database
```
conda create -n atom_alembic python==3.10
conda activate atom_alembic
python3 -m pip install alembic psycopg2-binary==2.9.9 SQLAlchemy-Utils==0.41.1 SQLAlchemy==2.0.25
python3 -m alembic init alembic
```
###### set alembic.ini:
```
sqlalchemy.url = postgresql+psycopg2://user:password@localhost/dbname
```
###### Then, you need to configure the env.py file inside the alembic folder to include your models for Alembic to detect them. Import your models at the top of alembic/env.py and then modify the run_migrations_offline() and run_migrations_online() functions by adding a line to include the model's MetaData object:
```
from models import models
target_metadata = models.Base.metadata
```
###### Perform migration:
```
python3 -m alembic revision --autogenerate -m "Initial migration"
python3 -m alembic upgrade head
```

### Edit config.yml at the project's root as needed
```
cd ..
cp config.yml.sample config.yml
ne config.yml
```

### Create conda environments
```
conda create -n atom_dow python==3.10
conda create -n atom_cls python==3.9
conda create -n atom_spl python==3.10
conda create -n atom_ext python==3.9
conda create -n atom_upl python==3.10
```


### Run Downloader
```
screen -S dow
conda activate atom_dow
python3 -m pip install -r requirements.txt
python3 downloader.py
```

### Run Classifier
```
screen -S cls
conda activate atom_cls
python3 -m pip install -r requirements.txt
python3 downloader.py
```

### Run Splitter
```
screen -S spl
conda activate atom_spl
python3 -m pip install -r requirements.txt
python3 downloader.py
```

### Run Metadata extractor
```
screen -S ext
conda activate atom_ext
python3 -m pip install -r requirements.txt
python3 -m spacy download en_core_web_sm
python3 metadata_extractor.py.py
```

### Run Uploader
```
screen -S spl
conda activate atom_spl
python3 -m pip install -r requirements.txt
python3 uploader.py
```


# How to use dvc
<details open>
  <summary>Classifier info</summary>
  
  ### DVC
  
  Make sure that dvc and dvc-gdrive are installed in the environment  
  Download files (weights, database) using dvc:  
  
  ```
  dvc pull
  ```

  Details can be found [here](https://docs.google.com/document/d/1mpNVX1yFfdCCE5sGSuego4PGioTAO9iDBoXKY1juS_A/edit?usp=sharing)

  </details>




