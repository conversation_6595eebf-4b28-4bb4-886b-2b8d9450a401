name: Update Trivy Cache

on:
  schedule:
    - cron: '15 0 * * 1'  # updates every Monday at 00:15 UTC
  workflow_dispatch:  # allow manual update
  push:
    branches:
      - main
      - stage
      - dev
      - feature/update-python-version
jobs:
  update-trivy-db:
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/stage' || github.ref == 'refs/heads/dev'
    runs-on: ubuntu-latest
    timeout-minutes: 5

    steps:
      - name: Get current date
        id: date
        run: echo "week=$(date +'%Y-%U')" >> $GITHUB_OUTPUT

      - name: Setup oras
        uses: oras-project/setup-oras@v1

      - name: Download and extract the vulnerability DB
        run: |
          mkdir -p $GITHUB_WORKSPACE/.cache/trivy/db
          oras pull ghcr.io/aquasecurity/trivy-db:2
          tar -xzf db.tar.gz -C $GITHUB_WORKSPACE/.cache/trivy/db
          rm db.tar.gz

      - name: Download and extract the Java DB
        run: |
          mkdir -p $GITHUB_WORKSPACE/.cache/trivy/java-db
          oras pull ghcr.io/aquasecurity/trivy-java-db:1
          tar -xzf javadb.tar.gz -C $GITHUB_WORKSPACE/.cache/trivy/java-db
          rm javadb.tar.gz

      - name: Save Trivy DBs to cache
        uses: actions/cache/save@v4
        with:
          path: ${{ github.workspace }}/.cache/trivy
          key: cache-trivy-${{ steps.date.outputs.week }}
