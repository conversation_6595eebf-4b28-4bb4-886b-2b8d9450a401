name: Build and Push Images to AWS ECR

on:
  push:
    branches: [ none ]
  workflow_dispatch:

env:
  AWS_REGION: us-east-2  # Change to your AWS region
  ECR_REGISTRY: "112623991000"
  PADDLE_BASE_IMAGE: docker.io/paddlepaddle/paddle:2.6.1-gpu-cuda11.7-cudnn8.4-trt8.4@sha256:3c91a0a7aac1bf56eb98c2c4b6c64c055fde8b7b4cfdfb95b7d2ddc786d859bf

jobs:
  # Build classifier (PaddlePaddle-based)
  build-classifier:
    name: Build Classifier
    uses: ./.github/workflows/build-paddle-image.yml
    with:
      image-name: classifier
      directory: classifier
    secrets: inherit

  # Build metadata-extractor (PaddlePaddle-based)
  build-metadata-extractor:
    name: Build Metadata Extractor
    uses: ./.github/workflows/build-paddle-image.yml
    with:
      image-name: metadata-extractor
      directory: metadata_extractor
    secrets: inherit

  # Build downloader
  build-downloader:
    name: Build Downloader
    uses: ./.github/workflows/build-image.yml
    with:
      image-name: downloader
      directory: downloader
    secrets: inherit

  # Build uploader
  build-uploader:
    name: Build Uploader
    uses: ./.github/workflows/build-image.yml
    with:
      image-name: uploader
      directory: uploader
    secrets: inherit

  # Build splitter
  build-splitter:
    name: Build Splitter
    uses: ./.github/workflows/build-image.yml
    with:
      image-name: splitter
      directory: splitter
    secrets: inherit

  # Build validate-route
  build-validate-route:
    name: Build Validate and Route
    uses: ./.github/workflows/build-image.yml
    with:
      image-name: validate-route
      directory: validate_and_route
    secrets: inherit

  # Build metadata-post-processor
  build-metadata-post-processor:
    name: Build Metadata Post-Processor
    uses: ./.github/workflows/build-image.yml
    with:
      image-name: metadata-post-processor
      directory: metadata_postprocessor
    secrets: inherit

  # Build qa-post-processor
  build-qa-post-processor:
    name: Build QA Post-Processor
    uses: ./.github/workflows/build-image.yml
    with:
      image-name: qa-post-processor
      directory: qa_post_processor
    secrets: inherit

  # Build data-cleaner
  build-data-cleaner:
    name: Build Data Cleaner
    uses: ./.github/workflows/build-image.yml
    with:
      image-name: data-cleaner
      directory: data_cleaner
    secrets: inherit

  # Build select-and-populate
  build-select-and-populate:
    name: Build Select and Populate
    uses: ./.github/workflows/build-image.yml
    with:
      image-name: select-and-populate
      directory: select_and_populate
    secrets: inherit 