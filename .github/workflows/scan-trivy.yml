name: Run Trivy scanner

on:
  workflow_dispatch:
  push:
    branches:
      - 'main'

jobs:
  scan:
    name: Trivy scan
    runs-on: ubuntu-22.04
    timeout-minutes: 10

    steps:
      - name: Get current date
        id: date
        run: echo "week=$(date +'%Y-%U')" >> $GITHUB_ENV

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Trivy
        run: |
          sudo apt-get update
          sudo apt-get install -y curl gnupg
          curl -fsSL https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo tee /etc/apt/trusted.gpg.d/trivy.asc
          echo "deb https://aquasecurity.github.io/trivy-repo/deb jammy main" | sudo tee -a /etc/apt/sources.list.d/trivy.list
          sudo apt-get update
          sudo apt-get install -y trivy
          trivy --version  # Verify installation

      - name: Restore Trivy DB cache
        uses: actions/cache@v4
        with:
          path: ${{ github.workspace }}/.cache/trivy
          key: cache-trivy-${{ env.week }}
          restore-keys: |
            cache-trivy-

      - name: Run Trivy vulnerability scanner in repo mode
        run: |
          trivy repo --severity CRITICAL --exit-code 1 --output trivy-report-repo.txt .
          trivy repo --severity HIGH,MEDIUM --exit-code 0 --output trivy-report-warnings.txt .
          echo "------ Trivy CRITICAL Scan Output ------"
          cat trivy-report-repo.txt || echo "Trivy CRITICAL report was not generated!"
          echo "------ Trivy HIGH/MEDIUM Scan Output ------"
          cat trivy-report-warnings.txt || echo "Trivy HIGH/MEDIUM report was not generated!"
        env:
          TRIVY_DEBUG: true
          TRIVY_SKIP_DB_UPDATE: false
          TRIVY_SKIP_JAVA_DB_UPDATE: false

      - name: Check if report exists
        run: ls -lah trivy-report-repo.txt || echo "Trivy report not found"

      - name: Upload Trivy Reports (Always)
        uses: actions/upload-artifact@v4
        with:
          name: trivy-reports
          path: trivy-report-*.txt
          retention-days: 30