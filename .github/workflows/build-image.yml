name: Build Standard Docker Image

on:
  workflow_call:
    inputs:
      image-name:
        required: true
        type: string
        description: "Name of the Docker image"
      directory:
        required: true
        type: string
        description: "Directory containing the Dockerfile"

env:
  AWS_REGION: us-east-2

jobs:
  build:
    runs-on:
      - codebuild-github-runner-ml-pipeline-${{ github.run_id }}-${{ github.run_attempt }}
    timeout-minutes: 60
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: ${{ env.AWS_REGION }}
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}

      - name: Display current AWS identity
        run: |
          aws sts get-caller-identity

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
        with:
          registries: "112623991000"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ inputs.image-name }}-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-${{ inputs.image-name }}-

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: ./${{ inputs.directory }}
          push: ${{ github.event_name != 'pull_request' }}
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ inputs.image-name }}:latest
            ${{ steps.login-ecr.outputs.registry }}/${{ inputs.image-name }}:${{ github.sha }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new
          build-args: --progress=plain

      # Move cache to avoid growth
      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

