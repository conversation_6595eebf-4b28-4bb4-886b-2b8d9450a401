[tool:pytest]
# Pytest configuration for multi-tenant ML pipeline testing

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add project root to Python path
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=.
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-exclude=tests/*
    --cov-exclude=*/test_*
    --cov-exclude=conftest.py
    -p no:warnings

# Coverage settings
[coverage:run]
source = .
omit = 
    tests/*
    */test_*
    conftest.py
    setup.py
    venv/*
    .venv/*
    */migrations/*
    */k8s/*
    */containers/*
    */__pycache__/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

# Markers for test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    performance: Performance and load tests
    compatibility: Backward compatibility tests
    e2e: End-to-end workflow tests
    slow: Slow running tests
    tenant_aware: Tests specifically for tenant functionality
    channel_specific: Tests for specific channel types (SFTP, ServiceBus, SQS)
    database: Tests requiring database connectivity
    mock: Tests using mocked dependencies
    real_deps: Tests requiring real service dependencies

# Test timeout (in seconds)
timeout = 300

# Parallel execution settings
[tool:pytest-xdist]
numprocesses = auto 