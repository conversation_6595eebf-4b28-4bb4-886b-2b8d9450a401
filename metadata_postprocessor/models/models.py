import uuid

from uuid6 import uuid7
from sqlalchemy.sql import func
from sqlalchemy import create_engine, Column, String, DateTime, JSON, Uuid, LargeBinary, ForeignKey, Integer, Text, \
    Date, SmallInteger, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from datetime import datetime

Base = declarative_base()
RemoteBase = declarative_base()


class IncomingPackage(Base):
    __tablename__ = 'incoming_packages'

    uuid = Column(Uuid, primary_key=True, default=uuid7)
    original_name = Column(String(256))
    channel = Column(String(64))
    received_date = Column(DateTime(timezone=True))
    status = Column(String(128))
    options = Column(JSON)
    incoming_data = Column(JSON)
    raw_incoming_package_id = Column(Uuid)


class Document(Base):
    __tablename__ = 'documents'

    uuid = Column(Uuid, primary_key=True)
    incoming_package_uuid = Column(Uuid, ForeignKey('incoming_packages.uuid'))
    incoming_package = relationship("IncomingPackage")
    file = Column(String(256))
    status = Column(String(128))
    last_opened = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    date = Column(DateTime(timezone=True))
    uploaded = Column(DateTime(timezone=True), nullable=True)
    text_ocr = Column(JSON)
    initial_predictions_ml = Column(JSON)
    initial_predictions_header_ml = Column(JSON)
    predictions_ml = Column(JSON)
    predictions_ml_time = Column(DateTime(timezone=True))
    predictions_human = Column(JSON)
    predictions_human_time = Column(DateTime(timezone=True))
    metadata_ml = Column(JSON)
    metadata_ml_time = Column(DateTime(timezone=True))
    metadata_human = Column(JSON)
    metadata_human_time = Column(DateTime(timezone=True))
    comment = Column(String(1024))
    validation_report = Column(JSON)
    file_size = Column(Integer)
    pages_count = Column(Integer, nullable=True)
    document_end_page = Column(Integer, nullable=True)

    comments = relationship("DocComment", back_populates="document", cascade="all, delete", passive_deletes=True)


class DocumentChunk(Base):
    __tablename__ = 'document_chunk'

    uuid = Column(Uuid, primary_key=True)
    incoming_package_uuid = Column(Uuid, ForeignKey('incoming_packages.uuid'))
    incoming_package = relationship("IncomingPackage")
    parent_document_uuid = Column(Uuid, ForeignKey('documents.uuid'))
    parent_document = relationship("Document")
    file = Column(String(256))
    last_opened = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    date = Column(DateTime(timezone=True))
    status = Column(String(128))
    text_ocr = Column(JSON)
    parent_document_start_page = Column(String(128))
    parent_document_end_page = Column(String(128))
    initial_predictions_ml = Column(JSON)
    initial_predictions_header_ml = Column(JSON)
    predictions_ml = Column(JSON)
    predictions_ml_time = Column(DateTime(timezone=True))
    predictions_human = Column(JSON)
    predictions_human_time = Column(DateTime(timezone=True))
    metadata_ml = Column(JSON)
    metadata_ml_time = Column(DateTime(timezone=True))
    metadata_human = Column(JSON)
    metadata_human_time = Column(DateTime(timezone=True))
    comment = Column(String(1024))
    file_size = Column(Integer)
    pages_count = Column(Integer, nullable=True)


class Splitted_Document(Base):
    __tablename__ = 'splitted_documents'

    uuid = Column(Uuid, primary_key=True)
    parent_document_uuid = Column(Uuid, ForeignKey('documents.uuid'))
    parent_document_pages = Column(JSON)
    classification_confidence = Column(Integer)
    overall_confidence = Column(Integer)
    parent_document = relationship("Document")
    document_type = Column(String(128))
    file = Column(String(256))
    status = Column(String(128))
    splitted = Column(DateTime(timezone=True))
    last_opened = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    text_ocr = Column(JSON)
    metadata_ml = Column(JSON)
    metadata_ml_time = Column(DateTime(timezone=True))
    metadata_human = Column(JSON)
    metadata_human_time = Column(DateTime(timezone=True))
    comment = Column(String(1024))
    file_size = Column(Integer)
    pages_count = Column(Integer)


class User(Base):
    __tablename__ = "users"

    user_uuid = Column(Uuid, default=uuid7, primary_key=True)
    username = Column(Text, unique=True)
    password = Column(String)
    email = Column(String(50), unique=True)
    given_name = Column(String(60), default='')
    family_name = Column(String(60), default='')
    last_online = Column(
        DateTime(timezone=True),
        onupdate=func.now(),
        nullable=True
    )
    secret_key = Column(String, nullable=True)
    user_type = Column(Text)
    current_file = Column(Text)
    login_attempt = Column(SmallInteger, default=0)
    active = Column(Boolean, default=True)
    password_set = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    pass_expire_mail = Column(SmallInteger, default=0)
    otp_requested = Column(
        DateTime(timezone=True), nullable=True
    )

    # parent of
    comments = relationship("DocComment", back_populates="user")


class DocComment(Base):
    __tablename__ = "doc_comments"

    doc_comment_id = Column(Uuid, default=uuid7, primary_key=True)
    document_uuid = Column(Uuid, ForeignKey("documents.uuid", ondelete='CASCADE'))
    left_by_user = Column(Uuid, ForeignKey("users.user_uuid"))
    left_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )
    text = Column(String(1000), nullable=True)

    # child of
    user = relationship("User", back_populates="comments")
    document = relationship("Document", back_populates="comments")


class UserDocument(Base):
    __tablename__ = "user_documents"

    user_uuid = Column(
        Uuid, ForeignKey("users.user_uuid"), primary_key=True)
    document_uuid = Column(
        Uuid, ForeignKey("documents.uuid"), primary_key=True)
    first_opened = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=True
    )
    last_opened = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )
    validated = Column(
        DateTime(timezone=True), nullable=True
    )
    delegated = Column(
        DateTime(timezone=True), nullable=True
    )
    details = Column(String(50), nullable=True)


class UserActivityLog(Base):
    __tablename__ = "user_activity_logs"
    activity_id = Column(Uuid, default=uuid7, primary_key=True)
    user_uuid = Column(Uuid)
    action = Column(String)
    description = Column(String, nullable=True)
    action_timestamp = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )


# Model for the remote database (secondary)
class RawIncomingPackages(RemoteBase):
    __tablename__ = 'raw_incoming_packages'

    id = Column(Uuid, primary_key=True, default=uuid7)
    name_of_file = Column(String)
    object = Column(LargeBinary, nullable=True)  # Incoming PDF file
    channel = Column(String)
    message = Column(JSON, nullable=True)
    received_date = Column(DateTime(timezone=True))
    start_processing_date = Column(DateTime(timezone=True), nullable=True)
    end_processing_date = Column(DateTime(timezone=True), nullable=True)
    status = Column(String)  # 'pending', 'processing', 'done', 'error'
