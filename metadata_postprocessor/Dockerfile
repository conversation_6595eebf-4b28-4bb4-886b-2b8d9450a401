FROM python:3.10.15

RUN apt-get update && apt-get install -y poppler-utils

WORKDIR /app

COPY metadata_postprocessor/requirements.txt /app/requirements.txt

RUN python3 -m pip install -r requirements.txt

COPY metadata_postprocessor/metadata_postprocessor.py /app/metadata_postprocessor.py
COPY pipeline_utils/ /app/pipeline_utils/
COPY models/ /app/models/

# Start the application
CMD ["python3", "-u", "metadata_postprocessor.py"]
