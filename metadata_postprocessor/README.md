# Docker manual for classifier

## Building a Docker for splitter module

1. Start build by 
```docker build -t dx-metadata-post-process-app .```

## Using a Docker for downloader module
1. Mount local configuration and secret keys for external services 
```-v ./{local_path}/config.yml:/app/config.yml```
2. Mount pgSQL models configuration 
```-v ./{local_path}/models/:/models/```
3. Pass ports required for external services using `-p` option or use `--network host` to use host network
```-p 5438:5438 -p 5682:5682 -p ...```

4. Run `docker run {options ... } dx-metadata-post-process-app ` with all required mounts and options 