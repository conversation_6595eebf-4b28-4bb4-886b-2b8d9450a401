import traceback
import yaml
import json
import fitz
import os
import sys
import pika
import re
import uuid

from minio import Minio
from minio.error import S3Error
from collections import defaultdict
from io import BytesIO
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.orm.attributes import flag_modified
from uuid import UUID
from uuid6 import uuid7
from time import sleep
from datetime import datetime, timezone
from statistics import median, mean
from requests.structures import CaseInsensitiveDict
from pprint import pprint
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from pipeline_utils.rabbitmq_connector import PikaServiceFactory
from pipeline_utils.database_connector import DBConnector
from pipeline_utils import J<PERSON>NFormat
from pipeline_utils.monitoring import MonitorService, Status
from pipeline_utils.tenant_utils import get_tenant_processing_config, default_tenant_config

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import IncomingPackage
from models import Document
from models import Splitted_Document
from pipeline_utils import J<PERSON><PERSON>ormat
from pipeline_utils.monitoring import MonitorService, Status

MINIO_URI = os.environ.get('MINIO_URI')
MINIO_ACCESS_KEY = os.environ.get('MINIO_ACCESS_KEY')
MINIO_SECRET_KEY = os.environ.get('MINIO_SECRET_KEY')
MINIO_FILES_BUCKET = os.environ.get('MINIO_FILES_BUCKET')
MINIO_SECURE = os.environ.get('MINIO_SECURE', 'false').lower() == 'true'

RABBITMQ_HOST = os.environ.get('RABBITMQ_HOST')
RABBITMQ_PORT = int(os.environ.get('RABBITMQ_PORT', 5672))
RABBITMQ_USERNAME = os.environ.get('RABBITMQ_USERNAME')
RABBITMQ_PASSWORD = os.environ.get('RABBITMQ_PASSWORD')
RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME = os.environ.get('RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME')
RABBITMQ_TO_VALIDATE_QUEUE_NAME = os.environ.get('RABBITMQ_TO_VALIDATE_QUEUE_NAME')

PGSQL_HOST = os.environ.get('PGSQL_HOST')
PGSQL_PORT = os.environ.get('PGSQL_PORT')
PGSQL_USERNAME = os.environ.get('PGSQL_USERNAME')
PGSQL_PASSWORD = os.environ.get('PGSQL_PASSWORD')
PGSQL_DB_NAME = os.environ.get('PGSQL_DB_NAME')

API_HOST = os.environ.get('API_HOST')
API_PORT = os.environ.get('API_PORT')
PROJECT_NAME = os.environ.get('project_name')
APP_NAME = os.environ.get('app_name')

Base = declarative_base()

db_connector = DBConnector(
    pgsql_host=PGSQL_HOST,
    pgsql_port=PGSQL_PORT,
    pgsql_username=PGSQL_USERNAME,
    pgsql_password=PGSQL_PASSWORD,
    pgsql_db_name=PGSQL_DB_NAME
)
Base.metadata.create_all(db_connector.get_engine())
session = None

rmq_service_factory = PikaServiceFactory(
    host=RABBITMQ_HOST,
    port=RABBITMQ_PORT,
    username=RABBITMQ_USERNAME,
    password=RABBITMQ_PASSWORD,
    ssl_options=None
)
rmq_service = rmq_service_factory.create_service()
rmq_service.start()

minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)

DOC_TYPES = [
    'Other', 'RFA', 'Medical Records', 'Misc Correspondence', 'Legal Correspondence',
    'State forms', 'Ratings', 'Subpoena', 'EOB/EOR', 'IMR/IME/QME', 'Supplemental/Work Status',
    'Physician Bill (HCFA)', 'Hospital Bill (UB)', 'Translation Bills', 'Determination - Med Auth',
    'Vocational Evaluations', 'Case Management Notes', 'Fax', 'Injury/Illness/FROI'
]

EXCLUDE_FIELDS = ['docName', 'docLink', 'docType', 'docConfidence', 'pageRefStart', 'pageRefEnd', 'pagesRef',
                  'treatments']


def most_frequent_string(lst):
    if not lst:
        return ""

    string_count = CaseInsensitiveDict({})
    max_count = 0
    most_frequent = None

    for item in lst:
        if isinstance(item, str):
            string_count[item] = string_count.get(item, 0) + 1
            if string_count[item] > max_count:
                max_count = string_count[item]
                most_frequent = item

    return most_frequent


def extract_best_values(all_metadata):
    claim_numbers = []
    patient_first_names = []
    patient_middle_names = []
    patient_last_names = []

    for metadata in all_metadata:
        if "namingData" in metadata:
            if "claimNumber" in metadata["namingData"]:
                claim_number = metadata["namingData"]["claimNumber"]["value"]
                if claim_number and re.match(r'^[a-zA-Z0-9#\s-]{6,16}$', claim_number):
                    claim_numbers.append(claim_number)

            if "patientFirstName" in metadata["namingData"]:
                patient_name = metadata["namingData"]["patientFirstName"]["value"]
                if patient_name and re.match(r'^[a-zA-Z\s,.]{4,30}$', patient_name):
                    patient_first_names.append(patient_name)

            if "patientMiddleName" in metadata["namingData"]:
                patient_name = metadata["namingData"]["patientMiddleName"]["value"]
                if patient_name and re.match(r'^[a-zA-Z\s,.]{4,30}$', patient_name):
                    patient_middle_names.append(patient_name)

            if "patientLastName" in metadata["namingData"]:
                patient_name = metadata["namingData"]["patientLastName"]["value"]
                if patient_name and re.match(r'^[a-zA-Z\s,.]{4,30}$', patient_name):
                    patient_last_names.append(patient_name)

    best_claim_number = most_frequent_string(claim_numbers)
    best_patient_first_name = most_frequent_string(patient_first_names)
    best_patient_middle_name = most_frequent_string(patient_middle_names)
    best_patient_last_name = most_frequent_string(patient_last_names)

    return best_claim_number.title(), best_patient_first_name.title(), best_patient_middle_name.title(), best_patient_last_name.title()


def update_status(splitted_documents):
    global session

    parent_document = splitted_documents[0].parent_document
    parent_document.status = 'to_validate'
    flag_modified(parent_document, "status")

    for splitted_document in splitted_documents:
        if splitted_document.metadata_ml:
            splitted_document.status = 'to_validate'
            flag_modified(splitted_document, "status")

    session.commit()


def update_metadata(splitted_documents):
    global session

    all_metadata = []

    # Loop over all splitted documents and mark metadata_ml as modified
    for splitted_document in splitted_documents:
        flag_modified(splitted_document, "metadata_ml")
        if splitted_document.metadata_ml:
            all_metadata.append(splitted_document.metadata_ml)

    # Extract best values from all metadata
    best_claim_number, best_patient_first_name, best_patient_middle_name, best_patient_last_name = extract_best_values(
        all_metadata)
    print(f"best_claim_number: {best_claim_number}")
    print(f"best_patient_first_name: {best_patient_first_name}")
    print(f"best_patient_middle_name: {best_patient_middle_name}")
    print(f"best_patient_last_name: {best_patient_last_name}")

    for splitted_document in splitted_documents:
        if splitted_document.metadata_ml:
            # Update namingData fields as before
            if "claimNumber" not in splitted_document.metadata_ml["namingData"]:
                splitted_document.metadata_ml["namingData"]["claimNumber"] = {}
            if "patientFirstName" not in splitted_document.metadata_ml["namingData"]:
                splitted_document.metadata_ml["namingData"]["patientFirstName"] = {}
            if "patientMiddleName" not in splitted_document.metadata_ml["namingData"]:
                splitted_document.metadata_ml["namingData"]["patientMiddleName"] = {}
            if "patientLastName" not in splitted_document.metadata_ml["namingData"]:
                splitted_document.metadata_ml["namingData"]["patientLastName"] = {}
            splitted_document.metadata_ml["namingData"]["claimNumber"]["value"] = best_claim_number
            splitted_document.metadata_ml["namingData"]["patientFirstName"]["value"] = best_patient_first_name
            splitted_document.metadata_ml["namingData"]["patientMiddleName"]["value"] = best_patient_middle_name
            splitted_document.metadata_ml["namingData"]["patientLastName"]["value"] = best_patient_last_name

            # Update metaData fields for claimant and claim
            if "metaData" not in splitted_document.metadata_ml:
                splitted_document.metadata_ml["metaData"] = {}
            if "claimant" not in splitted_document.metadata_ml["metaData"]:
                splitted_document.metadata_ml["metaData"]["claimant"] = {}
            if "claim" not in splitted_document.metadata_ml["metaData"]:
                splitted_document.metadata_ml["metaData"]["claim"] = {}
            splitted_document.metadata_ml["metaData"]["claimant"]["firstName"]["value"] = best_patient_first_name
            splitted_document.metadata_ml["metaData"]["claimant"]["middleName"]["value"] = best_patient_middle_name
            splitted_document.metadata_ml["metaData"]["claimant"]["lastName"]["value"] = best_patient_last_name
            splitted_document.metadata_ml["metaData"]["claim"]["claimNumber"]["value"] = best_claim_number

            flag_modified(splitted_document, "metadata_ml")

    session.commit()


def all_splitted_docs_are_ready(incoming_package, splitted_document_uuid):
    # target IncomingPackage UUID
    target_uuid = incoming_package.uuid

    # Query to find all Splitted_Document items related to the IncomingPackage with the given UUID
    splitted_documents = session.query(Splitted_Document). \
        join(Document, Document.uuid == Splitted_Document.parent_document_uuid). \
        join(IncomingPackage, IncomingPackage.uuid == Document.incoming_package_uuid). \
        filter(IncomingPackage.uuid == target_uuid). \
        all()

    for doc in splitted_documents:
        if doc.uuid != splitted_document_uuid:
            if doc.status != 'to_metadata_postprocess':
                return False, None
    sorted_splitted_documents = sorted(splitted_documents,
                                       key=lambda x: min(page[0] for page in x.parent_document_pages))
    return True, sorted_splitted_documents


def get_all_splitted_docs(incoming_package):
    # target IncomingPackage UUID
    target_uuid = incoming_package.uuid

    # Query to find all Splitted_Document items related to the IncomingPackage with the given UUID
    splitted_documents = session.query(Splitted_Document). \
        join(Document, Document.uuid == Splitted_Document.parent_document_uuid). \
        join(IncomingPackage, IncomingPackage.uuid == Document.incoming_package_uuid). \
        filter(IncomingPackage.uuid == target_uuid). \
        all()

    sorted_splitted_documents = sorted(splitted_documents,
                                       key=lambda x: min(page[0] for page in x.parent_document_pages))
    return sorted_splitted_documents


def get_parent_document(parent_document_uuid):
    global session
    document = session.query(Document).filter_by(uuid=parent_document_uuid).first()
    return document


def get_splitted_document(splitted_document_uuid):
    global session
    document = session.query(Splitted_Document).filter_by(uuid=splitted_document_uuid).first()
    return document


def add_splitted_document_to_minio(pdf_stream, file_id_str):
    file_size = pdf_stream.getbuffer().nbytes
    minio_client.put_object(MINIO_FILES_BUCKET, file_id_str, pdf_stream, file_size)


def add_splitted_document_record_to_db(splitted_document):
    global session
    splitted_document.splitted = datetime.now(timezone.utc)
    session.add(splitted_document)
    session.commit()


def create_new_splitted_document(parent_document_in_memory_file, parent_document, splitted_document):
    doc = fitz.open("pdf", parent_document_in_memory_file)
    subdocument = fitz.open()
    for page_range in splitted_document.parent_document_pages:
        for page_num in range(int(page_range[0]) - 1, int(page_range[1])):
            subdocument.insert_pdf(doc, from_page=page_num, to_page=page_num)
    pdf_stream = BytesIO()
    subdocument.save(pdf_stream)
    subdocument.close()
    pdf_stream.seek(0)

    add_splitted_document_record_to_db(splitted_document)
    add_splitted_document_to_minio(pdf_stream, f'{splitted_document.uuid}')


def collect_documents(splitted_documents):
    document_groups = defaultdict(list)

    for splitted_document in splitted_documents:
        metadata_ml = splitted_document.metadata_ml
        if metadata_ml and "namingData" in metadata_ml:
            document_type = splitted_document.document_type
            document_date = metadata_ml["namingData"].get("docDate")
            patient_first_name = metadata_ml["namingData"].get("patientFirstName")
            patient_middle_name = metadata_ml["namingData"].get("patientMiddleName") or {"value": ''}
            patient_last_name = metadata_ml["namingData"].get("patientLastName")

            if (document_type and patient_first_name and patient_last_name and document_date):
                key = (
                    document_type, patient_first_name['value'], patient_middle_name['value'],
                    patient_last_name['value'],
                    document_date['value'])
                print(f'key: {key}')

                document_groups[key].append(splitted_document)

    return document_groups


def merge_dicts(dict1, dict2):
    """
    Recursively merges two dictionaries.

    Args:
    dict1 (dict): The first dictionary.
    dict2 (dict): The second dictionary.

    Returns:
    dict: The merged dictionary.
    """
    for key in dict2:
        if key in dict1:
            if isinstance(dict1[key], dict) and isinstance(dict2[key], dict):
                merge_dicts(dict1[key], dict2[key])
            elif isinstance(dict1[key], list) and isinstance(dict2[key], list):
                dict1[key].extend(dict2[key])
            elif dict1[key] in [None, ""] and dict2[key] not in [None, ""]:
                dict1[key] = dict2[key]
        else:
            dict1[key] = dict2[key]
    return dict1


def get_tenant_postprocessing_config(tenant_id=None, subtenant_id=None):
    """
    Get tenant-specific metadata postprocessing configuration.
    
    Args:
        tenant_id: Tenant ID
        subtenant_id: Subtenant ID
    
    Returns:
        Dictionary containing postprocessing configuration
    """
    if not tenant_id:
        return {
            'enable_duplicate_detection': True,
            'duplicate_threshold': 0.85,
            'enable_document_merging': True,
            'enable_rfa_merging': True,
            'custom_merging_rules': {}
        }
    
    try:
        processing_config = get_tenant_processing_config(tenant_id, subtenant_id)
        tenant_config = default_tenant_config.get_tenant_config(tenant_id, subtenant_id)
        
        return {
            'enable_duplicate_detection': processing_config.get('enable_duplicate_detection', True),
            'duplicate_threshold': tenant_config.get('document_types', {}).get('duplicate_threshold', 0.85),
            'enable_document_merging': tenant_config.get('document_types', {}).get('enable_merging', True),
            'enable_rfa_merging': tenant_config.get('document_types', {}).get('enable_rfa_merging', True),
            'custom_merging_rules': tenant_config.get('document_types', {}).get('custom_merging_rules', {})
        }
    except Exception as e:
        print(f"Error getting tenant postprocessing config: {e}")
        return {
            'enable_duplicate_detection': True,
            'duplicate_threshold': 0.85,
            'enable_document_merging': True,
            'enable_rfa_merging': True,
            'custom_merging_rules': {}
        }


def mark_duplicates(splitted_documents, tenant_id=None, subtenant_id=None):
    """
    Finds duplicate documents (using document_duplicate_check_with_threshold)
    and sets the 'isDuplicated': True flag in their metadata_ml.
    In each group of duplicate documents, the first one (according to the order in splitted_documents)
    remains unmarked, and all the others get the 'isDuplicated': True flag along with a new field
    'pagesRefOrig' that corresponds to the 'pagesRef' value of the original document.

    Args:
        splitted_documents: List of splitted documents
        tenant_id: Tenant ID for tenant-specific processing
        subtenant_id: Subtenant ID for tenant-specific processing

    Returns:
        bool: True if at least one document was marked as duplicate; otherwise, False.
    """
    global session
    
    # Get tenant-specific postprocessing configuration
    postprocessing_config = get_tenant_postprocessing_config(tenant_id, subtenant_id)
    
    if not postprocessing_config['enable_duplicate_detection']:
        print(f"Duplicate detection disabled for tenant {tenant_id}")
        return False
        
    threshold = postprocessing_config['duplicate_threshold']
    print(f"Using tenant-specific duplicate detection for tenant {tenant_id}: threshold={threshold}")
    
    duplicates = document_duplicate_check_with_threshold(splitted_documents, threshold)
    if not duplicates:
        return False
    print("Duplicate check")
    # We use a union-find algorithm to group duplicates
    parent = {}
    doc_map = {}

    def find(x):
        if parent[x] != x:
            parent[x] = find(parent[x])
        return parent[x]

    def union(x, y):
        rootX = find(x)
        rootY = find(y)
        if rootX != rootY:
            parent[rootY] = rootX

    # Process each pair of duplicates, uniting only those for which compare_metadata_json returns True
    for doc1, doc2 in duplicates:

        print("doc1, doc2:", doc1.uuid, doc2.uuid)
        print("metadata_ml:", doc1.metadata_ml, doc2.metadata_ml)

        match, _ = compare_metadata_json(doc1.metadata_ml, doc2.metadata_ml, EXCLUDE_FIELDS)
        print("match:", match)
        if match:
            for doc in (doc1, doc2):
                if doc.uuid not in parent:
                    parent[doc.uuid] = doc.uuid
                    doc_map[doc.uuid] = doc
            union(doc1.uuid, doc2.uuid)

    if not parent:
        return False

    # Group documents by their root element
    groups = {}
    for doc_uuid in parent:
        root = find(doc_uuid)
        groups.setdefault(root, []).append(doc_map[doc_uuid])

    duplicates_marked = False
    # For each group with more than one document, mark all but the first one
    # Additionally, add the field "pagesRefOrig" with the 'pagesRef' value from the original document.
    for group in groups.values():
        if len(group) > 1:
            group.sort(key=lambda d: splitted_documents.index(d))
            original_doc = group[0]
            original_pages_ref = None
            if original_doc.metadata_ml is not None:
                original_pages_ref = original_doc.metadata_ml.get("pagesRef")
            # The first document remains the original, all others get isDuplicated=True and pagesRefOrig
            for doc in group[1:]:
                if doc.metadata_ml is None:
                    doc.metadata_ml = {}
                doc.metadata_ml["isDuplicated"] = True
                doc.metadata_ml["pagesRefOrig"] = original_pages_ref
                flag_modified(doc, "metadata_ml")
                duplicates_marked = True

    if duplicates_marked:
        session.commit()
    print("Duplicates marked")
    return duplicates_marked


def merge_documents(parent_document, splitted_documents, tenant_id=None, subtenant_id=None):
    """
    Merge documents based on tenant-specific configuration.
    
    Args:
        parent_document: Parent document object
        splitted_documents: List of splitted documents
        tenant_id: Tenant ID for tenant-specific processing
        subtenant_id: Subtenant ID for tenant-specific processing
    
    Returns:
        bool: True if any documents were merged, False otherwise
    """
    global session

    # Get tenant-specific postprocessing configuration
    postprocessing_config = get_tenant_postprocessing_config(tenant_id, subtenant_id)
    
    if not postprocessing_config['enable_document_merging']:
        print(f"Document merging disabled for tenant {tenant_id}")
        return False

    merged = False
    print("Starting merge_documents process.")

    document_groups = collect_documents(splitted_documents)

    for key, documents in document_groups.items():
        document_type, patient_first_name, patient_middle_name, patient_last_name, document_date = key

        # Apply tenant-specific merging rules
        enable_rfa_merging = postprocessing_config['enable_rfa_merging']
        custom_merging_rules = postprocessing_config.get('custom_merging_rules', {})
        
        if document_type == 'RFA' and len(documents) > 1 and enable_rfa_merging:
            print(f"Applying RFA merging for tenant {tenant_id}")
            sorted_docs = sorted(documents, key=lambda d: d.metadata_ml['pagesRef'][0][0])
            subgroups = []
            current_group = [sorted_docs[0]]
            for i in range(1, len(sorted_docs)):
                prev_doc = sorted_docs[i - 1]
                curr_doc = sorted_docs[i]
                prev_range = prev_doc.metadata_ml['pagesRef']
                curr_range = curr_doc.metadata_ml['pagesRef']
                if prev_range[0][1] + 1 == curr_range[0][0]:
                    current_group.append(curr_doc)
                else:
                    if len(current_group) > 1:
                        subgroups.append(current_group)
                    current_group = [curr_doc]
            if len(current_group) > 1:
                subgroups.append(current_group)

            if not subgroups:
                continue

            for subgroup in subgroups:
                print("Merging subgroup for key", key, "with", len(subgroup), "documents.")
                classification_confidence = []
                parent_document_pages = []
                metadata_ml = JSONFormat.GetFieldsForDocument(document_type)
                parent_document = subgroup[0].parent_document
                parent_document_id = f'{parent_document.uuid}'
                for document in subgroup:
                    classification_confidence.append(document.classification_confidence)
                    parent_document_pages.extend(document.parent_document_pages)
                    merge_dicts(metadata_ml, document.metadata_ml)
                    session.delete(document)
                    minio_client.remove_object(MINIO_FILES_BUCKET, str(document.uuid))
                new_splitted_document = Splitted_Document()
                new_splitted_document.parent_document = parent_document
                new_splitted_document.uuid = uuid7()
                new_splitted_document.classification_confidence = mean(classification_confidence)
                new_splitted_document.parent_document_pages = parent_document_pages
                new_splitted_document.metadata_ml = metadata_ml
                new_splitted_document.metadata_ml["pagesRefOrig"] = None
                new_splitted_document.document_type = document_type

                response = minio_client.get_object(MINIO_FILES_BUCKET, parent_document_id)
                parent_document_in_memory_file = BytesIO(response.read())
                create_new_splitted_document(parent_document_in_memory_file, parent_document, new_splitted_document)
                print("Merged subgroup created with new document UUID:", new_splitted_document.uuid)
                merged = True

        # Apply custom merging rules for other document types if specified
        elif document_type in custom_merging_rules and len(documents) > 1:
            merge_rule = custom_merging_rules[document_type]
            print(f"Applying custom merging rule for {document_type} for tenant {tenant_id}: {merge_rule}")
            # Custom merging logic could be implemented here based on the rule

    print("merge_documents process completed. Merged:", merged)
    return merged


def update_parent_document_metadata(parent_document, splitted_documents):
    global session

    new_boundaries = []
    new_metadata = []

    for splitted_document in splitted_documents:
        new_boundaries.append({
            'page_ranges': splitted_document.parent_document_pages,
            'type': splitted_document.document_type,
            'classification_confidence': splitted_document.classification_confidence,
            'overall_confidence': splitted_document.overall_confidence
        })

        metadata_ml = splitted_document.metadata_ml
        metadata_ml.update({
            'pagesRef': splitted_document.parent_document_pages,
            'docType': splitted_document.document_type,
        })
        splitted_document.metadata_ml = metadata_ml
        if metadata_ml["pagesRefOrig"] is not None and metadata_ml["pagesRefOrig"] != []:
            metadata_ml["isDuplicated"] = True
        new_metadata.append(metadata_ml)

    parent_document.predictions_ml = new_boundaries
    parent_document.metadata_ml = new_metadata
    parent_document.metadata_ml_time = datetime.now(timezone.utc)
    flag_modified(parent_document, "predictions_ml")
    flag_modified(parent_document, "metadata_ml")
    print(f"parent document metadata ml len: {len(parent_document.metadata_ml)}")

    session.commit()


def print_spl_docs_metadata(splitted_documents):
    for splitted_document in splitted_documents:
        pprint(splitted_document.metadata_ml["namingData"])


def compare_metadata_json(json1, json2, exclude_fields=EXCLUDE_FIELDS, field_name=None):
    """
    Recursively compares two JSON objects (which can be dictionaries or lists).

    Returns:
        (bool, bool): A tuple where the first value indicates if the JSON objects match,
                      and the second indicates if differences in treatments were detected.

    Note:
        - Fields listed in 'exclude_fields' are skipped everywhere.
        - If a field named 'isHandwritten' is True in either JSON, the JSONs are considered different,
          unless 'isHandwritten' is part of exclude_fields.
        - If a field named 'treatments' differs between the two JSONs, diff_treatments is set to True,
          unless 'treatments' is part of exclude_fields.
        - When comparing string values, if one is empty and the other is not, they are considered different.
          However, if both strings are empty, they are considered equal.
    """
    if exclude_fields is None:
        exclude_fields = []

    # If the current field is excluded, consider it matching without further comparison.
    if field_name is not None and field_name in exclude_fields:
        return True, False

    diff_treatments = False

    # Check if both JSON objects are of the same type
    if type(json1) != type(json2):
        return False, diff_treatments

    # If both are dictionaries, compare their keys and values recursively.
    if isinstance(json1, dict):
        for key in json1:
            # Skip excluded keys.
            if key in exclude_fields:
                continue

            # Special handling for "isHandwritten", unless it is excluded.
            if key == "isHandwritten" and (json1[key] is True or json2.get(key) is True):
                return False, diff_treatments

            # For "treatments", mark differences if values do not match (and key is not excluded).
            if key == "treatments" and (json1[key] != json2.get(key)):
                diff_treatments = True

            if key not in json2:
                return False, diff_treatments

            match, child_diff_treatments = compare_metadata_json(
                json1[key], json2[key], exclude_fields, field_name=key
            )
            if not match:
                return False, diff_treatments
            if child_diff_treatments:
                diff_treatments = True

        # Check for extra keys in the second dictionary that are not in the first.
        for key in json2:
            if key in exclude_fields:
                continue
            if key not in json1:
                return False, diff_treatments

    # If both are lists, compare each element recursively.
    elif isinstance(json1, list):
        if len(json1) != len(json2):
            return False, diff_treatments

        for item1, item2 in zip(json1, json2):
            # For list items, there is no specific field name.
            match, child_diff_treatments = compare_metadata_json(item1, item2, exclude_fields)
            if not match:
                return False, diff_treatments
            if child_diff_treatments:
                diff_treatments = True

    else:
        # For string values, treat both empty strings as equal.
        if isinstance(json1, str) and isinstance(json2, str):
            if json1 == "" and json2 == "":
                return True, diff_treatments  # Both empty: equal.
            elif json1 == "" or json2 == "":
                return False, diff_treatments  # One empty and one non-empty: different.

        # For other primitive types, directly compare.
        if json1 != json2:
            return False, diff_treatments

    return True, diff_treatments


def document_duplicate_check_with_threshold(splitted_documents, threshold=0.85):
    """
    Checks for duplicate documents based on OCR text similarity with a threshold.

    Args:
    splitted_documents (list): List of splitted documents to check for duplicates.
    threshold (float): The similarity threshold to consider documents as duplicates (default is 0.85).

    Returns:
    list: List of tuples containing pairs of duplicate documents.
    """
    # Initialize a dictionary with document types as keys and empty lists as values
    documents_by_type = {doc_type: [] for doc_type in DOC_TYPES}

    # Group documents by their type based on the predefined list
    for document in splitted_documents:
        if document.document_type in documents_by_type:
            documents_by_type[document.document_type].append(document)
        else:
            # If document type is not in the predefined list, group it under 'Other'
            documents_by_type['Other'].append(document)

    # Compare text_ocr for documents with the same type using a threshold
    duplicates = []
    for doc_type, documents in documents_by_type.items():
        if len(documents) > 1:  # Check if there are at least two documents of the same type
            # Extract text_ocr content for each document
            ocr_texts = [doc.text_ocr for doc in documents]

            # Ensure all elements in ocr_texts are strings
            ocr_texts = [" ".join(str(item) for item in text) if isinstance(text, list) else str(text) for text in
                         ocr_texts]

            # Convert texts to TF-IDF vectors
            vectorizer = TfidfVectorizer().fit_transform(ocr_texts)
            vectors = vectorizer.toarray()

            # Calculate cosine similarity between all pairs of documents
            cosine_sim_matrix = cosine_similarity(vectors)

            # Iterate through the similarity matrix to find duplicates based on the threshold
            for i in range(len(documents)):
                for j in range(i + 1, len(documents)):
                    if cosine_sim_matrix[i][j] >= threshold:
                        duplicates.append((documents[i], documents[j]))

    return duplicates


def callback(msg_tuple):
    """
    Function to be called by the RabbitMQ consumer loop.
    The message body should be in the format: {'file_id': str, 'document_type': str, 'tenant_id': str, 'subtenant_id': str}.
    """
    ch, method, properties, body = msg_tuple
    global session

    print(f" [x] Received {body}")
    queue_item = json.loads(body.decode('UTF-8'))

    splitted_document_uuid = queue_item['file_id']
    document_type = queue_item['document_type']
    # Extract tenant information from the message
    tenant_id = queue_item.get('tenant_id')
    subtenant_id = queue_item.get('subtenant_id')
    
    print(f"Post-processing metadata for document type '{document_type}' for tenant: {tenant_id}, subtenant: {subtenant_id}")

    session = db_connector.get_session()
    splitted_document = get_splitted_document(splitted_document_uuid)
    if splitted_document:
        parent_document = splitted_document.parent_document
        incoming_package = parent_document.incoming_package
        all_docs_are_ready, splitted_documents = all_splitted_docs_are_ready(incoming_package, splitted_document.uuid)
        print(f"{incoming_package.original_name} - Status ready - {all_docs_are_ready}")
        if all_docs_are_ready:

            [
                print("parent_document_pages", doc.parent_document_pages, "document_type", doc.document_type, "uuid",
                      doc.uuid)
                for doc in splitted_documents
            ]

            print(parent_document.uuid)
            print_spl_docs_metadata(splitted_documents)  # Debug info
            update_metadata(splitted_documents)
            print(parent_document.uuid)
            print_spl_docs_metadata(splitted_documents)  # Debug info
            # Check for duplicates and mark them
            duplicates_marked = mark_duplicates(splitted_documents, tenant_id, subtenant_id)

            if duplicates_marked:
                splitted_documents = get_all_splitted_docs(incoming_package)

            merged = merge_documents(parent_document, splitted_documents, tenant_id, subtenant_id)

            if merged or duplicates_marked:
                splitted_documents = get_all_splitted_docs(incoming_package)

            update_parent_document_metadata(parent_document, splitted_documents)
            update_status(splitted_documents)

            actual_splitted_document_id = str(splitted_documents[0].uuid)
            actual_splitted_document_type = str(splitted_documents[0].document_type)
            
            # Pass tenant information to the next component
            next_queue_item = {
                'file_id': actual_splitted_document_id, 
                'document_type': actual_splitted_document_type,
                'tenant_id': tenant_id,
                'subtenant_id': subtenant_id
            }
            rmq_service.send_message(RABBITMQ_TO_VALIDATE_QUEUE_NAME, json.dumps(next_queue_item))

    session.close()
    rmq_service.safe_ack(ch, method.delivery_tag)
    # connection.close() # if uncomment, process only one message


rmq_service.read_messages(RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME, callback)
# monitor = MonitorService(PROJECT_NAME, APP_NAME, 'metadata_postprocessor', API_HOST, API_PORT)
# monitor.start_monitoring()
# monitor.update_status(Status.UP)
rmq_service.run()
