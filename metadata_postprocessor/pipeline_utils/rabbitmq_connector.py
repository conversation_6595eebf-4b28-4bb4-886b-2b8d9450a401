import pika
import time
import traceback
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

PREFETCH_COUNT = 1


class PikaService:
    def __init__(self, parameters):
        self.parameters = parameters
        self.connection = None
        self.channel = None
        self.consumers = []
        self.read_list = []
        self._stop = False
        logger.info(f"Initializing PikaService with parameters: host={parameters.host}, port={parameters.port}, virtual_host={parameters.virtual_host}")

    def start(self):
        """Initialize connection."""
        try:
            logger.info("Starting RabbitMQ connection...")
            self.connect()
        except Exception:
            logger.error("Unexpected error during connection start:")
            logger.error(traceback.format_exc())

    def stop(self):
        """Stop reading messages and close the connection."""
        self._stop = True
        if self.connection and self.connection.is_open:
            try:
                logger.info("Closing RabbitMQ connection...")
                self.connection.close()
                logger.info("RabbitMQ connection closed successfully")
            except Exception:
                logger.error("Error while closing connection:")
                logger.error(traceback.format_exc())

    def connect(self):
        """Establish a connection to RabbitMQ, with retries on failure."""
        attempt = 0
        while True:
            try:
                logger.info(f"Attempting to connect to RabbitMQ (attempt {attempt + 1})...")
                logger.info(f"Connection parameters: host={self.parameters.host}, port={self.parameters.port}, virtual_host={self.parameters.virtual_host}")
                self.connection = pika.BlockingConnection(self.parameters)
                self.channel = self.connection.channel()
                self.channel.basic_qos(prefetch_count=PREFETCH_COUNT)
                logger.info("Successfully connected to RabbitMQ")
                # Re-declare consumers after restoring the connection
                for exchange, routing_key, callback in self.consumers:
                    self._setup_read(exchange, routing_key, callback)
                return
            except pika.exceptions.AMQPConnectionError as e:
                attempt += 1
                wait_time = min(5 * attempt, 30)
                logger.error(f"AMQP Connection failed: {str(e)}")
                logger.error(f"Connection parameters used: host={self.parameters.host}, port={self.parameters.port}, virtual_host={self.parameters.virtual_host}")
                logger.info(f"Reconnecting in {wait_time} seconds...")
                time.sleep(wait_time)
            except Exception as e:
                logger.error("Unexpected error during connect:")
                logger.error(f"Error type: {type(e).__name__}")
                logger.error(f"Error message: {str(e)}")
                logger.error(traceback.format_exc())
                attempt += 1
                wait_time = min(5 * attempt, 30)
                logger.info(f"Will try reconnecting in {wait_time} seconds...")
                time.sleep(wait_time)

    def _on_message_callback(self, ch, method, properties, body, callback):
        """Callback invoked when a message is received."""
        try:
            callback((ch, method, properties, body))
        except Exception:
            print("Error handling message:")
            print(traceback.format_exc())

    def read_messages(self, routing_key, user_callback, exchange=""):
        """
        Configure the queue and exchange for reading.
        We create a wrapper that logs the message before calling the user callback.
        """

        def wrap_callback(msg_tuple):
            ch, method, props, body = msg_tuple
            print(f"Received message: {body}")
            try:
                user_callback(msg_tuple)
            except Exception:
                print("Error in user callback:")
                print(traceback.format_exc())

        self.read_list.append((exchange, routing_key, wrap_callback))
        if self.channel and self.connection and self.connection.is_open:
            try:
                self._setup_read(exchange, routing_key, wrap_callback)
            except Exception:
                print("Error in setting up read queue:")
                print(traceback.format_exc())

    def _setup_read(self, exchange, routing_key, callback):
        """Declare exchange, queue, binding, and subscribe to messages."""
        try:
            if exchange:
                self.channel.exchange_declare(exchange=exchange, exchange_type='topic', durable=True, auto_delete=False)
            self.channel.queue_declare(queue=routing_key, durable=True)
            if exchange:
                self.channel.queue_bind(queue=routing_key, exchange=exchange, routing_key=routing_key)

            self.channel.basic_consume(
                queue=routing_key,
                on_message_callback=lambda ch, method, props, body: self._on_message_callback(ch, method, props, body,
                                                                                              callback),
                auto_ack=False
            )

            # Store the callback with the original parameters for reconnection
            found = False
            for i, (exch, rk, cb) in enumerate(self.consumers):
                if exch == exchange and rk == routing_key:
                    self.consumers[i] = (exchange, routing_key, callback)
                    found = True
                    break
            if not found:
                self.consumers.append((exchange, routing_key, callback))
        except Exception:
            print("Error in _setup_read:")
            print(traceback.format_exc())

    def run(self):
        """Main loop for reading messages. This runs in the main thread."""
        while not self._stop:
            try:
                self.channel.start_consuming()
            except pika.exceptions.ChannelWrongStateError:
                print("Channel is closed. Attempting to reconnect...")
                self.clientConnectionLost("Channel closed in consuming loop")
            except pika.exceptions.AMQPConnectionError:
                self.clientConnectionLost("Connection lost in consuming loop")
            except Exception:
                print("Unexpected error in consuming loop:")
                print(traceback.format_exc())
                time.sleep(5)

    def send_message(self, routing_key, message, exchange=""):
        """Send a message immediately (in the same thread)."""
        if not self._can_publish():
            print("Can't publish message: no active connection.")
            return
        try:
            if exchange:
                self.channel.exchange_declare(exchange=exchange, exchange_type='topic', durable=True, auto_delete=False)

            self.channel.basic_publish(
                exchange=exchange,
                routing_key=routing_key,
                body=message,
                properties=pika.BasicProperties(delivery_mode=2)
            )
            print(f'{exchange} ({routing_key}): {message}')
        except pika.exceptions.AMQPConnectionError as e:
            print("Error publishing message (ConnectionError):")
            print(e)
            self.clientConnectionLost("Publishing failed")
        except Exception:
            print("Unexpected error publishing message:")
            print(traceback.format_exc())

    def _can_publish(self):
        """Check if we can currently publish messages."""
        return self.connection and self.connection.is_open and self.channel and not self.channel.is_closed

    def clientConnectionLost(self, reason):
        """Handle connection loss: reconnect."""
        print(f"Connection lost, reason: {reason}. Reconnecting...")
        try:
            if self.connection and self.connection.is_open:
                self.connection.close()
        except Exception:
            print("Error closing lost connection:")
            print(traceback.format_exc())

        self.connect()
        # After reconnecting, re-declare all queues and exchanges
        for exchange, routing_key, callback in self.consumers:
            try:
                self._setup_read(exchange, routing_key, callback)
            except Exception:
                print("Error in re-setup after reconnect:")
                print(traceback.format_exc())

    @staticmethod
    def safe_ack(channel, delivery_tag):
        if not channel.is_open:
            print("Channel closed before ack could be sent.")
            return
        try:
            channel.basic_ack(delivery_tag=delivery_tag)
        except pika.exceptions.ChannelWrongStateError:
            print("Channel closed during ack, will attempt reconnection or handle differently.")


class PikaServiceFactory:
    def __init__(self, host="localhost", port=5672, virtual_host="/", username="guest", password="guest",
                 ssl_options=None, heartbeat=500):
        self.host = host
        self.port = port
        self.virtual_host = virtual_host
        self.username = username
        self.password = password
        self.ssl_options = ssl_options
        self.heartbeat = heartbeat
        logger.info(f"Initializing PikaServiceFactory with: host={host}, port={port}, virtual_host={virtual_host}, ssl_enabled={ssl_options is not None}")

    def create_service(self):
        credentials = pika.PlainCredentials(self.username, self.password)
        params = pika.ConnectionParameters(
            port=self.port,
            host=self.host,
            virtual_host=self.virtual_host,
            credentials=credentials,
            ssl_options=self.ssl_options,
            heartbeat=self.heartbeat,
        )
        logger.info(f"Creating PikaService with connection parameters: host={self.host}, port={self.port}, virtual_host={self.virtual_host}")
        service = PikaService(params)
        return service
