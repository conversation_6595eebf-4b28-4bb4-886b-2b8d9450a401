# Multi-tenant ML Pipeline Test Environment
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt* ./
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# Install common Python packages that we know we'll need
RUN pip install --no-cache-dir \
    sqlalchemy \
    psycopg2-binary \
    pika \
    minio \
    uuid6 \
    python-dateutil \
    requests \
    pyyaml \
    cryptography \
    boto3 \
    azure-servicebus \
    azure-storage-blob \
    paramiko \
    fitz \
    img2pdf \
    pillow \
    extract-msg \
    python-magic \
    PyMuPDF

# Copy the project files
COPY . .

# Set Python path
ENV PYTHONPATH=/app

# Create directories for test results
RUN mkdir -p /app/test_results

# Make the test script executable
RUN chmod +x /app/run_tests.sh

# Default command
CMD ["/app/run_tests.sh"] 