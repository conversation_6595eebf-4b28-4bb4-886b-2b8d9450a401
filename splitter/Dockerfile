FROM 112623991000.dkr.ecr.us-east-2.amazonaws.com/python:3.10.15

RUN apt-get update && apt-get install -y poppler-utils

WORKDIR /app
COPY models /app/models
COPY pipeline_utils /app/pipeline_utils
COPY splitter/requirements.txt /app/requirements.txt

RUN python3 -m pip install -r requirements.txt

COPY splitter/splitter.py /app/splitter.py

# Start the application
CMD ["python3", "-u", "splitter.py"]
