import io
import traceback
import json
import fitz
import os
import sys
import pika

from minio import Minio
from minio.error import S3Error

from io import Bytes<PERSON>

from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
import uuid
from uuid import UUID
from uuid6 import uuid7

from time import sleep
from datetime import datetime, timezone
from statistics import median

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import Document, Splitted_Document
from pipeline_utils.monitoring import MonitorService, Status
from pipeline_utils.database_connector import DBConnector
from pipeline_utils.rabbitmq_connector import PikaServiceFactory, PikaService
from pipeline_utils.tenant_utils import get_tenant_processing_config, default_tenant_config

# Project and app configuration
PROJECT_NAME = os.environ.get('PROJECT_NAME', '')
APP_NAME = os.environ.get('APP_NAME', '')

# Splitter configuration
MINIMAL_CONFIDENCE_THRESHOLD = float(os.environ.get('MINIMAL_CONFIDENCE_THRESHOLD', '0.5'))

# MinIO configuration
MINIO_URI = os.environ.get('MINIO_URI', '127.0.0.1:9015')
MINIO_ACCESS_KEY = os.environ.get('MINIO_ACCESS_KEY', '')
MINIO_SECRET_KEY = os.environ.get('MINIO_SECRET_KEY', '')
MINIO_FILES_BUCKET = os.environ.get('MINIO_FILES_BUCKET', 'from-sftp')
MINIO_SECURE = os.environ.get('MINIO_SECURE', 'true').lower() == 'true'

# RabbitMQ configuration
RABBITMQ_HOST = os.environ.get('RABBITMQ_HOST', 'localhost')
RABBITMQ_PORT = int(os.environ.get('RABBITMQ_PORT', '5682'))
RABBITMQ_USERNAME = os.environ.get('RABBITMQ_USERNAME', '')
RABBITMQ_PASSWORD = os.environ.get('RABBITMQ_PASSWORD', '')
RABBITMQ_TO_SPLIT_QUEUE_NAME = os.environ.get('RABBITMQ_TO_SPLIT_QUEUE_NAME', 'to_split')
RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME = os.environ.get('RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME', 'to_extract_metadata')

# PostgreSQL configuration
PGSQL_HOST = os.environ.get('PGSQL_HOST', 'localhost')
PGSQL_PORT = int(os.environ.get('PGSQL_PORT', '5438'))
PGSQL_USERNAME = os.environ.get('PGSQL_USERNAME', '')
PGSQL_PASSWORD = os.environ.get('PGSQL_PASSWORD', '')
PGSQL_DB_NAME = os.environ.get('PGSQL_DB_NAME', '')

# Monitoring configuration
API_HOST = os.environ.get('MONITOR_HOST', 'http://_address_here_')
API_PORT = os.environ.get('MONITOR_PORT', '')

Base = declarative_base()

db_connector = DBConnector(
    pgsql_host=PGSQL_HOST,
    pgsql_port=PGSQL_PORT,
    pgsql_username=PGSQL_USERNAME,
    pgsql_password=PGSQL_PASSWORD,
    pgsql_db_name=PGSQL_DB_NAME
)
Base.metadata.create_all(db_connector.get_engine())
session = None

rmq_service_factory = PikaServiceFactory(
    host=RABBITMQ_HOST,
    port=RABBITMQ_PORT,
    username=RABBITMQ_USERNAME,
    password=RABBITMQ_PASSWORD,
    ssl_options=None
)
rmq_service = rmq_service_factory.create_service()
rmq_service.start()

minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)


def enqueue_splitted_document(file_id_str, document_type, tenant_id=None, subtenant_id=None):
    queue_item = {
        'file_id': file_id_str, 
        'document_type': document_type,
        'tenant_id': tenant_id,
        'subtenant_id': subtenant_id
    }
    print(f'queue_item: {queue_item}')
    rmq_service.send_message(RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME, json.dumps(queue_item))


def add_splitted_document_record_to_db(file_id, text_ocr, boundary, parent_document, pages_count, file_size):
    global session
    splitted_document = Splitted_Document()
    splitted_document.uuid = file_id
    splitted_document.file_size = file_size
    splitted_document.parent_document = parent_document
    splitted_document.parent_document_uuid = parent_document.uuid
    splitted_document.parent_document_pages = boundary['page_ranges']
    splitted_document.pages_count = pages_count
    # splitted_document.parent_document_start_page = boundary['start_page']
    # splitted_document.parent_document_end_page = boundary['end_page']
    splitted_document.document_type = boundary["type"]
    splitted_document.classification_confidence = boundary["classification_confidence"]
    splitted_document.status = 'to_extract_metadata'
    splitted_document.text_ocr = text_ocr
    splitted_document.splitted = datetime.now(timezone.utc)
    session.add(splitted_document)
    session.commit()


def get_splitted_document_page_count_from_boundary(boundary):
    """
    Calculates the number of pages a splitted_document would have based on the given boundary.
    The boundary is expected to have 'page_ranges' like: [[start_page, end_page], [start_page2, end_page2], ...]

    Returns:
        int: The total number of pages.
    """
    total_pages = 0
    for page_range in boundary['page_ranges']:
        start_page, end_page = page_range
        total_pages += (end_page - start_page + 1)
    return total_pages


def save_splitted_document(pdf_stream, text_ocr, boundary, parent_document, tenant_id=None, subtenant_id=None):
    file_id = uuid7()
    file_id_str = f'{file_id}'

    pages_count = get_splitted_document_page_count_from_boundary(boundary)

    file_size = pdf_stream.getbuffer().nbytes

    add_splitted_document_record_to_db(file_id, text_ocr, boundary, parent_document, pages_count, file_size)
    minio_client.put_object(MINIO_FILES_BUCKET, file_id_str, pdf_stream, file_size)

    enqueue_splitted_document(file_id_str, boundary["type"], tenant_id, subtenant_id)


def split_document(document_boundaries, in_memory_file, parent_document, tenant_id=None, subtenant_id=None):
    """
    Split the original PDF document into subdocuments based on page boundaries,
    using the `select` method and saving each slice efficiently to reflect true content size.

    Args:
        document_boundaries (list): List of boundary dictionaries containing page ranges.
        in_memory_file (bytes): The original PDF file stream.
        parent_document: Metadata for the parent document, including text_ocr.
        tenant_id (str): Tenant ID for the document.
        subtenant_id (str): Subtenant ID for the document.
    """
    text_ocr = parent_document.text_ocr
    for boundary in document_boundaries:
        pages = []
        for page_range in boundary['page_ranges']:
            start, end = page_range
            # Page numbers in PyMuPDF are 0-indexed
            pages.extend(range(start - 1, end))

        in_memory_file.seek(0)
        doc = fitz.open(stream=in_memory_file, filetype="pdf")

        try:
            doc.select(pages)
        except RuntimeError as e:
            print(f"Error selecting pages {pages} for boundary {boundary}: {e}")
            doc.close()
            continue  # Skip this boundary or handle differently

        pdf_stream = io.BytesIO()
        # Save the document with aggressive cleanup and compression
        doc.save(pdf_stream, garbage=4, deflate=True)
        doc.close()
        pdf_stream.seek(0)

        #  the OCR text for the selected pages
        ocr_text_slice = []
        for pr in boundary['page_ranges']:
            start_page = pr[0] - 1
            end_page = pr[1] - 1
            ocr_text_slice.extend(text_ocr[start_page:end_page + 1])

        save_splitted_document(pdf_stream, ocr_text_slice, boundary, parent_document, tenant_id, subtenant_id)


def check_for_first_page(page_class, page_text):
    # Return True if it is the beggining of the document, False otherwise
    if page_class == 'RFA':
        first_page_substrings = [
            "REQUEST FOR AUTHORIZATION"
        ]
        if any(sub in text[-1][0] for sub in first_page_substrings for text in page_text):
            return True
    return False


def check_for_last_page(page_class, page_text):
    # Return True if it is the end of the current document, False otherwise
    # Implement your logic here
    return False


def check_for_page_spacer(current_class, page_class, page_text, confidence):
    # Return True if it is a blank/spacer page, False otherwise
    # Implement your logic here
    return False


def get_tenant_splitting_config(tenant_id=None, subtenant_id=None):
    """
    Get tenant-specific splitting configuration.
    
    Args:
        tenant_id: Tenant ID
        subtenant_id: Subtenant ID
    
    Returns:
        Dictionary containing splitting configuration
    """
    if not tenant_id:
        return {
            'confidence_threshold': MINIMAL_CONFIDENCE_THRESHOLD,
            'enable_first_page_detection': True,
            'enable_last_page_detection': True,
            'enable_spacer_detection': True
        }
    
    try:
        processing_config = get_tenant_processing_config(tenant_id, subtenant_id)
        tenant_config = default_tenant_config.get_tenant_config(tenant_id, subtenant_id)
        
        return {
            'confidence_threshold': processing_config.get('minimum_classification_confidence', MINIMAL_CONFIDENCE_THRESHOLD),
            'enable_first_page_detection': tenant_config.get('document_types', {}).get('enable_first_page_detection', True),
            'enable_last_page_detection': tenant_config.get('document_types', {}).get('enable_last_page_detection', True),
            'enable_spacer_detection': tenant_config.get('document_types', {}).get('enable_spacer_detection', True)
        }
    except Exception as e:
        print(f"Error getting tenant splitting config: {e}")
        return {
            'confidence_threshold': MINIMAL_CONFIDENCE_THRESHOLD,
            'enable_first_page_detection': True,
            'enable_last_page_detection': True,
            'enable_spacer_detection': True
        }


def get_document_boundaries(document, tenant_id=None, subtenant_id=None):
    ######################################################
    # Returns formatted document boundaries.
    # List of dictionaries; dictionary example:
    #   {
    #       'page_ranges': _page_ranges_here_,
    #       'type': _class_here_,
    #       'confidence': _median_of_pages_conf_
    #   }
    #
    # initial_predictions_ml and text_ocr are lists,
    # where 0 index relates to first page,
    # and the last index relates to last page
    ######################################################

    """
    This function processes a given packet to identify and classify the boundaries of individual documents within it.
    It uses initial predictions from a machine learning model and OCR text to determine where each document starts and ends.

    Args:
        document: A document object containing the following attributes:
            - initial_predictions_ml: A list of dictionaries where each dictionary contains the class and confidence of a page.
            - text_ocr: A list of OCR text for each page.
        tenant_id: Tenant ID for tenant-specific processing
        subtenant_id: Subtenant ID for tenant-specific processing

    Returns:
        document_boundaries: A list of dictionaries, each representing the boundaries and classification of an identified document.

    Steps:
    1. Initialize variables to track document boundaries, classifications, and confidence levels.
    2. Iterate through each page's initial prediction and OCR text.
    3. Check the confidence level of each page's classification:
        - If confidence is above the threshold, check if it matches the current document class.
        - If a new document is detected or the current document continues, update the boundary and confidence levels.
        - If confidence is too low, check for spacer pages or unrecognized document types and handle accordingly.
    4. Add identified document boundaries to the list as new documents are detected.
    5. Ensure the last document boundary is added to the list after the last page is processed.

    Example usage:
        boundaries = get_document_boundaries(document, tenant_id, subtenant_id)
        print(boundaries)
    """

    initial_predictions_ml = document.initial_predictions_ml
    text_ocr = document.text_ocr

    if initial_predictions_ml is None:
        raise ValueError(f"initial_predictions_ml is None for document {document.uuid}")
    if text_ocr is None:
        raise ValueError(f"text_ocr is None for document {document.uuid}")

    # Get tenant-specific splitting configuration
    splitting_config = get_tenant_splitting_config(tenant_id, subtenant_id)
    confidence_threshold = splitting_config['confidence_threshold']
    
    print(f"Using tenant-specific splitting config for tenant {tenant_id}: confidence_threshold={confidence_threshold}")

    document_boundaries = []
    previous_document_ended = False
    current_page_range = []
    current_class = None
    current_confidences = []
    current_boundary = {}

    for i, (initial_prediction, page_text) in enumerate(zip(initial_predictions_ml, text_ocr)):
        page_class = initial_prediction['class']
        confidence = initial_prediction['confidence']

        if confidence >= confidence_threshold:
            # Confidence level is good
            if page_class == current_class:
                first_page_triggered = check_for_first_page(page_class, page_text) if splitting_config['enable_first_page_detection'] else False

                if first_page_triggered:
                    # Add previous document boundary to list
                    if current_boundary:
                        document_boundaries.append(current_boundary.copy())
                    # New document boundary begins
                    current_confidences = [confidence]
                    current_page_range = [i + 1, i + 1]
                    current_boundary['page_ranges'] = [current_page_range]
                    current_boundary['type'] = page_class
                    current_boundary['classification_confidence'] = median(current_confidences)
                    current_boundary['overall_confidence'] = current_boundary['classification_confidence']

                elif previous_document_ended:
                    # Add previous document boundary to list
                    document_boundaries.append(current_boundary.copy())
                    # New document boundary begins
                    current_confidences = [confidence]
                    current_page_range = [i + 1, i + 1]
                    current_boundary['page_ranges'] = [current_page_range]
                    current_boundary['type'] = page_class
                    current_boundary['classification_confidence'] = median(current_confidences)
                    current_boundary['overall_confidence'] = current_boundary['classification_confidence']

                else:
                    # Expand current boundary
                    current_page_range[1] = i + 1
                    current_confidences.append(confidence)
                    current_boundary['page_ranges'] = [current_page_range]
                    current_boundary['type'] = page_class
                    current_boundary['classification_confidence'] = median(current_confidences)
                    current_boundary['overall_confidence'] = current_boundary['classification_confidence']

            else:
                # Add previous document boundary to list
                if current_boundary:
                    document_boundaries.append(current_boundary.copy())
                # New document starts
                current_confidences = [confidence]
                current_page_range = [i + 1, i + 1]
                current_boundary['page_ranges'] = [current_page_range]
                current_boundary['type'] = page_class
                current_boundary['classification_confidence'] = median(current_confidences)
                current_boundary['overall_confidence'] = current_boundary['classification_confidence']

            # Update variables
            current_class = page_class
            previous_document_ended = check_for_last_page(page_class, page_text) if splitting_config['enable_last_page_detection'] else False

        else:
            # Confidence level is too low
            spacer_page_triggered = check_for_page_spacer(current_class, page_class, page_text, confidence) if splitting_config['enable_spacer_detection'] else False
            if spacer_page_triggered:
                # This page is spacer and relates to current document
                # Expand current boundary
                current_page_range[1] = i + 1
                current_confidences.append(confidence)
                current_boundary['page_ranges'] = [current_page_range]
                current_boundary['type'] = page_class
                current_boundary['classification_confidence'] = median(current_confidences)
                current_boundary['overall_confidence'] = current_boundary['classification_confidence']

            else:
                # This page has unrecognized document type aka 'Other'
                page_class = 'Other'
                if current_class == page_class:
                    # Current unrecognized document continues
                    current_page_range[1] = i + 1
                    current_confidences.append(confidence)
                    current_boundary['page_ranges'] = [current_page_range]
                    current_boundary['type'] = page_class
                    current_boundary['classification_confidence'] = median(current_confidences)
                    current_boundary['overall_confidence'] = current_boundary['classification_confidence']

                else:
                    # Add previous document boundary to list
                    if current_boundary:
                        document_boundaries.append(current_boundary.copy())
                    # New unrecognized document boundary begins
                    current_confidences = [confidence]
                    current_page_range = [i + 1, i + 1]
                    current_boundary['page_ranges'] = [current_page_range]
                    current_boundary['type'] = page_class
                    current_boundary['classification_confidence'] = median(current_confidences)
                    current_boundary['overall_confidence'] = current_boundary['classification_confidence']

                current_class = 'Other'

        if i + 1 == len(initial_predictions_ml):
            # Last page reached, so Document ends; save latest boundary
            document_boundaries.append(current_boundary)

    from pprint import pprint
    pprint(document_boundaries)

    return document_boundaries


def get_total_page_count_from_all_boundaries(document_boundaries):
    """
    Calculates the total number of pages by summing the page counts of all splitted_documents defined in 'document_boundaries'.

    Each element in document_boundaries is expected to be a dictionary with 'page_ranges' like:
    [
        {'page_ranges': [[start_page, end_page], [start_page2, end_page2], ...], ...},
        {'page_ranges': [[start_pageX, end_pageY], ...]},
        ...
    ]

    Returns:
        int: The total number of pages across all splitted_documents.
    """
    total_pages = 0
    for boundary in document_boundaries:
        for page_range in boundary['page_ranges']:
            start_page, end_page = page_range
            total_pages += (end_page - start_page + 1)
    return total_pages


def update_document_record(document, document_boundaries, pages_count, document_end_page):
    global session
    document.pages_count = pages_count
    document.predictions_ml = document_boundaries
    document.predictions_ml_time = datetime.now(timezone.utc)
    document.status = 'to_extract_metadata'
    document.metadata_ml = None
    document.document_end_page = int(document_end_page) if document_end_page is not None else None
    session.commit()


def get_document(file_id):
    global session
    document = session.query(Document).filter_by(uuid=file_id).first()
    return document


def delete_splitted_documents(document_uuid):
    """
    Deletes all Splitted_Document records associated with a given Document UUID from the database
    and removes their corresponding files from Minio storage.

    Args:
        document_uuid (str): The UUID of the parent document whose splitted documents are to be deleted.

    Steps:
        1. Establish a new session with the database.
        2. Retrieve all Splitted_Document records that are associated with the provided Document UUID.
        3. Delete each of the retrieved Splitted_Document records from the database.
        4. Commit the changes to the database to ensure the records are permanently removed.
        5. For each deleted Splitted_Document, attempt to remove the corresponding file from Minio storage.
            - If there is an error during file removal, print an error message but continue with the process.
        6. If an exception occurs at any point during the database operations, roll back any changes to maintain database integrity and print an error message.
        7. Print a success message if all operations complete without errors.

    Raises:
        Exception: If there is any issue with deleting the records or removing the files, an error message is printed.
    """
    try:
        # Retrieve the Splitted_Document records related to the Document UUID
        splitted_documents = (
            session.query(Splitted_Document)
            .filter(Splitted_Document.parent_document_uuid == document_uuid)
            .all()
        )

        # Delete the Splitted_Document records
        for splitted_doc in splitted_documents:
            session.delete(splitted_doc)

        # Commit the changes to the database
        session.commit()

        # Remove the files from Minio
        for splitted_doc in splitted_documents:
            try:
                minio_client.remove_object(
                    bucket_name=MINIO_FILES_BUCKET,
                    object_name=str(splitted_doc.uuid)
                )
            except Exception as e:
                print(f"Error removing file from Minio: {e}")

        print("Splitted documents and their files deleted successfully.")

    except Exception as e:
        session.rollback()
        print(f"Error deleting splitted documents: {e}")


def get_document_end_page(document_boundaries):
    """
    Returns the start page and the last page from all document boundaries.

    The start_page is always set to 1.
    The end_page is determined by finding the maximum end page value
    from all the page ranges in document_boundaries.

    Args:
        document_boundaries (list): A list of dictionaries where each dictionary contains
                                    a 'page_ranges' key with a list of [start_page, end_page] ranges.

    Returns:
        tuple: containing end_page
    """
    max_end_page = 1  # Initialize maximum end page to 1

    # Iterate over each document boundary
    for boundary in document_boundaries:
        # Iterate over each page range in the current boundary
        for page_range in boundary.get('page_ranges', []):
            # Extract start_page and end_page from the range
            start, end = page_range
            # Update max_end_page if the current end is greater
            if end > max_end_page:
                max_end_page = end

    return max_end_page


def callback(msg_tuple):
    ch, method, properties, body = msg_tuple
    global session

    print(f" [x] Received {body}")
    queue_item = json.loads(body.decode('UTF-8'))

    file_id = queue_item['file_id']
    filename = queue_item['filename']
    # Extract tenant information from the message
    tenant_id = queue_item.get('tenant_id')
    subtenant_id = queue_item.get('subtenant_id')
    
    print(f"Splitting document {filename} for tenant: {tenant_id}, subtenant: {subtenant_id}")

    try:
        session = db_connector.get_session()
        document = get_document(file_id)
        if not document:
            raise Exception("EntryNotFound", f'The document with id {file_id} is not exists in DB')

        document_boundaries = document.predictions_human or get_document_boundaries(document, tenant_id, subtenant_id)
        document_boundaries = sorted(document_boundaries, key=lambda x: min(page[0] for page in x['page_ranges']))

        response = minio_client.get_object(MINIO_FILES_BUCKET, file_id)
        in_memory_file = BytesIO(response.read())

        delete_splitted_documents(file_id)
        # Pass tenant information to the splitting process
        split_document(document_boundaries, in_memory_file, document, tenant_id, subtenant_id)

        pages_count = get_total_page_count_from_all_boundaries(document_boundaries)

        document_end_page = get_document_end_page(document_boundaries)

        update_document_record(document, document_boundaries, pages_count, document_end_page)

        session.close()
        ch.basic_ack(delivery_tag=method.delivery_tag)

    except Exception as e:
        traceback.print_exc()
        session.rollback()
        session.close()


rmq_service.read_messages(RABBITMQ_TO_SPLIT_QUEUE_NAME, callback)

# monitor = MonitorService(PROJECT_NAME, APP_NAME, 'splitter', API_HOST, API_PORT)
# monitor.start_monitoring()
# monitor.update_status(Status.UP)

rmq_service.run()
