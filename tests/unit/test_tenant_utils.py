"""
Unit tests for tenant utilities module.
Tests TenantInfoExtractor and TenantConfig functionality.
"""
import pytest
import re
from unittest.mock import Mock, patch

from pipeline_utils.tenant_utils import (
    TenantInfoExtractor, TenantConfig, ChannelType, TenantExtractionStrategy,
    get_tenant_aware_queue_name, get_tenant_processing_config,
    create_tenant_aware_message, default_tenant_config, default_tenant_extractor
)


class TestTenantInfoExtractor:
    """Tests for TenantInfoExtractor class."""
    
    def test_initialization(self, tenant_extractor_instance):
        """Test TenantInfoExtractor initialization."""
        extractor = tenant_extractor_instance
        assert isinstance(extractor, TenantInfoExtractor)
        assert ChannelType.SFTP in extractor.extraction_patterns
        assert ChannelType.SERVICEBUS_TOPIC in extractor.extraction_patterns
        assert ChannelType.SQS in extractor.extraction_patterns
    
    def test_sftp_folder_structure_extraction(self, tenant_extractor_instance):
        """Test SFTP tenant extraction from folder structure."""
        extractor = tenant_extractor_instance
        
        # Test valid folder structure
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='tenant1/subtenant1/document.pdf'
        )
        assert tenant_id == 'tenant1'
        assert subtenant_id == 'subtenant1'
        
        # Test tenant only (no subtenant)
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='tenant2/document.pdf'
        )
        assert tenant_id == 'tenant2'
        assert subtenant_id is None
        
        # Test invalid structure
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='document.pdf'
        )
        assert tenant_id is None
        assert subtenant_id is None
    
    def test_sftp_filename_pattern_extraction(self, tenant_extractor_instance):
        """Test SFTP tenant extraction from filename pattern."""
        extractor = tenant_extractor_instance
        
        # Test valid filename pattern
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='tenant1_subtenant1_document.pdf',
            strategy=TenantExtractionStrategy.FILENAME_PATTERN
        )
        assert tenant_id == 'tenant1'
        assert subtenant_id == 'subtenant1'
        
        # Test invalid pattern
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='invalid_filename.pdf',
            strategy=TenantExtractionStrategy.FILENAME_PATTERN
        )
        assert tenant_id is None
        assert subtenant_id is None
    
    def test_servicebus_message_properties_extraction(self, tenant_extractor_instance):
        """Test ServiceBus tenant extraction from message properties."""
        extractor = tenant_extractor_instance
        
        # Test valid message properties
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SERVICEBUS_TOPIC,
            message_properties={
                'tenant_id': 'tenant1',
                'subtenant_id': 'subtenant1'
            }
        )
        assert tenant_id == 'tenant1'
        assert subtenant_id == 'subtenant1'
        
        # Test partial properties
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SERVICEBUS_TOPIC,
            message_properties={'tenant_id': 'tenant2'}
        )
        assert tenant_id == 'tenant2'
        assert subtenant_id is None
    
    def test_servicebus_message_body_extraction(self, tenant_extractor_instance):
        """Test ServiceBus tenant extraction from message body."""
        extractor = tenant_extractor_instance
        
        # Test valid message body
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SERVICEBUS_QUEUE,
            message_content={
                'tenantId': 'tenant1',
                'subtenantId': 'subtenant1',
                'sasUri': 'https://example.com/file.pdf'
            },
            strategy=TenantExtractionStrategy.MESSAGE_BODY
        )
        assert tenant_id == 'tenant1'
        assert subtenant_id == 'subtenant1'
    
    def test_sqs_message_extraction(self, tenant_extractor_instance):
        """Test SQS tenant extraction from message attributes and body."""
        extractor = tenant_extractor_instance
        
        # Test message attributes
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SQS,
            message_properties={
                'tenant_id': {'StringValue': 'tenant1'},
                'subtenant_id': {'StringValue': 'subtenant1'}
            }
        )
        assert tenant_id == 'tenant1'
        assert subtenant_id == 'subtenant1'
        
        # Test message body
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SQS,
            message_content={
                'tenantId': 'tenant2',
                'subtenantId': 'subtenant2'
            },
            strategy=TenantExtractionStrategy.MESSAGE_BODY
        )
        assert tenant_id == 'tenant2'
        assert subtenant_id == 'subtenant2'
    
    def test_s3_object_metadata_extraction(self, tenant_extractor_instance):
        """Test S3 tenant extraction from object metadata."""
        extractor = tenant_extractor_instance
        
        # Test object metadata
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.S3,
            object_metadata={
                'tenant-id': 'tenant1',
                'subtenant-id': 'subtenant1'
            }
        )
        assert tenant_id == 'tenant1'
        assert subtenant_id == 'subtenant1'
    
    def test_s3_object_tags_extraction(self, tenant_extractor_instance):
        """Test S3 tenant extraction from object tags."""
        extractor = tenant_extractor_instance
        
        # Test object tags
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.S3,
            object_tags={
                'TenantId': 'tenant1',
                'SubtenantId': 'subtenant1'
            },
            strategy=TenantExtractionStrategy.OBJECT_TAGS
        )
        assert tenant_id == 'tenant1'
        assert subtenant_id == 'subtenant1'
    
    def test_validate_tenant_info(self, tenant_extractor_instance):
        """Test tenant information validation."""
        extractor = tenant_extractor_instance
        
        # Valid tenant info
        assert extractor.validate_tenant_info('tenant1', 'subtenant1') == True
        assert extractor.validate_tenant_info('tenant-1', 'sub_tenant_1') == True
        assert extractor.validate_tenant_info('tenant123', None) == True
        
        # Invalid tenant info
        assert extractor.validate_tenant_info(None, 'subtenant1') == False
        assert extractor.validate_tenant_info('', 'subtenant1') == False
        assert extractor.validate_tenant_info('tenant@invalid', 'subtenant1') == False
        assert extractor.validate_tenant_info('tenant1', 'sub@invalid') == False
    
    def test_generate_tenant_prefix(self, tenant_extractor_instance):
        """Test tenant prefix generation."""
        extractor = tenant_extractor_instance
        
        # With subtenant
        prefix = extractor.generate_tenant_prefix('tenant1', 'subtenant1')
        assert prefix == 'tenant1/subtenant1'
        
        # Without subtenant
        prefix = extractor.generate_tenant_prefix('tenant1')
        assert prefix == 'tenant1'
    
    def test_update_extraction_pattern(self, tenant_extractor_instance):
        """Test updating extraction patterns."""
        extractor = tenant_extractor_instance
        
        # Update SFTP pattern
        new_pattern = r'^(?P<tenant_id>[^-]+)-(?P<subtenant_id>[^-]+)-.*'
        extractor.update_extraction_pattern(
            ChannelType.SFTP,
            TenantExtractionStrategy.FILENAME_PATTERN,
            new_pattern
        )
        
        pattern = extractor.extraction_patterns[ChannelType.SFTP][TenantExtractionStrategy.FILENAME_PATTERN]
        assert pattern == new_pattern
        
        # Test extraction with new pattern
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='tenant1-subtenant1-document.pdf',
            strategy=TenantExtractionStrategy.FILENAME_PATTERN
        )
        assert tenant_id == 'tenant1'
        assert subtenant_id == 'subtenant1'


class TestTenantConfig:
    """Tests for TenantConfig class."""
    
    def test_initialization(self, tenant_config_instance):
        """Test TenantConfig initialization."""
        config = tenant_config_instance
        assert isinstance(config, TenantConfig)
        assert 'processing_rules' in config.default_config
        assert 'queue_routing' in config.default_config
        assert 'storage_settings' in config.default_config
    
    def test_get_default_config(self, tenant_config_instance):
        """Test getting default configuration."""
        config = tenant_config_instance
        
        # Non-existent tenant should return default config
        tenant_config = config.get_tenant_config('nonexistent')
        assert tenant_config == config.default_config
    
    def test_get_tenant_config(self, tenant_config_instance):
        """Test getting tenant-specific configuration."""
        config = tenant_config_instance
        
        # Existing tenant
        tenant_config = config.get_tenant_config('tenant1')
        assert tenant_config['processing_rules']['minimum_classification_confidence'] == 0.8
        assert tenant_config['queue_routing']['use_tenant_specific_queues'] == True
    
    def test_get_subtenant_config(self, tenant_config_instance):
        """Test getting subtenant-specific configuration."""
        config = tenant_config_instance
        
        # Subtenant config should inherit from tenant and override
        subtenant_config = config.get_tenant_config('tenant2', 'subtenant1')
        assert subtenant_config['processing_rules']['minimum_classification_confidence'] == 0.7
        assert subtenant_config['document_types']['enabled_types'] == ['RFA', 'Medical Records']
    
    def test_set_tenant_config(self, tenant_config_instance):
        """Test setting tenant-specific configuration."""
        config = tenant_config_instance
        
        # Set new tenant config
        new_config = {
            'processing_rules': {
                'minimum_classification_confidence': 0.9
            },
            'storage_settings': {
                'use_tenant_specific_buckets': True
            }
        }
        config.set_tenant_config('tenant3', new_config)
        
        # Verify config was set and merged with defaults
        tenant_config = config.get_tenant_config('tenant3')
        assert tenant_config['processing_rules']['minimum_classification_confidence'] == 0.9
        assert tenant_config['storage_settings']['use_tenant_specific_buckets'] == True
        assert tenant_config['queue_routing']['use_tenant_specific_queues'] == False  # Default value
    
    def test_deep_merge(self, tenant_config_instance):
        """Test deep merging of configurations."""
        config = tenant_config_instance
        
        target = {
            'level1': {
                'level2': {
                    'existing_key': 'existing_value',
                    'to_override': 'old_value'
                }
            }
        }
        
        source = {
            'level1': {
                'level2': {
                    'to_override': 'new_value',
                    'new_key': 'new_value'
                },
                'new_level2': 'value'
            }
        }
        
        config._deep_merge(target, source)
        
        assert target['level1']['level2']['existing_key'] == 'existing_value'
        assert target['level1']['level2']['to_override'] == 'new_value'
        assert target['level1']['level2']['new_key'] == 'new_value'
        assert target['level1']['new_level2'] == 'value'
    
    def test_get_processing_rule(self, tenant_config_instance):
        """Test getting specific processing rules."""
        config = tenant_config_instance
        
        # Existing rule for existing tenant
        confidence = config.get_processing_rule('tenant1', 'minimum_classification_confidence')
        assert confidence == 0.8
        
        # Default rule for non-existent tenant
        default_confidence = config.get_processing_rule('nonexistent', 'minimum_classification_confidence')
        assert default_confidence == 0.7  # Default from default_config
    
    def test_should_use_tenant_queues(self, tenant_config_instance):
        """Test tenant queue usage check."""
        config = tenant_config_instance
        
        # Tenant with queue routing enabled
        assert config.should_use_tenant_queues('tenant1') == True
        
        # Tenant with default settings
        assert config.should_use_tenant_queues('nonexistent') == False
    
    def test_get_qa_queue_name(self, tenant_config_instance):
        """Test QA queue name generation."""
        config = tenant_config_instance
        
        # Tenant with queue routing enabled
        queue_name = config.get_qa_queue_name('qa_review', 'tenant1')
        assert queue_name == 'tenant1_qa_review'
        
        # Tenant without queue routing
        queue_name = config.get_qa_queue_name('qa_review', 'nonexistent')
        assert queue_name == 'qa_review'
    
    def test_get_bucket_name(self, tenant_config_instance):
        """Test bucket name generation."""
        config = tenant_config_instance
        
        # Set up tenant with bucket isolation
        config.set_tenant_config('tenant_isolated', {
            'storage_settings': {
                'use_tenant_specific_buckets': True,
                'bucket_prefix': 'isolated'
            }
        })
        
        bucket_name = config.get_bucket_name('documents', 'tenant_isolated')
        assert bucket_name == 'isolated-documents'
        
        # Tenant without bucket isolation
        bucket_name = config.get_bucket_name('documents', 'tenant1')
        assert bucket_name == 'documents'
    
    def test_get_storage_path(self, tenant_config_instance):
        """Test storage path generation."""
        config = tenant_config_instance
        
        # With subtenant
        path = config.get_storage_path('tenant1', 'subtenant1', 'document.pdf')
        assert path == 'tenant1/subtenant1/document.pdf'
        
        # Without subtenant
        path = config.get_storage_path('tenant1', filename='document.pdf')
        assert path == 'tenant1/document.pdf'
        
        # Without filename
        path = config.get_storage_path('tenant1', 'subtenant1')
        assert path == 'tenant1/subtenant1'


class TestUtilityFunctions:
    """Tests for utility functions."""
    
    def test_get_tenant_aware_queue_name(self):
        """Test tenant-aware queue name generation."""
        # With tenant queues enabled
        with patch.object(default_tenant_config, 'get_tenant_config') as mock_config:
            mock_config.return_value = {
                'queue_routing': {'use_tenant_queues': True}
            }
            
            queue_name = get_tenant_aware_queue_name('tenant1', 'to_classify', 'subtenant1')
            assert queue_name == 'tenant1_subtenant1_to_classify'
            
            queue_name = get_tenant_aware_queue_name('tenant1', 'to_classify')
            assert queue_name == 'tenant1_to_classify'
        
        # With tenant queues disabled
        with patch.object(default_tenant_config, 'get_tenant_config') as mock_config:
            mock_config.return_value = {
                'queue_routing': {'use_tenant_queues': False}
            }
            
            queue_name = get_tenant_aware_queue_name('tenant1', 'to_classify')
            assert queue_name == 'to_classify'
        
        # No tenant ID
        queue_name = get_tenant_aware_queue_name(None, 'to_classify')
        assert queue_name == 'to_classify'
    
    def test_get_tenant_processing_config(self):
        """Test tenant processing configuration retrieval."""
        # With tenant ID
        with patch.object(default_tenant_config, 'get_tenant_config') as mock_config:
            mock_config.return_value = {
                'processing_rules': {
                    'minimum_classification_confidence': 0.8,
                    'enable_duplicate_detection': True
                }
            }
            
            config = get_tenant_processing_config('tenant1', 'subtenant1')
            assert config['minimum_classification_confidence'] == 0.8
            assert config['enable_duplicate_detection'] == True
        
        # Without tenant ID
        config = get_tenant_processing_config()
        assert 'minimum_classification_confidence' in config
        assert isinstance(config, dict)
    
    def test_create_tenant_aware_message(self):
        """Test tenant-aware message creation."""
        base_message = {'file_id': '123', 'filename': 'test.pdf'}
        
        # With tenant info
        message = create_tenant_aware_message(base_message, 'tenant1', 'subtenant1')
        assert message['file_id'] == '123'
        assert message['filename'] == 'test.pdf'
        assert message['tenant_id'] == 'tenant1'
        assert message['subtenant_id'] == 'subtenant1'
        
        # Without subtenant
        message = create_tenant_aware_message(base_message, 'tenant1')
        assert message['tenant_id'] == 'tenant1'
        assert 'subtenant_id' not in message
        
        # Without tenant info
        message = create_tenant_aware_message(base_message)
        assert message == base_message


class TestErrorHandling:
    """Tests for error handling and edge cases."""
    
    def test_invalid_channel_type(self, tenant_extractor_instance):
        """Test handling of invalid channel types."""
        extractor = tenant_extractor_instance
        
        # Should handle gracefully without raising exception
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel='invalid_channel',
            file_path='test/path.pdf'
        )
        assert tenant_id is None
        assert subtenant_id is None
    
    def test_none_inputs(self, tenant_extractor_instance):
        """Test handling of None inputs."""
        extractor = tenant_extractor_instance
        
        # None file path
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path=None
        )
        assert tenant_id is None
        assert subtenant_id is None
        
        # None message content
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SERVICEBUS_TOPIC,
            message_content=None
        )
        assert tenant_id is None
        assert subtenant_id is None
    
    def test_malformed_regex_pattern(self, tenant_extractor_instance):
        """Test handling of malformed regex patterns."""
        extractor = tenant_extractor_instance
        
        # Set malformed pattern
        extractor.extraction_patterns[ChannelType.SFTP][TenantExtractionStrategy.FILENAME_PATTERN] = r'[invalid regex('
        
        # Should handle gracefully
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='tenant1_subtenant1_file.pdf',
            strategy=TenantExtractionStrategy.FILENAME_PATTERN
        )
        assert tenant_id is None
        assert subtenant_id is None
    
    def test_empty_config_values(self, tenant_config_instance):
        """Test handling of empty configuration values."""
        config = tenant_config_instance
        
        # Empty config
        config.set_tenant_config('empty_tenant', {})
        tenant_config = config.get_tenant_config('empty_tenant')
        
        # Should still have default values
        assert 'processing_rules' in tenant_config
        assert 'queue_routing' in tenant_config
    
    @patch('pipeline_utils.tenant_utils.logger')
    def test_logging_on_errors(self, mock_logger, tenant_extractor_instance):
        """Test that errors are properly logged."""
        extractor = tenant_extractor_instance
        
        # Test with invalid tenant ID format
        result = extractor.validate_tenant_info('invalid@tenant', 'subtenant')
        assert result == False
        # Verify logging was called (this would need the actual logger implementation)


class TestRealWorldScenarios:
    """Tests for real-world usage scenarios."""
    
    def test_mixed_tenant_data_processing(self, tenant_config_instance):
        """Test processing documents from multiple tenants."""
        config = tenant_config_instance
        
        # Set up multiple tenant configurations
        config.set_tenant_config('healthcare_tenant', {
            'processing_rules': {
                'minimum_classification_confidence': 0.9,
                'enable_language_detection': True
            },
            'document_types': {
                'enabled_types': ['Medical Records', 'RFA']
            }
        })
        
        config.set_tenant_config('legal_tenant', {
            'processing_rules': {
                'minimum_classification_confidence': 0.7,
                'enable_language_detection': False
            },
            'document_types': {
                'enabled_types': ['Legal Correspondence', 'Subpoena']
            }
        })
        
        # Test different tenant configurations
        healthcare_config = config.get_tenant_config('healthcare_tenant')
        legal_config = config.get_tenant_config('legal_tenant')
        
        assert healthcare_config['processing_rules']['minimum_classification_confidence'] == 0.9
        assert legal_config['processing_rules']['minimum_classification_confidence'] == 0.7
        
        assert 'Medical Records' in healthcare_config['document_types']['enabled_types']
        assert 'Legal Correspondence' in legal_config['document_types']['enabled_types']
    
    def test_configuration_inheritance_chain(self, tenant_config_instance):
        """Test complex configuration inheritance scenarios."""
        config = tenant_config_instance
        
        # Set up inheritance chain: tenant -> subtenant -> sub-subtenant
        config.set_tenant_config('parent_tenant', {
            'processing_rules': {'minimum_classification_confidence': 0.8},
            'storage_settings': {'folder_structure': 'tenant_id/subtenant_id'}
        })
        
        config.set_tenant_config('parent_tenant', {
            'processing_rules': {'enable_duplicate_detection': False},
            'document_types': {'enabled_types': ['RFA']}
        }, 'child_subtenant')
        
        # Test inheritance
        parent_config = config.get_tenant_config('parent_tenant')
        child_config = config.get_tenant_config('parent_tenant', 'child_subtenant')
        
        # Child should inherit parent's confidence but override duplicate detection
        assert child_config['processing_rules']['minimum_classification_confidence'] == 0.8
        assert child_config['processing_rules']['enable_duplicate_detection'] == False
        assert child_config['document_types']['enabled_types'] == ['RFA']
        assert child_config['storage_settings']['folder_structure'] == 'tenant_id/subtenant_id' 