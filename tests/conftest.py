"""
Pytest configuration and fixtures for multi-tenant ML pipeline testing.
"""
import os
import sys
import pytest
import tempfile
import shutil
from unittest.mock import Mock, MagicMock
from datetime import datetime, timezone
from uuid import uuid4
from uuid6 import uuid7

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from pipeline_utils.tenant_utils import TenantConfig, TenantInfoExtractor, ChannelType
from pipeline_utils.database_connector import DBConnector
from models import Tenant, Subtenant, IncomingPackage, Document, Splitted_Document


@pytest.fixture(scope="session")
def temp_dir():
    """Create a temporary directory for test files."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def mock_db_connector():
    """Mock database connector for testing."""
    mock_connector = Mock(spec=DBConnector)
    mock_session = Mock()
    mock_connector.get_session.return_value = mock_session
    mock_connector.session_scope.return_value.__enter__ = Mock(return_value=mock_session)
    mock_connector.session_scope.return_value.__exit__ = Mock(return_value=None)
    return mock_connector


@pytest.fixture
def mock_minio_client():
    """Mock MinIO client for testing."""
    mock_client = Mock()
    mock_client.bucket_exists.return_value = True
    mock_client.put_object.return_value = None
    mock_client.get_object.return_value = Mock()
    return mock_client


@pytest.fixture
def mock_rabbitmq_service():
    """Mock RabbitMQ service for testing."""
    mock_service = Mock()
    mock_service.start.return_value = None
    mock_service.stop.return_value = None
    mock_service.send_message.return_value = None
    mock_service.safe_ack.return_value = None
    return mock_service


@pytest.fixture
def sample_tenant_config():
    """Sample tenant configuration for testing."""
    return {
        'processing_rules': {
            'enable_duplicate_detection': True,
            'minimum_classification_confidence': 0.8,
            'enable_language_detection': True,
            'enable_handwriting_detection': True
        },
        'queue_routing': {
            'use_tenant_specific_queues': True,
            'qa_queue_prefix': 'tenant1'
        },
        'storage_settings': {
            'use_tenant_specific_buckets': False,
            'folder_structure': 'tenant_id/subtenant_id'
        },
        'document_types': {
            'enabled_types': ['RFA', 'Medical Records'],
            'disabled_types': ['Other'],
            'duplicate_threshold': 0.9,
            'enable_merging': True
        },
        'metadata_extraction': {
            'enable_summary_extraction': True,
            'required_fields': {
                'namingData': {
                    'claimNumber': True,
                    'patientFirstName': True,
                    'patientLastName': True
                }
            }
        }
    }


@pytest.fixture
def tenant_config_instance():
    """TenantConfig instance for testing."""
    config = TenantConfig()
    # Add some test configurations
    config.set_tenant_config('tenant1', {
        'processing_rules': {
            'minimum_classification_confidence': 0.8
        },
        'queue_routing': {
            'use_tenant_specific_queues': True
        }
    })
    config.set_tenant_config('tenant2', {
        'processing_rules': {
            'minimum_classification_confidence': 0.7
        },
        'document_types': {
            'enabled_types': ['RFA', 'Medical Records']
        }
    }, 'subtenant1')
    return config


@pytest.fixture
def tenant_extractor_instance():
    """TenantInfoExtractor instance for testing."""
    return TenantInfoExtractor()


@pytest.fixture
def sample_tenants():
    """Sample tenant records for testing."""
    return [
        Tenant(
            tenant_id='tenant1',
            tenant_name='Test Tenant 1',
            description='First test tenant',
            contact_email='<EMAIL>',
            active=True,
            tenant_config={'custom_setting': 'value1'}
        ),
        Tenant(
            tenant_id='tenant2',
            tenant_name='Test Tenant 2',
            description='Second test tenant',
            contact_email='<EMAIL>',
            active=True,
            tenant_config={'custom_setting': 'value2'}
        )
    ]


@pytest.fixture
def sample_subtenants():
    """Sample subtenant records for testing."""
    return [
        Subtenant(
            subtenant_id='subtenant1',
            tenant_id='tenant1',
            subtenant_name='Test Subtenant 1',
            description='First test subtenant',
            active=True,
            subtenant_config={'subtenant_setting': 'sub_value1'}
        ),
        Subtenant(
            subtenant_id='subtenant2',
            tenant_id='tenant2',
            subtenant_name='Test Subtenant 2',
            description='Second test subtenant',
            active=True,
            subtenant_config={'subtenant_setting': 'sub_value2'}
        )
    ]


@pytest.fixture
def sample_incoming_package():
    """Sample incoming package for testing."""
    return IncomingPackage(
        uuid=uuid7(),
        original_name='test_document.pdf',
        channel='sftp',
        received_date=datetime.now(timezone.utc),
        status='pending',
        tenant_id='tenant1',
        subtenant_id='subtenant1',
        incoming_data={'source': 'test'}
    )


@pytest.fixture
def sample_document():
    """Sample document for testing."""
    package = IncomingPackage(
        uuid=uuid7(),
        original_name='test_package.pdf',
        channel='sftp',
        received_date=datetime.now(timezone.utc),
        tenant_id='tenant1',
        subtenant_id='subtenant1'
    )
    
    return Document(
        uuid=uuid7(),
        file='test_document.pdf',
        incoming_package=package,
        status='to_classify',
        date=datetime.now(timezone.utc),
        file_size=1.5,
        tenant_id='tenant1',
        subtenant_id='subtenant1'
    )


@pytest.fixture
def sample_splitted_document():
    """Sample splitted document for testing."""
    package = IncomingPackage(
        uuid=uuid7(),
        original_name='test_package.pdf',
        channel='sftp',
        received_date=datetime.now(timezone.utc),
        tenant_id='tenant1',
        subtenant_id='subtenant1'
    )
    
    parent_doc = Document(
        uuid=uuid7(),
        file='test_document.pdf',
        incoming_package=package,
        status='to_split',
        date=datetime.now(timezone.utc),
        file_size=1.5,
        tenant_id='tenant1',
        subtenant_id='subtenant1'
    )
    
    return Splitted_Document(
        uuid=uuid7(),
        parent_document=parent_doc,
        parent_document_uuid=parent_doc.uuid,
        parent_document_pages=[[1, 3]],
        pages_count=3,
        document_type='RFA',
        classification_confidence=0.85,
        status='to_extract_metadata',
        text_ocr=['page 1 text', 'page 2 text', 'page 3 text'],
        tenant_id='tenant1',
        subtenant_id='subtenant1'
    )


@pytest.fixture
def sample_classification_results():
    """Sample classification results for testing."""
    return [
        {"class": "RFA", "confidence": 0.85},
        {"class": "Medical Records", "confidence": 0.92},
        {"class": "Other", "confidence": 0.45}
    ]


@pytest.fixture
def sample_metadata():
    """Sample metadata for testing."""
    return {
        "namingData": {
            "claimNumber": {"value": "CLM12345", "confidence": 0.9, "valid": True, "required": True},
            "patientFirstName": {"value": "John", "confidence": 0.85, "valid": True, "required": True},
            "patientLastName": {"value": "Doe", "confidence": 0.88, "valid": True, "required": True}
        },
        "metaData": {
            "claimant": {
                "firstName": {"value": "John", "confidence": 0.85, "valid": True, "required": True},
                "lastName": {"value": "Doe", "confidence": 0.88, "valid": True, "required": True}
            },
            "claim": {
                "claimNumber": {"value": "CLM12345", "confidence": 0.9, "valid": True, "required": True}
            },
            "isEnglishLanguage": {"value": "Y", "confidence": 0.95, "valid": True, "required": False},
            "isHandwritten": {"value": "N", "confidence": 0.98, "valid": True, "required": False}
        },
        "pagesRef": [[1, 3]]
    }


@pytest.fixture
def sample_ocr_text():
    """Sample OCR text for testing."""
    return [
        ["REQUEST FOR AUTHORIZATION", "Patient Name: John Doe", "Claim Number: CLM12345"],
        ["Medical Records", "Date: 2024-01-15", "Doctor: Dr. Smith"],
        ["Additional information", "Contact: 555-0123"]
    ]


@pytest.fixture
def sample_sftp_paths():
    """Sample SFTP paths for tenant extraction testing."""
    return [
        'tenant1/subtenant1/document.pdf',
        'tenant2/subtenant2/medical_record.pdf',
        'default/default/legacy_doc.pdf',
        'tenant1_subtenant1_filename.pdf',
        'invalid/path/structure.pdf'
    ]


@pytest.fixture
def sample_servicebus_messages():
    """Sample ServiceBus messages for testing."""
    return [
        {
            'body': {
                'sasUri': 'https://example.com/file1.pdf',
                'blobName': 'file1.pdf',
                'tenantId': 'tenant1',
                'subtenantId': 'subtenant1'
            },
            'application_properties': {
                'tenant_id': 'tenant1',
                'subtenant_id': 'subtenant1'
            }
        },
        {
            'body': {
                'sasUri': 'https://example.com/file2.pdf',
                'blobName': 'file2.pdf'
            },
            'application_properties': {}
        }
    ]


@pytest.fixture
def sample_sqs_messages():
    """Sample SQS messages for testing."""
    return [
        {
            'Body': '{"fileUrl": "https://example.com/file1.pdf", "fileName": "file1.pdf", "tenantId": "tenant1", "subtenantId": "subtenant1"}',
            'MessageAttributes': {
                'tenant_id': {'StringValue': 'tenant1'},
                'subtenant_id': {'StringValue': 'subtenant1'}
            }
        },
        {
            'Body': '{"fileUrl": "https://example.com/file2.pdf", "fileName": "file2.pdf"}',
            'MessageAttributes': {}
        }
    ]


@pytest.fixture
def sample_queue_items():
    """Sample queue items for testing."""
    return [
        {
            'file_id': str(uuid7()),
            'filename': 'test_document.pdf',
            'tenant_id': 'tenant1',
            'subtenant_id': 'subtenant1'
        },
        {
            'file_id': str(uuid7()),
            'filename': 'medical_record.pdf',
            'tenant_id': 'tenant2',
            'subtenant_id': 'subtenant2'
        },
        {
            'file_id': str(uuid7()),
            'filename': 'legacy_document.pdf'
            # No tenant info - should use defaults
        }
    ]


@pytest.fixture(autouse=True)
def clean_environment():
    """Clean environment variables before each test."""
    # Store original values
    original_env = {}
    tenant_related_vars = [
        'DEFAULT_TENANT_ID', 'DEFAULT_SUBTENANT_ID', 'TENANT_EXTRACTION_ENABLED',
        'RABBITMQ_TO_CLASSIFY_QUEUE_NAME', 'RABBITMQ_TO_SPLIT_QUEUE_NAME',
        'RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME', 'RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME',
        'RABBITMQ_TO_VALIDATE_QUEUE_NAME', 'RABBITMQ_TO_UPLOAD_QUEUE_NAME'
    ]
    
    for var in tenant_related_vars:
        if var in os.environ:
            original_env[var] = os.environ[var]
    
    # Set test defaults
    os.environ['DEFAULT_TENANT_ID'] = 'test_default'
    os.environ['DEFAULT_SUBTENANT_ID'] = 'test_default'
    os.environ['TENANT_EXTRACTION_ENABLED'] = 'true'
    
    yield
    
    # Restore original values
    for var in tenant_related_vars:
        if var in original_env:
            os.environ[var] = original_env[var]
        elif var in os.environ:
            del os.environ[var]


# Test configuration constants
TEST_CONFIG = {
    'DATABASE': {
        'HOST': 'localhost',
        'PORT': 5432,
        'USERNAME': 'test_user',
        'PASSWORD': 'test_password',
        'DATABASE': 'test_db'
    },
    'RABBITMQ': {
        'HOST': 'localhost',
        'PORT': 5672,
        'USERNAME': 'test_user',
        'PASSWORD': 'test_password'
    },
    'MINIO': {
        'URI': 'localhost:9000',
        'ACCESS_KEY': 'test_access',
        'SECRET_KEY': 'test_secret',
        'BUCKET': 'test-bucket'
    }
}


# Helper functions for testing
def create_mock_pdf_bytes():
    """Create mock PDF bytes for testing."""
    return b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n'


def create_test_message(tenant_id=None, subtenant_id=None, **kwargs):
    """Create a test message with optional tenant information."""
    message = {
        'file_id': str(uuid7()),
        'filename': 'test_document.pdf',
        **kwargs
    }
    if tenant_id:
        message['tenant_id'] = tenant_id
    if subtenant_id:
        message['subtenant_id'] = subtenant_id
    return message 