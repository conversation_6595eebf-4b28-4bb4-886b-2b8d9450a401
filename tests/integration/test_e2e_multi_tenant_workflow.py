"""
Integration tests for end-to-end multi-tenant workflows.
Tests complete document processing flows with tenant context preservation.
"""
import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from uuid6 import uuid7

from pipeline_utils.tenant_utils import (
    TenantInfoExtractor, ChannelType, default_tenant_config,
    get_tenant_aware_queue_name, create_tenant_aware_message
)


@pytest.mark.integration
@pytest.mark.tenant_aware
class TestEndToEndMultiTenantWorkflow:
    """Integration tests for complete multi-tenant document processing workflows."""
    
    def test_sftp_tenant_extraction_and_routing(self, tenant_config_instance):
        """Test complete SFTP workflow with tenant extraction and routing."""
        # Configure tenant with specific queue routing
        tenant_config_instance.set_tenant_config('tenant1', {
            'queue_routing': {
                'use_tenant_specific_queues': True,
                'qa_queue_prefix': 'tenant1'
            },
            'processing_rules': {
                'minimum_classification_confidence': 0.9
            }
        })
        
        # Simulate SFTP file processing
        extractor = TenantInfoExtractor()
        
        # Test folder structure extraction
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='tenant1/subtenant1/medical_record.pdf'
        )
        
        assert tenant_id == 'tenant1'
        assert subtenant_id == 'subtenant1'
        
        # Test queue routing with extracted tenant info
        queue_name = get_tenant_aware_queue_name(tenant_id, 'to_classify', subtenant_id)
        assert queue_name == 'tenant1_subtenant1_to_classify'
        
        # Test message creation with tenant info
        base_message = {
            'file_id': str(uuid7()),
            'filename': 'medical_record.pdf'
        }
        
        tenant_message = create_tenant_aware_message(base_message, tenant_id, subtenant_id)
        assert tenant_message['tenant_id'] == 'tenant1'
        assert tenant_message['subtenant_id'] == 'subtenant1'
        assert tenant_message['filename'] == 'medical_record.pdf'
    
    def test_servicebus_multi_tenant_processing(self, tenant_config_instance):
        """Test ServiceBus message processing with tenant information."""
        # Configure different tenant settings
        tenant_config_instance.set_tenant_config('healthcare_tenant', {
            'processing_rules': {
                'minimum_classification_confidence': 0.95,
                'enable_language_detection': True
            },
            'document_types': {
                'enabled_types': ['Medical Records', 'RFA']
            }
        })
        
        extractor = TenantInfoExtractor()
        
        # Simulate ServiceBus message with tenant info in properties
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SERVICEBUS_TOPIC,
            message_properties={
                'tenant_id': 'healthcare_tenant',
                'subtenant_id': 'cardiology'
            },
            message_content={
                'sasUri': 'https://example.com/patient_record.pdf',
                'blobName': 'patient_record.pdf'
            }
        )
        
        assert tenant_id == 'healthcare_tenant'
        assert subtenant_id == 'cardiology'
        
        # Verify tenant-specific configuration is applied
        config = tenant_config_instance.get_tenant_config(tenant_id, subtenant_id)
        assert config['processing_rules']['minimum_classification_confidence'] == 0.95
        assert config['processing_rules']['enable_language_detection'] == True
        assert 'Medical Records' in config['document_types']['enabled_types']
    
    def test_cross_tenant_isolation(self, tenant_config_instance):
        """Test that different tenants maintain proper isolation."""
        # Set up two distinct tenant configurations
        tenant_config_instance.set_tenant_config('tenant_a', {
            'queue_routing': {
                'use_tenant_specific_queues': True
            },
            'processing_rules': {
                'minimum_classification_confidence': 0.8
            },
            'document_types': {
                'enabled_types': ['RFA', 'Medical Records']
            }
        })
        
        tenant_config_instance.set_tenant_config('tenant_b', {
            'queue_routing': {
                'use_tenant_specific_queues': True
            },
            'processing_rules': {
                'minimum_classification_confidence': 0.6
            },
            'document_types': {
                'enabled_types': ['Legal Correspondence', 'Subpoena']
            }
        })
        
        # Test that configurations are isolated
        config_a = tenant_config_instance.get_tenant_config('tenant_a')
        config_b = tenant_config_instance.get_tenant_config('tenant_b')
        
        assert config_a['processing_rules']['minimum_classification_confidence'] == 0.8
        assert config_b['processing_rules']['minimum_classification_confidence'] == 0.6
        
        assert 'RFA' in config_a['document_types']['enabled_types']
        assert 'RFA' not in config_b['document_types']['enabled_types']
        
        assert 'Legal Correspondence' in config_b['document_types']['enabled_types']
        assert 'Legal Correspondence' not in config_a['document_types']['enabled_types']
        
        # Test queue isolation
        queue_a = get_tenant_aware_queue_name('tenant_a', 'to_validate')
        queue_b = get_tenant_aware_queue_name('tenant_b', 'to_validate')
        
        assert queue_a == 'tenant_a_to_validate'
        assert queue_b == 'tenant_b_to_validate'
        assert queue_a != queue_b
    
    def test_tenant_configuration_inheritance(self, tenant_config_instance):
        """Test that subtenant configurations properly inherit from tenant."""
        # Set up parent tenant configuration
        tenant_config_instance.set_tenant_config('parent_tenant', {
            'processing_rules': {
                'minimum_classification_confidence': 0.8,
                'enable_duplicate_detection': True,
                'enable_language_detection': True
            },
            'queue_routing': {
                'use_tenant_specific_queues': True
            },
            'storage_settings': {
                'folder_structure': 'tenant_id/subtenant_id'
            }
        })
        
        # Set up subtenant with partial override
        tenant_config_instance.set_tenant_config('parent_tenant', {
            'processing_rules': {
                'minimum_classification_confidence': 0.9,  # Override parent
                'enable_language_detection': False  # Override parent
            },
            'document_types': {
                'enabled_types': ['RFA']  # New config not in parent
            }
        }, 'child_subtenant')
        
        # Test parent configuration
        parent_config = tenant_config_instance.get_tenant_config('parent_tenant')
        assert parent_config['processing_rules']['minimum_classification_confidence'] == 0.8
        assert parent_config['processing_rules']['enable_duplicate_detection'] == True
        assert parent_config['processing_rules']['enable_language_detection'] == True
        
        # Test child configuration inheritance and overrides
        child_config = tenant_config_instance.get_tenant_config('parent_tenant', 'child_subtenant')
        
        # Should inherit from parent
        assert child_config['processing_rules']['enable_duplicate_detection'] == True
        assert child_config['queue_routing']['use_tenant_specific_queues'] == True
        assert child_config['storage_settings']['folder_structure'] == 'tenant_id/subtenant_id'
        
        # Should override parent values
        assert child_config['processing_rules']['minimum_classification_confidence'] == 0.9
        assert child_config['processing_rules']['enable_language_detection'] == False
        
        # Should have new configuration
        assert child_config['document_types']['enabled_types'] == ['RFA']
    
    @pytest.mark.slow
    def test_multiple_channels_concurrent_processing(self, tenant_config_instance):
        """Test processing documents from multiple channels concurrently with different tenants."""
        # Set up configurations for different channels and tenants
        configurations = {
            'sftp_tenant': {
                'processing_rules': {'minimum_classification_confidence': 0.8}
            },
            'servicebus_tenant': {
                'processing_rules': {'minimum_classification_confidence': 0.7}
            },
            'sqs_tenant': {
                'processing_rules': {'minimum_classification_confidence': 0.9}
            }
        }
        
        for tenant_id, config in configurations.items():
            tenant_config_instance.set_tenant_config(tenant_id, config)
        
        extractor = TenantInfoExtractor()
        
        # Simulate concurrent processing from different channels
        test_scenarios = [
            {
                'channel': ChannelType.SFTP,
                'file_path': 'sftp_tenant/subtenant1/document1.pdf',
                'expected_tenant': 'sftp_tenant',
                'expected_subtenant': 'subtenant1'
            },
            {
                'channel': ChannelType.SERVICEBUS_TOPIC,
                'message_properties': {'tenant_id': 'servicebus_tenant', 'subtenant_id': 'sub1'},
                'expected_tenant': 'servicebus_tenant',
                'expected_subtenant': 'sub1'
            },
            {
                'channel': ChannelType.SQS,
                'message_content': {'tenantId': 'sqs_tenant', 'subtenantId': 'sub2'},
                'expected_tenant': 'sqs_tenant',
                'expected_subtenant': 'sub2'
            }
        ]
        
        results = []
        for scenario in test_scenarios:
            # Extract tenant info based on channel type
            if scenario['channel'] == ChannelType.SFTP:
                tenant_id, subtenant_id = extractor.extract_tenant_info(
                    channel=scenario['channel'],
                    file_path=scenario['file_path']
                )
            elif scenario['channel'] == ChannelType.SERVICEBUS_TOPIC:
                tenant_id, subtenant_id = extractor.extract_tenant_info(
                    channel=scenario['channel'],
                    message_properties=scenario['message_properties']
                )
            elif scenario['channel'] == ChannelType.SQS:
                tenant_id, subtenant_id = extractor.extract_tenant_info(
                    channel=scenario['channel'],
                    message_content=scenario['message_content']
                )
            
            # Verify extraction results
            assert tenant_id == scenario['expected_tenant']
            assert subtenant_id == scenario['expected_subtenant']
            
            # Get tenant-specific configuration
            config = tenant_config_instance.get_tenant_config(tenant_id, subtenant_id)
            
            results.append({
                'tenant_id': tenant_id,
                'subtenant_id': subtenant_id,
                'channel': scenario['channel'],
                'confidence_threshold': config['processing_rules']['minimum_classification_confidence']
            })
        
        # Verify all scenarios processed with correct tenant isolation
        assert len(results) == 3
        assert results[0]['confidence_threshold'] == 0.8  # SFTP tenant
        assert results[1]['confidence_threshold'] == 0.7  # ServiceBus tenant
        assert results[2]['confidence_threshold'] == 0.9  # SQS tenant


@pytest.mark.integration
@pytest.mark.compatibility
class TestBackwardCompatibility:
    """Tests for backward compatibility with existing single-tenant deployments."""
    
    def test_default_tenant_fallback(self, tenant_config_instance):
        """Test that missing tenant information falls back to default tenant."""
        extractor = TenantInfoExtractor()
        
        # Test with no tenant information (legacy scenario)
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='legacy_document.pdf'  # No tenant path structure
        )
        
        # Should return None for extraction, but system should handle gracefully
        assert tenant_id is None
        assert subtenant_id is None
        
        # Test default configuration retrieval
        default_config = tenant_config_instance.get_tenant_config('nonexistent_tenant')
        
        # Should return default configuration
        assert 'processing_rules' in default_config
        assert 'queue_routing' in default_config
        assert default_config['queue_routing']['use_tenant_specific_queues'] == False
    
    def test_legacy_queue_routing(self):
        """Test that legacy deployments continue to use standard queues."""
        # Test without tenant ID (legacy scenario)
        queue_name = get_tenant_aware_queue_name(None, 'to_classify')
        assert queue_name == 'to_classify'
        
        # Test with tenant ID but tenant queues disabled
        with patch.object(default_tenant_config, 'get_tenant_config') as mock_config:
            mock_config.return_value = {
                'queue_routing': {'use_tenant_queues': False}
            }
            
            queue_name = get_tenant_aware_queue_name('some_tenant', 'to_classify')
            assert queue_name == 'to_classify'
    
    def test_legacy_message_format(self):
        """Test that legacy messages without tenant info are handled correctly."""
        # Legacy message format
        legacy_message = {
            'file_id': str(uuid7()),
            'filename': 'legacy_document.pdf'
        }
        
        # Should not modify the message if no tenant info provided
        tenant_message = create_tenant_aware_message(legacy_message)
        assert tenant_message == legacy_message
        assert 'tenant_id' not in tenant_message
        assert 'subtenant_id' not in tenant_message 