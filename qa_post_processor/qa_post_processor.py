import base64
import io

import psycopg2
import os
import json
import sys
import yaml
import fitz
import pika
import ssl
import urllib3
import re

from io import Bytes<PERSON>
from minio import Minio
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.orm import joinedload
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy_utils import UUIDType
from uuid6 import uuid7
from datetime import datetime
from datetime import timezone
from pprint import pprint
from cryptography.fernet import Fernet, InvalidToken
import re
from uuid import UUID

from pipeline_utils.rabbitmq_connector import PikaServiceFactory
from pipeline_utils.database_connector import DBConnector

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import IncomingPackage
from models import Document
from models import Splitted_Document
from pipeline_utils.monitoring import MonitorService, Status

# Project and app configuration
PROJECT_NAME = os.environ.get('PROJECT_NAME', '')
APP_NAME = os.environ.get('APP_NAME', '')

# PostgreSQL configuration
PGSQL_HOST = os.environ.get('PGSQL_HOST', 'localhost')
PGSQL_PORT = int(os.environ.get('PGSQL_PORT', '5438'))
PGSQL_USERNAME = os.environ.get('PGSQL_USERNAME', '')
PGSQL_PASSWORD = os.environ.get('PGSQL_PASSWORD', '')
PGSQL_DB_NAME = os.environ.get('PGSQL_DB_NAME', '')

# RabbitMQ configuration
RABBITMQ_HOST = os.environ.get('RABBITMQ_HOST', 'localhost')
RABBITMQ_PORT = int(os.environ.get('RABBITMQ_PORT', '5682'))
RABBITMQ_USERNAME = os.environ.get('RABBITMQ_USERNAME', '')
RABBITMQ_PASSWORD = os.environ.get('RABBITMQ_PASSWORD', '')
RABBITMQ_TO_UPLOAD_QUEUE_NAME = os.environ.get('RABBITMQ_TO_UPLOAD_QUEUE_NAME', 'to_upload')
RABBITMQ_TO_QA_POSTPROCESS_QUEUE_NAME = os.environ.get('RABBITMQ_TO_QA_POSTPROCESS_QUEUE_NAME', 'to_qa_postprocess')

# Remote RabbitMQ configuration
REMOTE_RABBITMQ_HOST = os.environ.get('REMOTE_RABBITMQ_HOST', '')
REMOTE_RABBITMQ_PORT = int(os.environ.get('REMOTE_RABBITMQ_PORT', '5672'))
REMOTE_RABBITMQ_VHOST = os.environ.get('REMOTE_RABBITMQ_VHOST', '/')
REMOTE_RABBITMQ_USERNAME = os.environ.get('REMOTE_RABBITMQ_USERNAME', '')
REMOTE_RABBITMQ_PASSWORD = os.environ.get('REMOTE_RABBITMQ_PASSWORD', '')
REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME = os.environ.get('REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME', 'from_backend_qa')
TENANT_NAME = os.environ.get('TENANT_NAME', '')

# MinIO configuration
MINIO_URI = os.environ.get('MINIO_URI', '127.0.0.1:9015')
MINIO_ACCESS_KEY = os.environ.get('MINIO_ACCESS_KEY', '')
MINIO_SECRET_KEY = os.environ.get('MINIO_SECRET_KEY', '')
MINIO_FILES_BUCKET = os.environ.get('MINIO_FILES_BUCKET', 'from-sftp')
MINIO_OBJECT_URI_PREFIX = os.environ.get('MINIO_OBJECT_URI_PREFIX', '')
MINIO_SECURE = os.environ.get('MINIO_SECURE', 'true').lower() == 'true'

# Monitoring configuration
API_HOST = os.environ.get('MONITOR_HOST', 'http://_address_here_')
API_PORT = os.environ.get('MONITOR_PORT', '')

# Cryptography configuration
KEY = os.environ.get('CRYPTOGRAPHY_KEY', '')

Base = declarative_base()

db_connector = DBConnector(
    pgsql_host=PGSQL_HOST,
    pgsql_port=PGSQL_PORT,
    pgsql_username=PGSQL_USERNAME,
    pgsql_password=PGSQL_PASSWORD,
    pgsql_db_name=PGSQL_DB_NAME
)
Base.metadata.create_all(db_connector.get_engine())
session = None

httpClient = urllib3.PoolManager()

minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)

REMOTE_SSL_CAFILE_PATH = os.environ.get('REMOTE_SSL_CAFILE_PATH', '')
REMOTE_SSL_CERTFILE_PATH = os.environ.get('REMOTE_SSL_CERTFILE_PATH', '')
REMOTE_SSL_KEYFILE_PATH = os.environ.get('REMOTE_SSL_KEYFILE_PATH', '')

context = None
if REMOTE_SSL_CAFILE_PATH and REMOTE_SSL_CERTFILE_PATH and REMOTE_SSL_KEYFILE_PATH:
    try:
        context = ssl.create_default_context(cafile=REMOTE_SSL_CAFILE_PATH)
        context.load_cert_chain(certfile=REMOTE_SSL_CERTFILE_PATH,
                                keyfile=REMOTE_SSL_KEYFILE_PATH)
        print("SSL context for RabbitMQ configured successfully.")
    except Exception as e:
        print(f"Error configuring SSL context for RabbitMQ: {e}")
        context = None
else:
    print("SSL configuration not found in config. Proceeding without SSL for RabbitMQ.")

# **THIS is the important line** – only wrap the context when it exists
ssl_options = pika.SSLOptions(context) if context else None


remote_rmq_service_factory = PikaServiceFactory(
    host=REMOTE_RABBITMQ_HOST,
    port=REMOTE_RABBITMQ_PORT,
    virtual_host=REMOTE_RABBITMQ_VHOST,
    username=REMOTE_RABBITMQ_USERNAME,
    password=REMOTE_RABBITMQ_PASSWORD,
    ssl_options=ssl_options
)
remote_rmq_service = remote_rmq_service_factory.create_service()
remote_rmq_service.start()

from_backend_qa_queue_name = f"{TENANT_NAME}_queue_{REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME}"
print("""Listening to queue: """ + from_backend_qa_queue_name)

def delete_splitted_documents(document_uuid):
    global session

    try:
        # Retrieve the Splitted_Document records related to the Document UUID
        splitted_documents = (
            session.query(Splitted_Document)
            .filter(Splitted_Document.parent_document_uuid == document_uuid)
            .all()
        )

        # Delete the Splitted_Document records
        for splitted_doc in splitted_documents:
            session.delete(splitted_doc)

        # Commit the changes to the database
        session.commit()

        # Remove the files from Minio
        for splitted_doc in splitted_documents:
            try:
                minio_client.remove_object(
                    bucket_name=MINIO_FILES_BUCKET,
                    object_name=str(splitted_doc.uuid)
                )
            except Exception as e:
                print(f"Error removing file from Minio: {e}")

        print("Splitted documents and their files deleted successfully.")

    except Exception as e:
        session.rollback()
        print(f"Error deleting splitted documents: {e}")


def get_parent_document(parent_document_file_id):
    global session

    document = session.query(Document).filter_by(uuid=parent_document_file_id).first()
    return document


def enqueue_splitted_document(file_id_str, document_type):
    """
    Publishes a message to the specified RabbitMQ queue with the given file ID and document type.

    Args:
        file_id_str (str): The unique identifier of the file to be processed.
        document_type (str): The type of document associated with the file.
    """
    queue_item = {'file_id': file_id_str, 'document_type': document_type}
    rmq_service_factory = PikaServiceFactory(
        host=RABBITMQ_HOST,
        port=RABBITMQ_PORT,
        username=RABBITMQ_USERNAME,
        password=RABBITMQ_PASSWORD,
        ssl_options=None
    )
    rmq_service = rmq_service_factory.create_service()
    rmq_service.start()
    try:
        rmq_service.send_message(routing_key=RABBITMQ_TO_UPLOAD_QUEUE_NAME, message=json.dumps(queue_item))
        print("Send message to RabbitMQ")
    finally:
        rmq_service.stop()


def add_splitted_document_record_to_db(file_id, splitted_document_metadata, parent_document):
    global session
    splitted_document = Splitted_Document()
    splitted_document.uuid = file_id
    splitted_document.parent_document_uuid = parent_document.uuid
    splitted_document.parent_document_pages = splitted_document_metadata['pagesRef']
    splitted_document.parent_document = parent_document
    splitted_document.document_type = splitted_document_metadata[
        "docType"] if "docType" in splitted_document_metadata else "Other"
    splitted_document.metadata_human = splitted_document_metadata
    splitted_document.status = 'to_upload'
    splitted_document.splitted = datetime.now(timezone.utc)
    session.add(splitted_document)
    session.commit()


def add_splitted_document_to_minio(pdf_stream, file_id_str):
    file_size = pdf_stream.getbuffer().nbytes
    minio_client.put_object(MINIO_FILES_BUCKET, file_id_str, pdf_stream, file_size)


def save_splitted_document(pdf_stream, splitted_document_metadata, parent_document):
    file_id = uuid7()
    file_id_str = f'{file_id}'

    add_splitted_document_record_to_db(file_id, splitted_document_metadata, parent_document)
    add_splitted_document_to_minio(pdf_stream, file_id_str)


def create_new_splitted_documents(parent_document_in_memory_file, parent_document, parent_document_data):
    """
    Creates new splitted documents from a parent PDF document based on provided metadata.

    Args:
        parent_document_in_memory_file (BytesIO): A BytesIO object containing the in-memory representation of the parent PDF document.
        parent_document (Document): The parent document object which contains metadata and text OCR information.
        parent_document_data (dict): A dictionary containing metadata for the parent document, including 'metadata_human' which has details for splitting the document.

    Steps:
        1. Open the in-memory PDF file using PyMuPDF (fitz).
        2. Iterate over each set of splitted document metadata in the parent document data's 'metadata' field.
        3. For each set of splitted document metadata:
            a. Create a list of page indices to extract.
            b. Open the parent document again and select only the specified pages.
            c. Save the selected pages to a new in-memory PDF stream with compression and cleanup.
            d. Pass the new subdocument and metadata to the save_splitted_document function.
    """

    for splitted_document_metadata in parent_document_data["metadata"]:
        pages = []
        splitted_document_metadata['pagesRef'].sort(key=lambda x: x[0])
        for page_range in splitted_document_metadata['pagesRef']:
            start_page = int(page_range[0])
            end_page = int(page_range[1])
            pages.extend(range(start_page - 1, end_page))
        parent_document_in_memory_file.seek(0)
        doc = fitz.open(stream=parent_document_in_memory_file, filetype="pdf")
        try:
            doc.select(pages)
        except RuntimeError as e:
            print(f"Error selecting pages {pages} for splitted_document_metadata {splitted_document_metadata}: {e}")
            doc.close()
            continue  # Skip this one if selection fails
        pdf_stream = io.BytesIO()
        doc.save(pdf_stream, garbage=4, deflate=True)
        doc.close()
        pdf_stream.seek(0)
        save_splitted_document(pdf_stream, splitted_document_metadata, parent_document)


def get_all_splitted_documents(parent_document):
    global session
    # target IncomingPackage UUID
    target_uuid = parent_document.uuid

    # Query to find all Splitted_Document items related to the IncomingPackage with the given UUID
    splitted_documents = session.query(Splitted_Document). \
        join(Document, Document.uuid == Splitted_Document.parent_document_uuid). \
        options(joinedload(Splitted_Document.parent_document)). \
        filter(Document.uuid == target_uuid). \
        all()

    return splitted_documents


def enqueue_to_upload(parent_document):
    global session
    splitted_documents = get_all_splitted_documents(parent_document)

    for splitted_document in splitted_documents:
        splitted_document.status = "to_upload"
        enqueue_splitted_document(str(splitted_document.uuid), splitted_document.document_type)
    session.commit()


def decrypt_token(encrypted_token, key):
    """
    Decrypt the encrypted token and extract parent_document_uuid and timestamp.
    """
    try:
        cipher = Fernet(key)
        decrypted_token = cipher.decrypt(encrypted_token.encode()).decode()
        parts = decrypted_token.split('-')
        if len(parts) >= 2:
            parent_document_uuid = '-'.join(parts[:-1])
            timestamp = parts[-1]
        else:
            print("Invalid token format.")
            return None, None
        return parent_document_uuid, timestamp
    except InvalidToken:
        print("Invalid token.")
        return None, None


def find_package_by_id(decoded_string, session):
    """
    Search for a matching package_id (UUID) among all IncomingPackage records.

    Args:
        decoded_string (str): The decoded string from the token containing package_id, timestamp, and UUID.
        session: The SQLAlchemy session for database queries.

    Returns:
        bool: True if a matching IncomingPackage is found, False otherwise.
    """
    # Extract package_id from the decoded string
    package_id = decoded_string.split('-')[0]

    # Query the database to find the IncomingPackage with the matching uuid
    package_exists = session.query(IncomingPackage).filter_by(uuid=package_id).first() is not None

    return package_exists


def callback(msg_tuple):
    ch, method, properties, body = msg_tuple
    """
    Function to be called by RabbitMQ consumer loop when a message is received.

    Args:
        ch (BlockingChannel): The channel object.
        method (Method): The method frame with delivery tag and other delivery parameters.
        properties (BasicProperties): The properties of the message.
        body (bytes): The message body, expected to be a JSON string with keys 'documentId' and 'data',
          example: {'documentId': str, 'data': dict}

    Steps:
        1. Decode the JSON string from the message body to extract 'documentId' and 'data'.
        2. Extract 'predictions_human' and 'metadata_human' from the 'data' dictionary.
        3. Print the received parent document ID.
        4. Start a new database session.
        5. Retrieve the parent document associated with 'documentId' from the database.
        6. If the parent document exists:
            a. Delete any existing splitted documents associated with the parent document.
            b. Update the parent document's 'predictions_human' and 'metadata_human'.
            c. Set the parent document's status to 'to_upload'.
            d. Retrieve the parent document file from Minio storage.
            e. Split the parent document into new splitted documents using the provided data.
            f. Enqueue the parent document for upload.
            g. Commit the changes to the database.
            h. Print a success message indicating the document was split and human metadata saved.
        7. If the parent document does not exist, print a message indicating the document was skipped.
        8. Close the database session.
        9. Acknowledge the message as processed.

    Note:
        This function handles the processing of a parent document message, updates its metadata, splits it into new documents, and enqueues it for upload.
    """

    global session

    queue_item = json.loads(body.decode('UTF-8'))

    token = queue_item['token']
    parent_document_id, _ = decrypt_token(token, KEY)
    if not parent_document_id:
        print("Skipping message due to invalid token.")
        ch.basic_ack(delivery_tag=method.delivery_tag)
        return

    print(f" [x] Received parent document id: {parent_document_id}")
    parent_document_data = queue_item['data']
    print("parent_document_data:", parent_document_data)
    parent_document_predictions_human = parent_document_data["predictions"]
    parent_document_metadata_human = parent_document_data["metadata"]

    pprint(parent_document_metadata_human)

    session = db_connector.get_session()
    parent_document = get_parent_document(parent_document_id)
    # parent_document = None
    if parent_document:
        delete_splitted_documents(parent_document_id)
        parent_document.predictions_human = parent_document_predictions_human
        parent_document.metadata_human = parent_document_metadata_human
        parent_document.status = 'to_upload'
        response = minio_client.get_object(MINIO_FILES_BUCKET, parent_document_id)
        parent_document_in_memory_file = BytesIO(response.read())

        create_new_splitted_documents(parent_document_in_memory_file, parent_document, parent_document_data)
        enqueue_to_upload(parent_document)

        session.commit()
        print("Document splitted, human metadata saved.")
    else:
        print("No parent document, skipped.")

    session.close()

    ch.basic_ack(delivery_tag=method.delivery_tag)
    # connection.close() # if uncomment, process only one message


remote_rmq_service.read_messages(from_backend_qa_queue_name, callback)
# monitor = MonitorService(PROJECT_NAME, APP_NAME, 'qa_postprocessor', API_HOST, API_PORT)
# monitor.start_monitoring()
# monitor.update_status(Status.UP)
remote_rmq_service.run()
