#!/usr/bin/env python3
"""
Simple test runner for multi-tenant ML pipeline tests.
Can be used as a fallback when pytest is not available.
"""
import sys
import os
import traceback
import importlib.util
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_module_from_path(module_name, file_path):
    """Load a Python module from a file path."""
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

def run_test_method(test_class, method_name, fixtures=None):
    """Run a single test method with optional fixtures."""
    if fixtures is None:
        fixtures = {}
    
    try:
        # Create instance of test class
        instance = test_class()
        
        # Get the test method
        test_method = getattr(instance, method_name)
        
        # Call with fixtures if the method accepts them
        import inspect
        sig = inspect.signature(test_method)
        
        if len(sig.parameters) > 1:  # More than just 'self'
            # Try to provide fixtures based on parameter names
            kwargs = {}
            for param_name in sig.parameters:
                if param_name != 'self' and param_name in fixtures:
                    kwargs[param_name] = fixtures[param_name]
            test_method(**kwargs)
        else:
            test_method()
        
        return True, None
    except Exception as e:
        return False, str(e)

def create_basic_fixtures():
    """Create basic fixtures for testing."""
    from pipeline_utils.tenant_utils import TenantConfig, TenantInfoExtractor
    
    fixtures = {}
    
    # Tenant config instance
    tenant_config = TenantConfig()
    tenant_config.set_tenant_config('tenant1', {
        'processing_rules': {
            'minimum_classification_confidence': 0.8
        },
        'queue_routing': {
            'use_tenant_specific_queues': True
        }
    })
    fixtures['tenant_config_instance'] = tenant_config
    
    # Tenant extractor instance
    fixtures['tenant_extractor_instance'] = TenantInfoExtractor()
    
    return fixtures

def discover_and_run_tests():
    """Discover and run all test files."""
    test_results = {
        'passed': 0,
        'failed': 0,
        'errors': []
    }
    
    fixtures = create_basic_fixtures()
    
    # Find all test files
    test_dir = project_root / 'tests'
    if not test_dir.exists():
        print("❌ No tests directory found!")
        return test_results
    
    test_files = []
    for test_file in test_dir.rglob('test_*.py'):
        test_files.append(test_file)
    
    print(f"🔍 Found {len(test_files)} test files")
    print("=" * 50)
    
    for test_file in test_files:
        print(f"\n📁 Running tests in {test_file.relative_to(project_root)}")
        
        try:
            # Load the test module
            module_name = test_file.stem
            module = load_module_from_path(module_name, str(test_file))
            
            # Find test classes
            test_classes = []
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    attr_name.startswith('Test') and 
                    attr != type):
                    test_classes.append(attr)
            
            for test_class in test_classes:
                print(f"  🧪 {test_class.__name__}")
                
                # Find test methods
                test_methods = []
                for method_name in dir(test_class):
                    if method_name.startswith('test_'):
                        test_methods.append(method_name)
                
                for method_name in test_methods:
                    success, error = run_test_method(test_class, method_name, fixtures)
                    
                    if success:
                        print(f"    ✅ {method_name}")
                        test_results['passed'] += 1
                    else:
                        print(f"    ❌ {method_name}: {error}")
                        test_results['failed'] += 1
                        test_results['errors'].append({
                            'test': f"{test_class.__name__}.{method_name}",
                            'error': error
                        })
        
        except Exception as e:
            print(f"    💥 Failed to load test file: {e}")
            test_results['errors'].append({
                'test': str(test_file),
                'error': str(e)
            })
    
    return test_results

def main():
    """Main test runner function."""
    print("🚀 Multi-Tenant ML Pipeline Test Runner")
    print("=" * 50)
    
    # Test basic imports
    print("🔧 Testing basic imports...")
    try:
        from pipeline_utils.tenant_utils import TenantConfig, TenantInfoExtractor, ChannelType
        print("  ✅ TenantConfig import successful")
        print("  ✅ TenantInfoExtractor import successful")
        print("  ✅ ChannelType import successful")
    except ImportError as e:
        print(f"  ❌ Import failed: {e}")
        return 1
    
    # Test basic functionality
    print("\n🧪 Testing basic functionality...")
    try:
        config = TenantConfig()
        extractor = TenantInfoExtractor()
        
        # Test basic configuration
        tenant_config = config.get_tenant_config('test_tenant')
        assert 'processing_rules' in tenant_config
        print("  ✅ TenantConfig basic functionality works")
        
        # Test basic extraction
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='tenant1/subtenant1/test.pdf'
        )
        assert tenant_id == 'tenant1'
        assert subtenant_id == 'subtenant1'
        print("  ✅ TenantInfoExtractor basic functionality works")
        
    except Exception as e:
        print(f"  ❌ Basic functionality test failed: {e}")
        traceback.print_exc()
        return 1
    
    # Run discovered tests
    print("\n🔍 Discovering and running tests...")
    results = discover_and_run_tests()
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Passed: {results['passed']}")
    print(f"❌ Failed: {results['failed']}")
    print(f"📊 Total:  {results['passed'] + results['failed']}")
    
    if results['errors']:
        print(f"\n💥 ERRORS ({len(results['errors'])}):")
        for error in results['errors']:
            print(f"  - {error['test']}: {error['error']}")
    
    if results['failed'] == 0:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print(f"\n⚠️  {results['failed']} tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 