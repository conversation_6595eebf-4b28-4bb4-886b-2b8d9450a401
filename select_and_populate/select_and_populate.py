import io
import json
import fitz
import pika
from PIL import Image
import numpy as np
from paddleocr import PaddleOCR
import yaml
import ssl
from six import binary_type
from pipeline_utils.rabbitmq_connector import PikaServiceFactory

# Existing configuration and setup
configuration = yaml.safe_load(open("config.yml"))
REMOTE_RABBITMQ_HOST = configuration["rabbitmq"]["REMOTE_RABBITMQ_HOST"]
REMOTE_RABBITMQ_PORT = configuration["rabbitmq"]["REMOTE_RABBITMQ_PORT"]
REMOTE_RABBITMQ_VHOST = configuration["rabbitmq"]["REMOTE_RABBITMQ_VHOST"]
REMOTE_RABBITMQ_USERNAME = configuration["rabbitmq"]["REMOTE_RABBITMQ_USERNAME"]
REMOTE_RABBITMQ_PASSWORD = configuration["rabbitmq"]["REMOTE_RABBITMQ_PASSWORD"]
REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME = configuration['rabbitmq']['REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME']
REMOTE_RABBITMQ_FROM_BACKEND_SCREENSHOT = configuration['rabbitmq']['REMOTE_RABBITMQ_FROM_BACKEND_SCREENSHOT']
TENANT_NAME = configuration["TENANT_NAME"]

ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False, use_gpu=True)

REMOTE_SSL_CAFILE_PATH  = configuration['rabbitmq'].get('REMOTE_SSL_CAFILE_PATH')
REMOTE_SSL_CERTFILE_PATH = configuration['rabbitmq'].get('REMOTE_SSL_CERTFILE_PATH')
REMOTE_SSL_KEYFILE_PATH  = configuration['rabbitmq'].get('REMOTE_SSL_KEYFILE_PATH')

context = None
if REMOTE_SSL_CAFILE_PATH and REMOTE_SSL_CERTFILE_PATH and REMOTE_SSL_KEYFILE_PATH:
    try:
        context = ssl.create_default_context(cafile=REMOTE_SSL_CAFILE_PATH)
        context.load_cert_chain(certfile=REMOTE_SSL_CERTFILE_PATH,
                                keyfile=REMOTE_SSL_KEYFILE_PATH)
        print("SSL context for RabbitMQ configured successfully.")
    except Exception as e:
        print(f"Error configuring SSL context for RabbitMQ: {e}")
        context = None
else:
    print("SSL configuration not found in config. Proceeding without SSL for RabbitMQ.")

# **THIS is the important line** – only wrap the context when it exists
ssl_options = pika.SSLOptions(context) if context else None

remote_rmq_service_factory = PikaServiceFactory(
    host=REMOTE_RABBITMQ_HOST,
    port=REMOTE_RABBITMQ_PORT,
    virtual_host=REMOTE_RABBITMQ_VHOST,
    username=REMOTE_RABBITMQ_USERNAME,
    password=REMOTE_RABBITMQ_PASSWORD,
    ssl_options=ssl_options,
    heartbeat=800,
)
remote_rmq_service = remote_rmq_service_factory.create_service()
remote_rmq_service.start()


def convert_to_pdf_binary(binary_data):
    """
    Convert binary data to PDF binary. If the data is already a PDF, return it as is.
    If it's an image (PNG, JPEG, or JPG), convert it to PDF.

    Args:
        binary_data (bytes): The input binary data (PDF, PNG, JPEG, or JPG).

    Returns:
        bytes: PDF binary data.

    Raises:
        ValueError: If the binary data is neither a PDF nor a supported image format.
    """
    # Check if it's a PDF
    if binary_data.startswith(b'%PDF'):
        return binary_data

    # Check for PNG
    elif binary_data.startswith(b'\x89PNG\r\n\x1a\n'):
        image = Image.open(io.BytesIO(binary_data))
    # Check for JPEG or JPG
    elif binary_data.startswith(b'\xFF\xD8\xFF'):
        image = Image.open(io.BytesIO(binary_data))
    else:
        raise ValueError("Unsupported file format. Only PDF, PNG, JPEG, and JPG are supported.")

    # Convert image to RGB if needed
    if image.mode in ("RGBA", "P"):
        image = image.convert("RGB")

    # Save image to PDF in memory
    pdf_bytes_io = io.BytesIO()
    image.save(pdf_bytes_io, format='PDF')
    pdf_bytes = pdf_bytes_io.getvalue()
    pdf_bytes_io.close()
    return pdf_bytes


def flatten_ocr_result(ocr_result):
    """
    Flatten the OCR result, extracting only the recognized text.

    Args:
        ocr_result: OCR result from PaddleOCR.

    Returns:
        list: List of recognized text strings.
    """
    # Guard against None or empty results
    if not ocr_result:
        return []

    flattened = []
    for page_result in ocr_result:
        for line in page_result:
            if isinstance(line, list) and len(line) > 1 and isinstance(line[1], tuple):
                flattened.append(line[1][0])  # Extract the text safely
    return flattened


def perform_image_ocr(image_bytes):
    try:
        image = Image.open(io.BytesIO(image_bytes))
        numpydata = np.asarray(image)
        result = ocr.ocr(numpydata, cls=True)
        if not result:
            print("Empty OCR result for image.")
            return []
        return flatten_ocr_result(result)
    except Exception as e:
        print(f"Error during image OCR: {e}")
        return []


def perform_ocr(file):
    mat = fitz.Matrix(300 / 72, 300 / 72)
    try:
        doc = fitz.open(stream=file, filetype="pdf")
        texts = []
        for page in doc:
            pix = page.get_pixmap(matrix=mat)
            img_bytes = pix.tobytes("ppm")
            image = Image.open(io.BytesIO(img_bytes))
            numpydata = np.asarray(image)
            result = ocr.ocr(numpydata, cls=True)
            if result:
                texts.extend(flatten_ocr_result(result))
            else:
                print("Empty OCR result for page.")
        return texts
    except Exception as e:
        print(f"Error during PDF OCR: {e}")
        return []


def callback(msg_tuple):
    """
    Callback function to handle incoming messages and perform OCR.

    Args:
        msg_tuple: Tuple containing channel, method, properties, and body.
    """
    ch, method, properties, body = msg_tuple

    try:
        # Split the message into metadata and binary file (assuming the delimiter is '___')
        metadata_bytes, binary_file = body.split(b'___', 1)

        metadata = json.loads(metadata_bytes.decode('utf-8'))
        print("Received metadata:", metadata)
        screenshot_id = metadata["screenshot_id"]
        client_name = metadata["client_name"]

        print("Binary file signature:", binary_file[:50])  # Debugging output

        if client_name != TENANT_NAME:
            print("Message does not match tenant name. Skipping.")
            remote_rmq_service.safe_ack(ch, method.delivery_tag)
            return

        # Check for known file signatures
        if binary_file.startswith(b'%PDF'):
            filetype = 'pdf'
        elif binary_file.startswith(b'\x89PNG\r\n\x1a\n'):
            filetype = 'png'
        elif binary_file.startswith(b'\xFF\xD8\xFF'):
            filetype = 'jpeg'
        elif all(chr(c).isdigit() or chr(c).isspace() for c in binary_file[:10]):
            filetype = 'binary_text'
        else:
            raise ValueError("Unsupported file format. Only PDF, PNG, JPEG, and JPG are supported.")

        # Handle each file type
        if filetype == 'pdf':
            texts = perform_ocr(binary_file)
        elif filetype in ['png', 'jpeg']:
            texts = perform_image_ocr(binary_file)
        elif filetype == 'binary_text':
            decoded_text = binary_file.decode('utf-8')
            texts = [decoded_text]
        else:
            raise ValueError(f"Unhandled file type: {filetype}")

        # Flatten OCR results
        combined_text = ' '.join(texts)

        if not combined_text:
            try:
                binary_file = convert_to_pdf_binary(binary_file)
                texts = perform_ocr(binary_file)
                combined_text = ' '.join(texts)
            except Exception as e:
                print("Error processing binary file:", e)

        message = {
            "screenshot_id": screenshot_id,
            "ocr_text": combined_text
        }

        # Send message to RabbitMQ
        remote_rmq_service.send_message(
            REMOTE_RABBITMQ_FROM_BACKEND_SCREENSHOT,
            json.dumps(message).encode('utf-8')  # Convert dict to JSON string and encode to bytes
        )

        # Acknowledge message and print success
        remote_rmq_service.safe_ack(ch, method.delivery_tag)
        print("Message successfully sent to RabbitMQ.")

    except Exception as e:
        print("Error processing message:", e)


queue_name: str = f"{TENANT_NAME}_screenshots_{REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME}"
remote_rmq_service.read_messages(queue_name, callback)
remote_rmq_service.run()
