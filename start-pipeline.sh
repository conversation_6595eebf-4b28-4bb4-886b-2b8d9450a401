#!/bin/bash

# Multi-Tenant ML Pipeline Startup Script
# ========================================

set -e

echo "🚀 Starting Multi-Tenant ML Pipeline..."
echo "========================================="

# Load environment variables
if [ -f "docker.env" ]; then
    echo "📋 Loading environment variables from docker.env"
    export $(cat docker.env | grep -v '^#' | xargs)
else
    echo "⚠️  Warning: docker.env not found, using defaults"
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null 2>&1; then
    echo "❌ Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

# Use docker-compose or docker compose based on availability
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    COMPOSE_CMD="docker compose"
fi

echo "🐳 Using: $COMPOSE_CMD"

# Function to wait for service health
wait_for_service() {
    local service=$1
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service to be healthy..."
    
    while [ $attempt -le $max_attempts ]; do
        if $COMPOSE_CMD ps | grep -q "$service.*healthy"; then
            echo "✅ $service is healthy"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            echo "❌ $service failed to become healthy after $max_attempts attempts"
            return 1
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 5
        ((attempt++))
    done
}

# Function to show service status
show_status() {
    echo ""
    echo "📊 Service Status:"
    echo "=================="
    $COMPOSE_CMD ps
    echo ""
}

# Function to show service logs
show_logs() {
    echo ""
    echo "📋 Recent Logs:"
    echo "==============="
    $COMPOSE_CMD logs --tail=10
    echo ""
}

# Parse command line arguments
case "${1:-start}" in
    "start")
        echo "🏗️  Building and starting services..."
        
        # Build and start services
        $COMPOSE_CMD up -d --build
        
        # Wait for core services
        wait_for_service "postgres"
        wait_for_service "rabbitmq" 
        wait_for_service "minio"
        
        echo ""
        echo "🎯 Core infrastructure is ready!"
        echo ""
        
        # Start ML pipeline services
        echo "🔄 Starting ML Pipeline services..."
        $COMPOSE_CMD up -d downloader classifier splitter
        
        # Start backend API
        echo "🌐 Starting Backend API..."
        $COMPOSE_CMD up -d backend-api
        
        # Start monitoring
        echo "📈 Starting Monitoring services..."
        $COMPOSE_CMD up -d prometheus grafana
        
        # Start reverse proxy
        echo "🔧 Starting Nginx reverse proxy..."
        $COMPOSE_CMD up -d nginx
        
        show_status
        
        echo ""
        echo "🎉 Multi-Tenant ML Pipeline is now running!"
        echo "==========================================="
        echo ""
        echo "📡 Service Endpoints:"
        echo "  • Backend API: http://localhost:${BACKEND_API_PORT:-8000}"
        echo "  • API Documentation: http://localhost:${BACKEND_API_PORT:-8000}/docs"
        echo "  • Nginx Proxy: http://localhost:${HTTP_PORT:-80}"
        echo "  • RabbitMQ Management: http://localhost:${RABBITMQ_MANAGEMENT_PORT:-15672}"
        echo "  • MinIO Console: http://localhost:${MINIO_CONSOLE_PORT:-9001}"
        echo "  • Grafana: http://localhost:${GRAFANA_PORT:-3000}"
        echo "  • Prometheus: http://localhost:${PROMETHEUS_PORT:-9090}"
        echo ""
        echo "🔐 Default Credentials:"
        echo "  • RabbitMQ: ${RABBITMQ_USER:-pipeline_user} / ${RABBITMQ_PASSWORD:-pipeline_secure_password_2024}"
        echo "  • MinIO: ${MINIO_ACCESS_KEY:-pipeline_access_key_2024} / ${MINIO_SECRET_KEY:-pipeline_secure_secret_key_2024_very_long}"
        echo "  • Grafana: admin / ${GRAFANA_PASSWORD:-admin_change_in_production}"
        echo ""
        echo "📖 Next Steps:"
        echo "  1. Access the API documentation at http://localhost:${BACKEND_API_PORT:-8000}/docs"
        echo "  2. Create tenants using the tenant management API"
        echo "  3. Configure document processing channels"
        echo "  4. Monitor system health via Grafana dashboards"
        echo ""
        ;;
        
    "stop")
        echo "🛑 Stopping Multi-Tenant ML Pipeline..."
        $COMPOSE_CMD down
        echo "✅ Pipeline stopped"
        ;;
        
    "restart")
        echo "🔄 Restarting Multi-Tenant ML Pipeline..."
        $COMPOSE_CMD down
        $COMPOSE_CMD up -d --build
        echo "✅ Pipeline restarted"
        ;;
        
    "status")
        show_status
        ;;
        
    "logs")
        show_logs
        ;;
        
    "clean")
        echo "🧹 Cleaning up Multi-Tenant ML Pipeline..."
        echo "⚠️  This will remove all containers, volumes, and data!"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            $COMPOSE_CMD down -v --remove-orphans
            docker system prune -f
            echo "✅ Cleanup completed"
        else
            echo "❌ Cleanup cancelled"
        fi
        ;;
        
    "help"|"--help"|"-h")
        echo "Multi-Tenant ML Pipeline Management Script"
        echo "=========================================="
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  start     Start the entire pipeline (default)"
        echo "  stop      Stop all services"
        echo "  restart   Restart all services"
        echo "  status    Show service status"
        echo "  logs      Show recent logs"
        echo "  clean     Remove all containers and volumes"
        echo "  help      Show this help message"
        echo ""
        ;;
        
    *)
        echo "❌ Unknown command: $1"
        echo "Run '$0 help' for usage information"
        exit 1
        ;;
esac 