#!/usr/bin/env python3
"""
Minimal test for tenant utilities functionality.
Tests only the tenant-specific code without requiring full pipeline dependencies.
"""
import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """Test that we can import the core tenant classes without errors."""
    print("🔧 Testing tenant utility imports...")
    
    try:
        # Test individual imports to isolate the issue
        from pipeline_utils.tenant_utils import ChannelType
        print("  ✅ ChannelType import successful")
        
        from pipeline_utils.tenant_utils import TenantExtractionStrategy  
        print("  ✅ TenantExtractionStrategy import successful")
        
        from pipeline_utils.tenant_utils import TenantInfoExtractor
        print("  ✅ TenantInfoExtractor import successful")
        
        from pipeline_utils.tenant_utils import TenantConfig
        print("  ✅ TenantConfig import successful")
        
        return True
    except ImportError as e:
        print(f"  ❌ Import failed: {e}")
        return False

def test_tenant_extractor():
    """Test TenantInfoExtractor functionality."""
    print("\n🧪 Testing TenantInfoExtractor...")
    
    try:
        from pipeline_utils.tenant_utils import TenantInfoExtractor, ChannelType
        
        extractor = TenantInfoExtractor()
        
        # Test SFTP extraction
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SFTP,
            file_path='tenant1/subtenant1/test.pdf'
        )
        
        assert tenant_id == 'tenant1', f"Expected 'tenant1', got '{tenant_id}'"
        assert subtenant_id == 'subtenant1', f"Expected 'subtenant1', got '{subtenant_id}'"
        print("  ✅ SFTP folder structure extraction works")
        
        # Test ServiceBus extraction
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SERVICEBUS_TOPIC,
            message_properties={
                'tenant_id': 'healthcare_tenant',
                'subtenant_id': 'cardiology'
            }
        )
        
        assert tenant_id == 'healthcare_tenant', f"Expected 'healthcare_tenant', got '{tenant_id}'"
        assert subtenant_id == 'cardiology', f"Expected 'cardiology', got '{subtenant_id}'"
        print("  ✅ ServiceBus message properties extraction works")
        
        # Test SQS extraction
        tenant_id, subtenant_id = extractor.extract_tenant_info(
            channel=ChannelType.SQS,
            message_content={
                'tenantId': 'legal_tenant',
                'subtenantId': 'litigation'
            }
        )
        
        assert tenant_id == 'legal_tenant', f"Expected 'legal_tenant', got '{tenant_id}'"
        assert subtenant_id == 'litigation', f"Expected 'litigation', got '{subtenant_id}'"
        print("  ✅ SQS message body extraction works")
        
        # Test validation
        assert extractor.validate_tenant_info('valid_tenant', 'valid_subtenant') == True
        assert extractor.validate_tenant_info('invalid@tenant', 'valid_subtenant') == False
        print("  ✅ Tenant validation works")
        
        return True
        
    except Exception as e:
        print(f"  ❌ TenantInfoExtractor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tenant_config():
    """Test TenantConfig functionality."""
    print("\n🧪 Testing TenantConfig...")
    
    try:
        from pipeline_utils.tenant_utils import TenantConfig
        
        config = TenantConfig()
        
        # Test setting tenant configuration
        config.set_tenant_config('test_tenant', {
            'processing_rules': {
                'minimum_classification_confidence': 0.8
            },
            'queue_routing': {
                'use_tenant_specific_queues': True
            }
        })
        
        # Test getting tenant configuration
        tenant_config = config.get_tenant_config('test_tenant')
        
        assert tenant_config['processing_rules']['minimum_classification_confidence'] == 0.8
        assert tenant_config['queue_routing']['use_tenant_specific_queues'] == True
        print("  ✅ Tenant configuration setting and retrieval works")
        
        # Test configuration inheritance
        config.set_tenant_config('test_tenant', {
            'processing_rules': {
                'enable_duplicate_detection': False
            }
        }, 'test_subtenant')
        
        subtenant_config = config.get_tenant_config('test_tenant', 'test_subtenant')
        
        # Should inherit parent's confidence but override duplicate detection
        assert subtenant_config['processing_rules']['minimum_classification_confidence'] == 0.8
        assert subtenant_config['processing_rules']['enable_duplicate_detection'] == False
        print("  ✅ Configuration inheritance works")
        
        return True
        
    except Exception as e:
        print(f"  ❌ TenantConfig test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_utility_functions():
    """Test utility functions."""
    print("\n🧪 Testing utility functions...")
    
    try:
        from pipeline_utils.tenant_utils import (
            get_tenant_aware_queue_name, 
            create_tenant_aware_message,
            get_tenant_processing_config
        )
        
        # Test queue name generation (basic test without config dependency)
        queue_name = get_tenant_aware_queue_name('test_tenant', 'to_classify', 'test_subtenant')
        # This might return the original queue name if tenant queues are disabled, which is fine
        print(f"  ✅ Queue name generation works: {queue_name}")
        
        # Test message creation
        base_message = {'file_id': '123', 'filename': 'test.pdf'}
        tenant_message = create_tenant_aware_message(base_message, 'test_tenant', 'test_subtenant')
        
        assert tenant_message['tenant_id'] == 'test_tenant'
        assert tenant_message['subtenant_id'] == 'test_subtenant'
        assert tenant_message['file_id'] == '123'
        print("  ✅ Tenant-aware message creation works")
        
        # Test processing config retrieval
        processing_config = get_tenant_processing_config('test_tenant', 'test_subtenant')
        assert isinstance(processing_config, dict)
        print("  ✅ Processing config retrieval works")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Utility functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Minimal Multi-Tenant Pipeline Test")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_tenant_extractor,
        test_tenant_config,
        test_utility_functions
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Total:  {passed + failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! Multi-tenant functionality is working!")
        return 0
    else:
        print(f"\n⚠️  {failed} tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 