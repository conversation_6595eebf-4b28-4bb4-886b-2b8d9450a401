from minio import Minio
from datetime import datetime, timedelta, timezone
import psycopg2
import schedule
import time
import os
import yaml
from google.cloud import storage
from google.cloud.exceptions import NotFound

# Load configuration from file
configuration = yaml.safe_load(open("/app/config.yml"))

# Configurations from the configuration file
MINIO_ACCESS_KEY = configuration['minio']['MINIO_ACCESS_KEY']
MINIO_SECRET_KEY = configuration['minio']['MINIO_SECRET_KEY']
MINIO_URI = configuration['minio']['MINIO_URI']
MINIO_FILES_BUCKET = configuration['minio']['MINIO_FILES_BUCKET']
MINIO_SECURE = configuration['minio']['MINIO_SECURE']

PGSQL_HOST = configuration['pgsql']['PGSQL_HOST']
PGSQL_PORT = configuration['pgsql']['PGSQL_PORT']
PGSQL_USERNAME = configuration['pgsql']['PGSQL_USERNAME']
PGSQL_PASSWORD = configuration['pgsql']['PGSQL_PASSWORD']
PGSQL_DB_NAME = configuration['pgsql']['PGSQL_DB_NAME']
RETENTION_HOURS = int(configuration['pgsql']['RETENTION_HOURS'])

PGSQL_HOST_REMOTE = configuration['pgsql']['PGSQL_HOST_REMOTE']
PGSQL_PORT_REMOTE = configuration['pgsql']['PGSQL_PORT_REMOTE']
PGSQL_USERNAME_REMOTE = configuration['pgsql']['PGSQL_USERNAME_REMOTE']
PGSQL_PASSWORD_REMOTE = configuration['pgsql']['PGSQL_PASSWORD_REMOTE']
PGSQL_DB_NAME_REMOTE = configuration['pgsql']['PGSQL_DB_NAME_REMOTE']
PGSQL_SSL_MODE_REMOTE = configuration['pgsql']['PGSQL_SSL_MODE_REMOTE']
RETENTION_HOURS_REMOTE = configuration['pgsql']['RETENTION_HOURS_REMOTE']

GCP_BUCKET_NAME = configuration["GCP_BUCKET_NAME"]
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = '/app/gcp-key.json'

# Initialize Minio client
minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)

# PostgreSQL connection parameters for local and remote databases
conn_params = {
    'dbname': PGSQL_DB_NAME,
    'user': PGSQL_USERNAME,
    'password': PGSQL_PASSWORD,
    'host': PGSQL_HOST,
    'port': PGSQL_PORT
}

remote_conn_params = {
    'dbname': PGSQL_DB_NAME_REMOTE,
    'user': PGSQL_USERNAME_REMOTE,
    'password': PGSQL_PASSWORD_REMOTE,
    'host': PGSQL_HOST_REMOTE,
    'port': PGSQL_PORT_REMOTE,
    "sslmode": PGSQL_SSL_MODE_REMOTE
}


def list_files_in_gcp_bucket():
    """Lists all the blobs in the GCP bucket."""
    storage_client = storage.Client()
    blobs = storage_client.list_blobs(GCP_BUCKET_NAME)
    for blob in blobs:
        print(blob.name)


def clean_remote_postgresql_force():
    delete_before = datetime.now(timezone.utc) - timedelta(hours=RETENTION_HOURS_REMOTE)
    try:
        conn = psycopg2.connect(**remote_conn_params)
        cursor = conn.cursor()

        # Fetch both id and name_of_file from the database
        cursor.execute("""
            SELECT id, name_of_file FROM raw_incoming_packages
            WHERE received_date < %s
        """, (delete_before,))
        results = cursor.fetchall()

        if not results:
            print(f"No records older than {delete_before}")
            return

        # Extract file names for GCP deletion (optional)
        name_of_files_list = [row[1] for row in results]

        # Attempt to delete files from GCP (optional)
        delete_files_from_gcp_bucket(name_of_files_list)

        # Extract all ids to delete from the database
        all_ids = [str(row[0]) for row in results]

        # Use the uuid[] cast in the SQL query to delete records
        cursor.execute("""
            DELETE FROM raw_incoming_packages
            WHERE id = ANY(%s::uuid[])
            RETURNING id
        """, (all_ids,))
        deleted_files = [row[0] for row in cursor.fetchall()]
        conn.commit()
        print(f"Deleted {len(deleted_files)} records from PostgreSQL regardless of GCP deletion results.")
        print(f"Database cleanup completed for records older than {delete_before}")

    except psycopg2.Error as e:
        print(f"Error executing PostgreSQL queries: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()


# Function to clean files from Minio
def clean_minio(uuid_list):
    deleted_files = []
    failed_files = []
    for uuid in uuid_list:
        try:
            # Delete the document from Minio
            minio_client.remove_object(MINIO_FILES_BUCKET, f'{uuid}')
            deleted_files.append(f'{uuid}')
        except Exception as e:
            print(f"Error deleting from Minio for UUID {uuid}: {e}")
            failed_files.append(f'{uuid}')
    if deleted_files:
        print("Deleted files from Minio:", deleted_files)
    if failed_files:
        print("Failed to delete files from Minio:", failed_files)
    return deleted_files, failed_files


# Function to clean local PostgreSQL database
def clean_postgresql():
    delete_before = datetime.now(timezone.utc) - timedelta(hours=RETENTION_HOURS)
    try:
        conn = psycopg2.connect(**conn_params)
        cursor = conn.cursor()
    except psycopg2.Error as e:
        print(f"Error connecting to PostgreSQL: {e}")
        return

    try:
        # Find old packages
        cursor.execute("""
            SELECT uuid FROM incoming_packages
            WHERE received_date < %s
        """, (delete_before,))
        uuid_list = [row[0] for row in cursor.fetchall()]

        if not uuid_list:
            print("No old packages found. Skipping document cleanup.")
            return

        # Find related documents
        cursor.execute("""
            SELECT uuid FROM documents
            WHERE incoming_package_uuid IN %s
        """, (tuple(uuid_list),))
        document_uuid_list = [row[0] for row in cursor.fetchall()]

        # Find related splitted documents
        cursor.execute("""
            SELECT uuid FROM splitted_documents
            WHERE parent_document_uuid IN %s
        """, (tuple(document_uuid_list),))
        splitted_document_uuid_list = [row[0] for row in cursor.fetchall()]

        # Combine UUIDs for Minio deletion
        uuid_to_delete = document_uuid_list + splitted_document_uuid_list

        # Clean files from Minio
        deleted_files, failed_files = clean_minio(uuid_to_delete)

        if deleted_files:
            # Separate deleted UUIDs into documents and splitted documents
            deleted_document_uuids = [uuid for uuid in document_uuid_list if uuid in deleted_files]
            deleted_splitted_uuids = [uuid for uuid in splitted_document_uuid_list if uuid in deleted_files]

            # Delete from user_documents
            if deleted_document_uuids:
                cursor.execute("""
                    DELETE FROM user_documents
                    WHERE document_uuid IN %s
                """, (tuple(deleted_document_uuids),))
                user_documents_deleted = cursor.rowcount
            else:
                user_documents_deleted = 0

            # Delete from splitted_documents
            if deleted_splitted_uuids:
                cursor.execute("""
                    DELETE FROM splitted_documents
                    WHERE uuid IN %s
                """, (tuple(deleted_splitted_uuids),))
                splitted_documents_deleted = cursor.rowcount
            else:
                splitted_documents_deleted = 0

            # Delete from document_chunk
            if deleted_document_uuids:
                cursor.execute("""
                    DELETE FROM document_chunk
                    WHERE parent_document_uuid IN %s
                """, (tuple(deleted_document_uuids),))
                document_chunk_deleted = cursor.rowcount
            else:
                document_chunk_deleted = 0

            # Delete from documents only if no splitted_documents reference them
            if deleted_document_uuids:
                cursor.execute("""
                    DELETE FROM documents
                    WHERE uuid IN %s
                    AND NOT EXISTS (
                        SELECT 1 FROM splitted_documents
                        WHERE parent_document_uuid = documents.uuid
                    )
                """, (tuple(deleted_document_uuids),))
                documents_deleted = cursor.rowcount
            else:
                documents_deleted = 0

            # Delete from incoming_packages only if no documents reference them
            if uuid_list:
                cursor.execute("""
                    DELETE FROM incoming_packages
                    WHERE uuid IN %s
                    AND NOT EXISTS (
                        SELECT 1 FROM documents
                        WHERE incoming_package_uuid = incoming_packages.uuid
                    )
                """, (tuple(uuid_list),))
                incoming_packages_deleted = cursor.rowcount
            else:
                incoming_packages_deleted = 0

            conn.commit()

            print(
                f"Deleted {user_documents_deleted} user documents, "
                f"{splitted_documents_deleted} splitted documents, "
                f"{document_chunk_deleted} document_chunk records, "
                f"{documents_deleted} documents, and "
                f"{incoming_packages_deleted} incoming packages from PostgreSQL"
            )
        else:
            print("No documents were deleted from PostgreSQL because all Minio deletions failed.")
    except psycopg2.Error as e:
        print(f"Error executing PostgreSQL queries: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()
        print(f"Deleted records older than {delete_before}")


def delete_files_from_gcp_bucket(name_of_files):
    """
    Deletes files from the GCP bucket based on the provided names
    and returns the list of successfully deleted file names.
    """
    storage_client = storage.Client()
    bucket = storage_client.bucket(GCP_BUCKET_NAME)
    successfully_deleted_names = []

    for file in name_of_files:
        blob = bucket.blob(str(file))
        try:
            blob.reload()
            generation_match_precondition = blob.generation
            blob.delete(if_generation_match=generation_match_precondition)
            successfully_deleted_names.append(file)
            print(f"Deleted {file} from GCP bucket.")
        except NotFound:
            pass
        except Exception as e:
            print(f"Failed to delete {file} from GCP bucket: {e}")

    return successfully_deleted_names


def clean_remote_postgresql():
    delete_before = datetime.now(timezone.utc) - timedelta(hours=RETENTION_HOURS_REMOTE)
    try:
        conn = psycopg2.connect(**remote_conn_params)
        cursor = conn.cursor()

        # Retrieve id and name_of_file from the table
        cursor.execute("""
            SELECT id, name_of_file FROM raw_incoming_packages
            WHERE received_date < %s
        """, (delete_before,))
        results = cursor.fetchall()

        if not results:
            print(f"No records older than {delete_before}")
            return

        # Map name_of_file to id for later deletion
        file_to_id_map = {row[1]: row[0] for row in results}
        name_of_files_list = [row[1] for row in results]

        # Delete files from GCP by their names
        successfully_deleted_files = delete_files_from_gcp_bucket(name_of_files_list)

        if successfully_deleted_files:
            # Retrieve the corresponding ids for the successfully deleted files
            successfully_deleted_ids = [
                str(file_to_id_map[f]) for f in successfully_deleted_files if f in file_to_id_map
            ]

            # Delete records from PostgreSQL using the ids
            cursor.execute("""
                DELETE FROM raw_incoming_packages
                WHERE id = ANY(%s::uuid[])
                RETURNING id
            """, (successfully_deleted_ids,))
            deleted_files = [row[0] for row in cursor.fetchall()]
            conn.commit()
            print(f"Deleted {len(deleted_files)} records from PostgreSQL.")
        else:
            print("No files were deleted from GCP, so no records will be deleted from the database.")

        print(f"Database cleanup completed for records older than {delete_before}")

    except psycopg2.Error as e:
        print(f"Error executing PostgreSQL queries: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()


# Combined function to clean both Minio and PostgreSQL (local and remote)
def clean_all():
    try:
        clean_postgresql()
        clean_remote_postgresql()
    except Exception as e:
        print(f"Error during cleaning: {e}")


# Schedule the cleaning job
schedule.every().day.at("00:00").do(clean_all)
schedule.every().day.at("12:00").do(clean_all)

# For testing, you can uncomment the following line:
# list_files_in_gcp_bucket()
clean_all()  # Initial cleanup run

# Main loop to run scheduled tasks
while True:
    try:
        schedule.run_pending()
    except Exception as e:
        print(f"Error in scheduler: {e}")
    time.sleep(1)
