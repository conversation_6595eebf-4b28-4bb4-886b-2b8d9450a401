#!/usr/bin/env python3
"""
Manual migration script to add tenant support to both local and remote databases.
This script should be run after updating the models to include tenant fields.

Usage:
    python migrations/add_tenant_support_migration.py --local-only
    python migrations/add_tenant_support_migration.py --remote-only  
    python migrations/add_tenant_support_migration.py --both (default)
"""

import sys
import os
import argparse
from datetime import datetime

# Add the parent directory to the path so we can import models
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from sqlalchemy import text
from pipeline_utils.database_connector import DBConnector
from models.models import Base, RemoteBase, Tenant, Subtenant

def get_database_connectors():
    """Get database connectors for local and remote databases"""
    local_db_connector = DBConnector(
        pgsql_host=os.environ.get('PGSQL_HOST', 'localhost'),
        pgsql_port=int(os.environ.get('PGSQL_PORT', '5438')),
        pgsql_username=os.environ.get('PGSQL_USERNAME', ''),
        pgsql_password=os.environ.get('PGSQL_PASSWORD', ''),
        pgsql_db_name=os.environ.get('PGSQL_DB_NAME', '')
    )
    
    remote_db_connector = DBConnector(
        pgsql_host=os.environ.get('PGSQL_HOST_REMOTE', ''),
        pgsql_port=int(os.environ.get('PGSQL_PORT_REMOTE', '5432')),
        pgsql_username=os.environ.get('PGSQL_USERNAME_REMOTE', ''),
        pgsql_password=os.environ.get('PGSQL_PASSWORD_REMOTE', ''),
        pgsql_db_name=os.environ.get('PGSQL_DB_NAME_REMOTE', ''),
        pgsql_sslmode=os.environ.get('PGSQL_SSL_MODE_REMOTE', 'require')
    )
    
    return local_db_connector, remote_db_connector

def migrate_local_database(db_connector):
    """Add tenant support to local database"""
    print("Starting local database migration...")
    
    migration_sql = [
        # Create tenants table
        """
        CREATE TABLE IF NOT EXISTS tenants (
            tenant_id VARCHAR(128) PRIMARY KEY,
            tenant_name VARCHAR(256) NOT NULL,
            description VARCHAR(512),
            contact_email VARCHAR(256),
            created_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            active BOOLEAN DEFAULT TRUE,
            tenant_config JSON
        );
        """,
        
        # Create subtenants table
        """
        CREATE TABLE IF NOT EXISTS subtenants (
            subtenant_id VARCHAR(128) PRIMARY KEY,
            tenant_id VARCHAR(128) NOT NULL REFERENCES tenants(tenant_id),
            subtenant_name VARCHAR(256) NOT NULL,
            description VARCHAR(512),
            contact_email VARCHAR(256),
            created_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            active BOOLEAN DEFAULT TRUE,
            subtenant_config JSON
        );
        """,
        
        # Create index on subtenants
        """
        CREATE INDEX IF NOT EXISTS idx_subtenant_tenant ON subtenants(tenant_id, subtenant_id);
        """,
        
        # Add tenant fields to incoming_packages
        """
        ALTER TABLE incoming_packages 
        ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128) REFERENCES tenants(tenant_id),
        ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);
        """,
        
        # Add tenant fields to documents
        """
        ALTER TABLE documents 
        ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128) REFERENCES tenants(tenant_id),
        ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);
        """,
        
        # Add tenant fields to document_chunk
        """
        ALTER TABLE document_chunk 
        ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128) REFERENCES tenants(tenant_id),
        ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);
        """,
        
        # Add tenant fields to splitted_documents
        """
        ALTER TABLE splitted_documents 
        ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128) REFERENCES tenants(tenant_id),
        ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);
        """,
        
        # Add tenant fields to users
        """
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS accessible_tenants JSON,
        ADD COLUMN IF NOT EXISTS default_tenant_id VARCHAR(128);
        """,
        
        # Add tenant fields to user_activity_logs
        """
        ALTER TABLE user_activity_logs 
        ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128),
        ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);
        """,
        
        # Create indexes for incoming_packages
        """
        CREATE INDEX IF NOT EXISTS idx_incoming_package_tenant ON incoming_packages(tenant_id, subtenant_id);
        CREATE INDEX IF NOT EXISTS idx_incoming_package_status_tenant ON incoming_packages(status, tenant_id, subtenant_id);
        """,
        
        # Create indexes for documents
        """
        CREATE INDEX IF NOT EXISTS idx_document_tenant ON documents(tenant_id, subtenant_id);
        CREATE INDEX IF NOT EXISTS idx_document_status_tenant ON documents(status, tenant_id, subtenant_id);
        """,
        
        # Create indexes for document_chunk
        """
        CREATE INDEX IF NOT EXISTS idx_document_chunk_tenant ON document_chunk(tenant_id, subtenant_id);
        CREATE INDEX IF NOT EXISTS idx_document_chunk_status_tenant ON document_chunk(status, tenant_id, subtenant_id);
        """,
        
        # Create indexes for splitted_documents
        """
        CREATE INDEX IF NOT EXISTS idx_splitted_document_tenant ON splitted_documents(tenant_id, subtenant_id);
        CREATE INDEX IF NOT EXISTS idx_splitted_document_status_tenant ON splitted_documents(status, tenant_id, subtenant_id);
        CREATE INDEX IF NOT EXISTS idx_splitted_document_type_tenant ON splitted_documents(document_type, tenant_id, subtenant_id);
        """,
        
        # Create indexes for user_activity_logs
        """
        CREATE INDEX IF NOT EXISTS idx_activity_log_tenant ON user_activity_logs(tenant_id, subtenant_id);
        """
    ]
    
    try:
        with db_connector.session_scope() as session:
            for sql in migration_sql:
                print(f"Executing SQL: {sql[:100]}...")
                session.execute(text(sql))
            print("Local database migration completed successfully!")
    except Exception as e:
        print(f"Error during local database migration: {e}")
        raise

def migrate_remote_database(db_connector):
    """Add tenant support to remote database"""
    print("Starting remote database migration...")
    
    migration_sql = [
        # Add tenant fields to raw_incoming_packages
        """
        ALTER TABLE raw_incoming_packages 
        ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(128),
        ADD COLUMN IF NOT EXISTS subtenant_id VARCHAR(128);
        """,
        
        # Create indexes for raw_incoming_packages
        """
        CREATE INDEX IF NOT EXISTS idx_raw_incoming_tenant ON raw_incoming_packages(tenant_id, subtenant_id);
        CREATE INDEX IF NOT EXISTS idx_raw_incoming_status_tenant ON raw_incoming_packages(status, tenant_id, subtenant_id);
        """
    ]
    
    try:
        with db_connector.session_scope() as session:
            for sql in migration_sql:
                print(f"Executing SQL: {sql[:100]}...")
                session.execute(text(sql))
            print("Remote database migration completed successfully!")
    except Exception as e:
        print(f"Error during remote database migration: {e}")
        raise

def create_default_tenant(db_connector):
    """Create a default tenant for existing data"""
    print("Creating default tenant...")
    
    try:
        with db_connector.session_scope() as session:
            # Check if default tenant already exists
            existing_tenant = session.query(Tenant).filter_by(tenant_id='default').first()
            if not existing_tenant:
                default_tenant = Tenant(
                    tenant_id='default',
                    tenant_name='Default Tenant',
                    description='Default tenant for existing data migration',
                    active=True
                )
                session.add(default_tenant)
                
                # Create default subtenant
                default_subtenant = Subtenant(
                    subtenant_id='default',
                    tenant_id='default',
                    subtenant_name='Default Subtenant',
                    description='Default subtenant for existing data migration',
                    active=True
                )
                session.add(default_subtenant)
                
                print("Default tenant and subtenant created successfully!")
            else:
                print("Default tenant already exists, skipping creation.")
    except Exception as e:
        print(f"Error creating default tenant: {e}")
        raise

def update_existing_data_with_default_tenant(local_db_connector, remote_db_connector):
    """Update existing data to use the default tenant"""
    print("Updating existing data with default tenant...")
    
    update_sql_local = [
        "UPDATE incoming_packages SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;",
        "UPDATE documents SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;",
        "UPDATE document_chunk SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;",
        "UPDATE splitted_documents SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;",
        "UPDATE user_activity_logs SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;"
    ]
    
    update_sql_remote = [
        "UPDATE raw_incoming_packages SET tenant_id = 'default', subtenant_id = 'default' WHERE tenant_id IS NULL;"
    ]
    
    try:
        # Update local database
        with local_db_connector.session_scope() as session:
            for sql in update_sql_local:
                result = session.execute(text(sql))
                print(f"Updated {result.rowcount} rows: {sql[:50]}...")
        
        # Update remote database
        with remote_db_connector.session_scope() as session:
            for sql in update_sql_remote:
                result = session.execute(text(sql))
                print(f"Updated {result.rowcount} rows: {sql[:50]}...")
        
        print("Existing data updated successfully!")
    except Exception as e:
        print(f"Error updating existing data: {e}")
        raise

def main():
    parser = argparse.ArgumentParser(description='Add tenant support to ML pipeline databases')
    parser.add_argument('--local-only', action='store_true', help='Migrate only local database')
    parser.add_argument('--remote-only', action='store_true', help='Migrate only remote database')
    parser.add_argument('--skip-data-update', action='store_true', help='Skip updating existing data with default tenant')
    args = parser.parse_args()
    
    print(f"Starting tenant support migration at {datetime.now()}")
    
    try:
        local_db_connector, remote_db_connector = get_database_connectors()
        
        # Migrate local database
        if not args.remote_only:
            migrate_local_database(local_db_connector)
            create_default_tenant(local_db_connector)
        
        # Migrate remote database
        if not args.local_only:
            migrate_remote_database(remote_db_connector)
        
        # Update existing data
        if not args.skip_data_update and not args.local_only and not args.remote_only:
            update_existing_data_with_default_tenant(local_db_connector, remote_db_connector)
        
        print(f"Migration completed successfully at {datetime.now()}")
        
    except Exception as e:
        print(f"Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 