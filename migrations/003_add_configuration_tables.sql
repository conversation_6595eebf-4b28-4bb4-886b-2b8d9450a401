-- Migration 003: Add configuration and monitoring tables
-- Step 5: Monitoring & Configuration

-- Create tenant_configurations table for storing tenant-specific configurations
CREATE TABLE IF NOT EXISTS tenant_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id VARCHAR(255) NOT NULL,
    subtenant_id VARCHAR(255),
    configuration_key VARCHAR(255) NOT NULL,
    configuration_value JSONB NOT NULL,
    configuration_type VARCHAR(100) NOT NULL DEFAULT 'user', -- user, system, template
    version INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_encrypted BOOLEAN NOT NULL DEFAULT false,
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    modified_by <PERSON><PERSON><PERSON><PERSON>(255),
    description TEXT,
    
    -- Constraints
    CONSTRAINT fk_tenant_configs_tenant 
        FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    CONSTRAINT fk_tenant_configs_subtenant 
        FOREIGN KEY (tenant_id, subtenant_id) REFERENCES subtenants(tenant_id, subtenant_id) ON DELETE CASCADE,
    CONSTRAINT unique_tenant_config_key 
        UNIQUE (tenant_id, subtenant_id, configuration_key, version)
);

-- Create configuration_history table for tracking changes
CREATE TABLE IF NOT EXISTS configuration_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    configuration_id UUID NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    subtenant_id VARCHAR(255),
    configuration_key VARCHAR(255) NOT NULL,
    old_value JSONB,
    new_value JSONB NOT NULL,
    change_type VARCHAR(50) NOT NULL, -- create, update, delete, rollback
    version INTEGER NOT NULL,
    changed_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    changed_by VARCHAR(255),
    change_reason TEXT,
    rollback_configuration_id UUID,
    
    -- Constraints
    CONSTRAINT fk_config_history_config 
        FOREIGN KEY (configuration_id) REFERENCES tenant_configurations(id) ON DELETE CASCADE
);

-- Create monitoring_metrics table for storing historical metrics
CREATE TABLE IF NOT EXISTS monitoring_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id VARCHAR(255) NOT NULL,
    subtenant_id VARCHAR(255),
    component VARCHAR(255),
    metric_name VARCHAR(255) NOT NULL,
    metric_value NUMERIC NOT NULL,
    metric_type VARCHAR(50) NOT NULL DEFAULT 'gauge', -- gauge, counter, histogram, summary
    tags JSONB,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_monitoring_metrics_tenant (tenant_id),
    INDEX idx_monitoring_metrics_timestamp (timestamp),
    INDEX idx_monitoring_metrics_name (metric_name),
    INDEX idx_monitoring_metrics_component (component)
);

-- Create alert_rules table for tenant-specific alert configurations
CREATE TABLE IF NOT EXISTS alert_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id VARCHAR(255) NOT NULL,
    subtenant_id VARCHAR(255),
    rule_name VARCHAR(255) NOT NULL,
    metric_name VARCHAR(255) NOT NULL,
    component VARCHAR(255),
    threshold_value NUMERIC NOT NULL,
    operator VARCHAR(10) NOT NULL DEFAULT 'gt', -- gt, lt, eq, gte, lte, ne
    severity VARCHAR(20) NOT NULL DEFAULT 'warning', -- critical, warning, info
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    evaluation_window_minutes INTEGER NOT NULL DEFAULT 5,
    cooldown_minutes INTEGER NOT NULL DEFAULT 30,
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    description TEXT,
    
    -- Constraints
    CONSTRAINT fk_alert_rules_tenant 
        FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    CONSTRAINT fk_alert_rules_subtenant 
        FOREIGN KEY (tenant_id, subtenant_id) REFERENCES subtenants(tenant_id, subtenant_id) ON DELETE CASCADE,
    CONSTRAINT unique_tenant_alert_rule 
        UNIQUE (tenant_id, subtenant_id, rule_name),
    CONSTRAINT check_operator 
        CHECK (operator IN ('gt', 'lt', 'eq', 'gte', 'lte', 'ne')),
    CONSTRAINT check_severity 
        CHECK (severity IN ('critical', 'warning', 'info'))
);

-- Create notification_channels table for alert notification settings
CREATE TABLE IF NOT EXISTS notification_channels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id VARCHAR(255) NOT NULL,
    subtenant_id VARCHAR(255),
    channel_name VARCHAR(255) NOT NULL,
    channel_type VARCHAR(50) NOT NULL, -- email, webhook, slack, teams, sms
    configuration JSONB NOT NULL,
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    
    -- Constraints
    CONSTRAINT fk_notification_channels_tenant 
        FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    CONSTRAINT fk_notification_channels_subtenant 
        FOREIGN KEY (tenant_id, subtenant_id) REFERENCES subtenants(tenant_id, subtenant_id) ON DELETE CASCADE,
    CONSTRAINT unique_tenant_notification_channel 
        UNIQUE (tenant_id, subtenant_id, channel_name),
    CONSTRAINT check_channel_type 
        CHECK (channel_type IN ('email', 'webhook', 'slack', 'teams', 'sms'))
);

-- Create alert_notifications table for tracking sent notifications
CREATE TABLE IF NOT EXISTS alert_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    alert_id UUID NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    subtenant_id VARCHAR(255),
    notification_channel_id UUID NOT NULL,
    alert_message TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL,
    sent_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    delivery_status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, sent, failed, retry
    delivery_error TEXT,
    retry_count INTEGER NOT NULL DEFAULT 0,
    
    -- Constraints
    CONSTRAINT fk_alert_notifications_channel 
        FOREIGN KEY (notification_channel_id) REFERENCES notification_channels(id) ON DELETE CASCADE,
    CONSTRAINT check_delivery_status 
        CHECK (delivery_status IN ('pending', 'sent', 'failed', 'retry'))
);

-- Create configuration_templates table for reusable configuration templates
CREATE TABLE IF NOT EXISTS configuration_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    template_name VARCHAR(255) NOT NULL UNIQUE,
    template_description TEXT,
    template_category VARCHAR(100) NOT NULL, -- processing, monitoring, storage, etc.
    template_configuration JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    version VARCHAR(50) NOT NULL DEFAULT '1.0'
);

-- Create performance_metrics table for aggregated performance tracking
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id VARCHAR(255) NOT NULL,
    subtenant_id VARCHAR(255),
    component VARCHAR(255) NOT NULL,
    metric_period VARCHAR(20) NOT NULL, -- hour, day, week, month
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    documents_processed INTEGER NOT NULL DEFAULT 0,
    avg_processing_time_seconds NUMERIC,
    total_processing_time_seconds NUMERIC,
    error_count INTEGER NOT NULL DEFAULT 0,
    success_rate NUMERIC,
    storage_usage_bytes BIGINT,
    queue_backlog_avg INTEGER,
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT fk_performance_metrics_tenant 
        FOREIGN KEY (tenant_id) REFERENCES tenants(tenant_id) ON DELETE CASCADE,
    CONSTRAINT unique_performance_period 
        UNIQUE (tenant_id, subtenant_id, component, metric_period, period_start),
    CONSTRAINT check_metric_period 
        CHECK (metric_period IN ('hour', 'day', 'week', 'month'))
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tenant_configurations_tenant ON tenant_configurations(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_configurations_key ON tenant_configurations(configuration_key);
CREATE INDEX IF NOT EXISTS idx_tenant_configurations_active ON tenant_configurations(is_active);
CREATE INDEX IF NOT EXISTS idx_tenant_configurations_type ON tenant_configurations(configuration_type);

CREATE INDEX IF NOT EXISTS idx_configuration_history_config ON configuration_history(configuration_id);
CREATE INDEX IF NOT EXISTS idx_configuration_history_date ON configuration_history(changed_date);
CREATE INDEX IF NOT EXISTS idx_configuration_history_tenant ON configuration_history(tenant_id);

CREATE INDEX IF NOT EXISTS idx_alert_rules_tenant ON alert_rules(tenant_id);
CREATE INDEX IF NOT EXISTS idx_alert_rules_enabled ON alert_rules(is_enabled);
CREATE INDEX IF NOT EXISTS idx_alert_rules_metric ON alert_rules(metric_name);

CREATE INDEX IF NOT EXISTS idx_notification_channels_tenant ON notification_channels(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notification_channels_enabled ON notification_channels(is_enabled);
CREATE INDEX IF NOT EXISTS idx_notification_channels_type ON notification_channels(channel_type);

CREATE INDEX IF NOT EXISTS idx_alert_notifications_alert ON alert_notifications(alert_id);
CREATE INDEX IF NOT EXISTS idx_alert_notifications_status ON alert_notifications(delivery_status);
CREATE INDEX IF NOT EXISTS idx_alert_notifications_date ON alert_notifications(sent_date);

CREATE INDEX IF NOT EXISTS idx_performance_metrics_tenant ON performance_metrics(tenant_id);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_component ON performance_metrics(component);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_period ON performance_metrics(metric_period, period_start);

-- Insert default configuration templates
INSERT INTO configuration_templates (template_name, template_description, template_category, template_configuration) VALUES
('default_processing', 'Default processing configuration for new tenants', 'processing', '{
  "processing_rules": {
    "minimum_classification_confidence": 0.7,
    "enable_ocr": true,
    "enable_handwriting_detection": false,
    "classification_timeout_seconds": 30,
    "splitting_timeout_seconds": 60,
    "metadata_extraction_timeout_seconds": 120
  },
  "document_types": {
    "allowed_types": ["pdf", "tiff", "docx"],
    "max_file_size_mb": 50,
    "enable_document_merging": true,
    "merge_similarity_threshold": 0.8
  }
}'),

('standard_monitoring', 'Standard monitoring configuration', 'monitoring', '{
  "alert_rules": {
    "high_error_rate": {
      "metric": "tenant_error_rate",
      "threshold": 0.1,
      "operator": "gt",
      "severity": "warning"
    },
    "processing_delay": {
      "metric": "tenant_processing_duration_seconds",
      "threshold": 60,
      "operator": "gt",
      "severity": "warning"
    },
    "queue_backlog": {
      "metric": "tenant_queue_size",
      "threshold": 100,
      "operator": "gt",
      "severity": "critical"
    }
  },
  "notification_channels": {
    "default_webhook": {
      "type": "webhook",
      "enabled": false,
      "config": {
        "url": "",
        "headers": {},
        "timeout": 10
      }
    }
  }
}'),

('isolated_storage', 'Isolated storage configuration for sensitive tenants', 'storage', '{
  "storage_settings": {
    "use_tenant_isolation": true,
    "bucket_naming_strategy": "isolated",
    "file_path_template": "{tenant_id}/{subtenant_id}/{year}/{month}/{day}/{filename}",
    "encryption_enabled": true,
    "retention_days": 365,
    "backup_enabled": true
  },
  "queue_routing": {
    "use_tenant_specific_queues": true,
    "queue_prefix": "tenant",
    "enable_priority_queues": false
  }
}'),

('high_performance', 'High performance configuration for heavy workloads', 'processing', '{
  "processing_rules": {
    "minimum_classification_confidence": 0.6,
    "enable_parallel_processing": true,
    "max_concurrent_documents": 10,
    "enable_preprocessing_cache": true,
    "classification_timeout_seconds": 15,
    "splitting_timeout_seconds": 30,
    "metadata_extraction_timeout_seconds": 60
  },
  "performance_optimizations": {
    "enable_document_batching": true,
    "batch_size": 5,
    "enable_result_caching": true,
    "cache_ttl_minutes": 60
  }
}');

-- Create function to update modified_date automatically
CREATE OR REPLACE FUNCTION update_modified_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.modified_date = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic modified_date updates
CREATE TRIGGER update_tenant_configurations_modified_date
    BEFORE UPDATE ON tenant_configurations
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_date();

CREATE TRIGGER update_alert_rules_modified_date
    BEFORE UPDATE ON alert_rules
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_date();

CREATE TRIGGER update_notification_channels_modified_date
    BEFORE UPDATE ON notification_channels
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_date();

CREATE TRIGGER update_configuration_templates_modified_date
    BEFORE UPDATE ON configuration_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_date();

-- Insert sample configurations for testing
INSERT INTO tenant_configurations (tenant_id, configuration_key, configuration_value, configuration_type, created_by) VALUES
('test_default', 'processing_rules', '{
  "minimum_classification_confidence": 0.8,
  "enable_ocr": true,
  "enable_handwriting_detection": true,
  "classification_timeout_seconds": 30
}', 'user', 'system'),

('test_default', 'queue_routing', '{
  "use_tenant_specific_queues": true,
  "queue_prefix": "test_tenant",
  "enable_priority_queues": false
}', 'user', 'system'),

('test_default', 'storage_settings', '{
  "use_tenant_isolation": true,
  "bucket_naming_strategy": "isolated",
  "file_path_template": "{tenant_id}/{subtenant_id}/{filename}",
  "encryption_enabled": false
}', 'user', 'system');

-- Insert sample alert rules for testing
INSERT INTO alert_rules (tenant_id, rule_name, metric_name, threshold_value, operator, severity, created_by) VALUES
('test_default', 'high_error_rate', 'tenant_error_rate', 0.1, 'gt', 'warning', 'system'),
('test_default', 'processing_delay', 'tenant_processing_duration_seconds', 60, 'gt', 'warning', 'system'),
('test_default', 'queue_backlog', 'tenant_queue_size', 50, 'gt', 'critical', 'system');

-- Insert sample notification channel for testing
INSERT INTO notification_channels (tenant_id, channel_name, channel_type, configuration, created_by) VALUES
('test_default', 'default_webhook', 'webhook', '{
  "url": "http://localhost:8080/alerts",
  "headers": {"Content-Type": "application/json"},
  "timeout": 10
}', 'system');

COMMIT; 