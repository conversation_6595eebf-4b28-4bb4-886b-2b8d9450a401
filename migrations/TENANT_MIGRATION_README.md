# Tenant Support Migration Guide

This guide explains how to migrate your ML pipeline databases to support multi-tenancy.

## Overview

The tenant support migration adds the following to your databases:

### New Tables (Local Database)
- `tenants` - Stores tenant information
- `subtenants` - Stores subtenant information with relationship to tenants

### New Columns Added to Existing Tables

#### Local Database:
- `incoming_packages`: `tenant_id`, `subtenant_id`
- `documents`: `tenant_id`, `subtenant_id`
- `document_chunk`: `tenant_id`, `subtenant_id`
- `splitted_documents`: `tenant_id`, `subtenant_id`
- `users`: `accessible_tenants`, `default_tenant_id`
- `user_activity_logs`: `tenant_id`, `subtenant_id`

#### Remote Database:
- `raw_incoming_packages`: `tenant_id`, `subtenant_id`

### Database Indexes
The migration also creates optimized indexes for tenant-based queries on all modified tables.

## Prerequisites

1. **Environment Variables**: Set up the following environment variables:

```bash
# Local Database Configuration
export PGSQL_HOST=localhost
export PGSQL_PORT=5438
export PGSQL_USERNAME=your_local_username
export PGSQL_PASSWORD=your_local_password
export PGSQL_DB_NAME=your_local_database

# Remote Database Configuration
export PGSQL_HOST_REMOTE=your_remote_host
export PGSQL_PORT_REMOTE=5432
export PGSQL_USERNAME_REMOTE=your_remote_username
export PGSQL_PASSWORD_REMOTE=your_remote_password
export PGSQL_DB_NAME_REMOTE=your_remote_database
export PGSQL_SSL_MODE_REMOTE=require
```

2. **Database Access**: Ensure you have write access to both local and remote databases.

3. **Backup**: **IMPORTANT**: Create backups of your databases before running the migration.

## Running the Migration

### Option 1: Migrate Both Databases (Recommended)
```bash
python migrations/add_tenant_support_migration.py
```

### Option 2: Migrate Only Local Database
```bash
python migrations/add_tenant_support_migration.py --local-only
```

### Option 3: Migrate Only Remote Database
```bash
python migrations/add_tenant_support_migration.py --remote-only
```

### Option 4: Skip Updating Existing Data
If you want to manually handle existing data migration:
```bash
python migrations/add_tenant_support_migration.py --skip-data-update
```

## What the Migration Does

1. **Creates new tenant tables** in the local database
2. **Adds tenant columns** to existing tables in both databases
3. **Creates database indexes** for efficient tenant-based queries
4. **Creates a default tenant** named "default" with a "default" subtenant
5. **Updates existing data** to use the default tenant (unless `--skip-data-update` is used)

## Post-Migration Steps

1. **Verify the migration**: Check that all tables have the new tenant columns:
   ```sql
   -- Local database
   \d+ incoming_packages
   \d+ documents
   \d+ document_chunk
   \d+ splitted_documents
   \d+ users
   \d+ user_activity_logs
   
   -- Remote database
   \d+ raw_incoming_packages
   ```

2. **Create your tenant data**: Add your actual tenants and subtenants:
   ```sql
   INSERT INTO tenants (tenant_id, tenant_name, description, contact_email) 
   VALUES ('your_tenant_id', 'Your Tenant Name', 'Description', '<EMAIL>');
   
   INSERT INTO subtenants (subtenant_id, tenant_id, subtenant_name, description) 
   VALUES ('your_subtenant_id', 'your_tenant_id', 'Your Subtenant Name', 'Description');
   ```

3. **Update application code**: The models have been updated, but you'll need to update application code to:
   - Extract tenant information from incoming data
   - Pass tenant context through the processing pipeline
   - Filter queries by tenant

## Rollback (If Needed)

If you need to rollback the migration, you can remove the added columns:

```sql
-- Local database rollback
ALTER TABLE incoming_packages DROP COLUMN IF EXISTS tenant_id, DROP COLUMN IF EXISTS subtenant_id;
ALTER TABLE documents DROP COLUMN IF EXISTS tenant_id, DROP COLUMN IF EXISTS subtenant_id;
ALTER TABLE document_chunk DROP COLUMN IF EXISTS tenant_id, DROP COLUMN IF EXISTS subtenant_id;
ALTER TABLE splitted_documents DROP COLUMN IF EXISTS tenant_id, DROP COLUMN IF EXISTS subtenant_id;
ALTER TABLE users DROP COLUMN IF EXISTS accessible_tenants, DROP COLUMN IF EXISTS default_tenant_id;
ALTER TABLE user_activity_logs DROP COLUMN IF EXISTS tenant_id, DROP COLUMN IF EXISTS subtenant_id;
DROP TABLE IF EXISTS subtenants;
DROP TABLE IF EXISTS tenants;

-- Remote database rollback
ALTER TABLE raw_incoming_packages DROP COLUMN IF EXISTS tenant_id, DROP COLUMN IF EXISTS subtenant_id;
```

## Next Steps

After completing the migration, you can proceed to:

1. **Step 2**: Update the downloader to extract tenant information
2. **Step 3**: Update processing pipeline components for tenant awareness
3. **Step 4**: Implement tenant-based routing and monitoring

## Troubleshooting

### Common Issues

1. **Connection Errors**: Verify your database credentials and network connectivity
2. **Permission Errors**: Ensure your database user has CREATE TABLE and ALTER TABLE permissions
3. **Table Already Exists**: The migration uses `IF NOT EXISTS` and `IF EXISTS` to be safe to re-run

### Getting Help

Check the migration logs for detailed error messages. The migration script provides detailed output about each step.

### Validation Queries

After migration, you can validate the changes:

```sql
-- Check tenant tables exist
SELECT COUNT(*) FROM tenants;
SELECT COUNT(*) FROM subtenants;

-- Check tenant columns exist
SELECT tenant_id, subtenant_id FROM incoming_packages LIMIT 5;
SELECT tenant_id, subtenant_id FROM documents LIMIT 5;

-- Check default tenant was created
SELECT * FROM tenants WHERE tenant_id = 'default';
SELECT * FROM subtenants WHERE tenant_id = 'default';
``` 