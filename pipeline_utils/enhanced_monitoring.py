"""
Enhanced monitoring system with tenant-aware capabilities.
Extends the existing MonitorService to include tenant tags and tenant-specific metrics.
"""

import time
import json
import logging
import threading
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
import requests
from uuid import uuid4

# Import existing monitoring if available
try:
    from pipeline_utils.monitoring import MonitorService, Status
except ImportError:
    # Fallback definitions if monitoring module doesn't exist
    class Status(Enum):
        UP = "up"
        DOWN = "down"
        UNKNOWN = "unknown"
    
    class MonitorService:
        def __init__(self, *args, **kwargs):
            pass
        
        def start_monitoring(self):
            pass
        
        def update_status(self, status):
            pass


@dataclass
class TenantMetric:
    """Represents a tenant-specific metric."""
    name: str
    value: Union[int, float]
    timestamp: datetime
    tenant_id: str
    subtenant_id: Optional[str] = None
    component: Optional[str] = None
    tags: Dict[str, str] = field(default_factory=dict)
    metric_type: str = "gauge"  # gauge, counter, histogram, summary


@dataclass
class TenantAlert:
    """Represents a tenant-specific alert."""
    alert_id: str
    tenant_id: str
    subtenant_id: Optional[str]
    component: str
    severity: str  # critical, warning, info
    message: str
    timestamp: datetime
    resolved: bool = False
    tags: Dict[str, str] = field(default_factory=dict)


class TenantMetricsCollector:
    """Collects and manages tenant-specific metrics."""
    
    def __init__(self):
        self.metrics: List[TenantMetric] = []
        self.metrics_lock = threading.Lock()
        self.metric_counters: Dict[str, Dict[str, int]] = {}
        self.metric_gauges: Dict[str, Dict[str, float]] = {}
        self.metric_histograms: Dict[str, Dict[str, List[float]]] = {}
        
    def record_metric(self, name: str, value: Union[int, float], 
                     tenant_id: str, subtenant_id: Optional[str] = None,
                     component: Optional[str] = None, tags: Optional[Dict[str, str]] = None,
                     metric_type: str = "gauge"):
        """Record a tenant-specific metric."""
        with self.metrics_lock:
            metric = TenantMetric(
                name=name,
                value=value,
                timestamp=datetime.now(timezone.utc),
                tenant_id=tenant_id,
                subtenant_id=subtenant_id,
                component=component,
                tags=tags or {},
                metric_type=metric_type
            )
            self.metrics.append(metric)
            
            # Update internal counters/gauges
            metric_key = f"{tenant_id}:{subtenant_id or 'default'}:{component or 'default'}"
            
            if metric_type == "counter":
                if name not in self.metric_counters:
                    self.metric_counters[name] = {}
                self.metric_counters[name][metric_key] = self.metric_counters[name].get(metric_key, 0) + value
            elif metric_type == "gauge":
                if name not in self.metric_gauges:
                    self.metric_gauges[name] = {}
                self.metric_gauges[name][metric_key] = value
            elif metric_type == "histogram":
                if name not in self.metric_histograms:
                    self.metric_histograms[name] = {}
                if metric_key not in self.metric_histograms[name]:
                    self.metric_histograms[name][metric_key] = []
                self.metric_histograms[name][metric_key].append(value)
                
    def get_metrics(self, tenant_id: Optional[str] = None, 
                   component: Optional[str] = None, 
                   since: Optional[datetime] = None) -> List[TenantMetric]:
        """Get metrics with optional filtering."""
        with self.metrics_lock:
            filtered_metrics = []
            for metric in self.metrics:
                if tenant_id and metric.tenant_id != tenant_id:
                    continue
                if component and metric.component != component:
                    continue
                if since and metric.timestamp < since:
                    continue
                filtered_metrics.append(metric)
            return filtered_metrics
    
    def get_metric_summary(self, name: str, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """Get a summary of a specific metric."""
        metrics = self.get_metrics(tenant_id=tenant_id)
        matching_metrics = [m for m in metrics if m.name == name]
        
        if not matching_metrics:
            return {}
        
        values = [m.value for m in matching_metrics]
        return {
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "avg": sum(values) / len(values),
            "latest": matching_metrics[-1].value,
            "latest_timestamp": matching_metrics[-1].timestamp.isoformat()
        }
    
    def clear_old_metrics(self, older_than: datetime):
        """Clear metrics older than specified time."""
        with self.metrics_lock:
            self.metrics = [m for m in self.metrics if m.timestamp >= older_than]


class TenantAlertManager:
    """Manages tenant-specific alerts and notifications."""
    
    def __init__(self):
        self.alerts: List[TenantAlert] = []
        self.alert_rules: Dict[str, Dict[str, Any]] = {}
        self.notification_channels: Dict[str, Dict[str, Any]] = {}
        self.alerts_lock = threading.Lock()
        
    def add_alert_rule(self, tenant_id: str, rule_name: str, 
                      metric_name: str, threshold: float, 
                      operator: str = "gt", severity: str = "warning",
                      component: Optional[str] = None):
        """Add an alert rule for a tenant."""
        rule_key = f"{tenant_id}:{rule_name}"
        self.alert_rules[rule_key] = {
            "tenant_id": tenant_id,
            "rule_name": rule_name,
            "metric_name": metric_name,
            "threshold": threshold,
            "operator": operator,  # gt, lt, eq, gte, lte
            "severity": severity,
            "component": component,
            "enabled": True
        }
        
    def check_alert_rules(self, metrics: List[TenantMetric]) -> List[TenantAlert]:
        """Check metrics against alert rules and generate alerts."""
        new_alerts = []
        
        for metric in metrics:
            for rule_key, rule in self.alert_rules.items():
                if not rule["enabled"]:
                    continue
                    
                if rule["tenant_id"] != metric.tenant_id:
                    continue
                    
                if rule["metric_name"] != metric.name:
                    continue
                    
                if rule["component"] and rule["component"] != metric.component:
                    continue
                
                # Check threshold
                threshold_met = False
                if rule["operator"] == "gt" and metric.value > rule["threshold"]:
                    threshold_met = True
                elif rule["operator"] == "lt" and metric.value < rule["threshold"]:
                    threshold_met = True
                elif rule["operator"] == "gte" and metric.value >= rule["threshold"]:
                    threshold_met = True
                elif rule["operator"] == "lte" and metric.value <= rule["threshold"]:
                    threshold_met = True
                elif rule["operator"] == "eq" and metric.value == rule["threshold"]:
                    threshold_met = True
                
                if threshold_met:
                    alert = TenantAlert(
                        alert_id=str(uuid4()),
                        tenant_id=metric.tenant_id,
                        subtenant_id=metric.subtenant_id,
                        component=metric.component or "unknown",
                        severity=rule["severity"],
                        message=f"Alert: {rule['rule_name']} - {metric.name} {rule['operator']} {rule['threshold']} (current: {metric.value})",
                        timestamp=datetime.now(timezone.utc),
                        tags={"rule_name": rule["rule_name"], "metric_name": metric.name}
                    )
                    new_alerts.append(alert)
        
        with self.alerts_lock:
            self.alerts.extend(new_alerts)
            
        return new_alerts
    
    def add_notification_channel(self, tenant_id: str, channel_name: str, 
                               channel_type: str, config: Dict[str, Any]):
        """Add a notification channel for a tenant."""
        channel_key = f"{tenant_id}:{channel_name}"
        self.notification_channels[channel_key] = {
            "tenant_id": tenant_id,
            "channel_name": channel_name,
            "channel_type": channel_type,  # email, webhook, slack, etc.
            "config": config,
            "enabled": True
        }
    
    def send_alert_notifications(self, alerts: List[TenantAlert]):
        """Send notifications for alerts."""
        for alert in alerts:
            tenant_channels = [
                channel for channel_key, channel in self.notification_channels.items()
                if channel["tenant_id"] == alert.tenant_id and channel["enabled"]
            ]
            
            for channel in tenant_channels:
                try:
                    self._send_notification(alert, channel)
                except Exception as e:
                    logging.error(f"Failed to send notification for alert {alert.alert_id}: {e}")
    
    def _send_notification(self, alert: TenantAlert, channel: Dict[str, Any]):
        """Send a notification to a specific channel."""
        if channel["channel_type"] == "webhook":
            self._send_webhook_notification(alert, channel)
        elif channel["channel_type"] == "email":
            self._send_email_notification(alert, channel)
        # Add other notification types as needed
    
    def _send_webhook_notification(self, alert: TenantAlert, channel: Dict[str, Any]):
        """Send webhook notification."""
        webhook_url = channel["config"].get("url")
        if not webhook_url:
            return
            
        payload = {
            "alert_id": alert.alert_id,
            "tenant_id": alert.tenant_id,
            "subtenant_id": alert.subtenant_id,
            "component": alert.component,
            "severity": alert.severity,
            "message": alert.message,
            "timestamp": alert.timestamp.isoformat(),
            "tags": alert.tags
        }
        
        try:
            response = requests.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
            logging.info(f"Webhook notification sent for alert {alert.alert_id}")
        except Exception as e:
            logging.error(f"Failed to send webhook notification: {e}")
    
    def _send_email_notification(self, alert: TenantAlert, channel: Dict[str, Any]):
        """Send email notification (placeholder)."""
        # Implementation depends on email service being used
        logging.info(f"Email notification would be sent for alert {alert.alert_id}")


class EnhancedTenantMonitorService:
    """Enhanced monitoring service with comprehensive tenant support."""
    
    def __init__(self, project_name: str, app_name: str, module_name: str,
                 api_host: str = "http://localhost", api_port: str = "8080"):
        self.project_name = project_name
        self.app_name = app_name
        self.module_name = module_name
        self.api_host = api_host
        self.api_port = api_port
        
        # Initialize components
        self.metrics_collector = TenantMetricsCollector()
        self.alert_manager = TenantAlertManager()
        
        # Initialize base monitor service
        self.base_monitor = MonitorService(project_name, app_name, module_name, api_host, api_port)
        
        # Monitoring thread
        self.monitoring_thread = None
        self.monitoring_active = False
        
        # Setup default alert rules
        self._setup_default_alert_rules()
    
    def _setup_default_alert_rules(self):
        """Setup default alert rules that apply to all tenants."""
        # These can be overridden per tenant
        default_rules = [
            ("high_error_rate", "tenant_error_rate", 0.1, "gt", "warning"),
            ("low_throughput", "tenant_documents_processed_per_minute", 5, "lt", "warning"),
            ("high_processing_time", "tenant_processing_duration_seconds", 60, "gt", "warning"),
            ("queue_backlog", "tenant_queue_size", 100, "gt", "critical"),
        ]
        
        for rule_name, metric_name, threshold, operator, severity in default_rules:
            # Add as global rule (will be checked for all tenants)
            self.alert_manager.add_alert_rule(
                "*", rule_name, metric_name, threshold, operator, severity
            )
    
    def start_monitoring(self):
        """Start the enhanced monitoring service."""
        self.base_monitor.start_monitoring()
        self.monitoring_active = True
        
        # Start monitoring thread
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logging.info("Enhanced tenant monitoring service started")
    
    def stop_monitoring(self):
        """Stop the monitoring service."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logging.info("Enhanced tenant monitoring service stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_active:
            try:
                # Check for alerts
                recent_metrics = self.metrics_collector.get_metrics(
                    since=datetime.now(timezone.utc).replace(minute=datetime.now().minute - 1)
                )
                
                new_alerts = self.alert_manager.check_alert_rules(recent_metrics)
                if new_alerts:
                    self.alert_manager.send_alert_notifications(new_alerts)
                
                # Clean up old metrics (keep last 24 hours)
                cutoff_time = datetime.now(timezone.utc).replace(hour=datetime.now().hour - 24)
                self.metrics_collector.clear_old_metrics(cutoff_time)
                
                # Send metrics to monitoring endpoint
                self._send_metrics_to_endpoint()
                
            except Exception as e:
                logging.error(f"Error in monitoring loop: {e}")
            
            time.sleep(30)  # Check every 30 seconds
    
    def _send_metrics_to_endpoint(self):
        """Send aggregated metrics to monitoring endpoint."""
        try:
            # Aggregate metrics by tenant
            tenant_metrics = {}
            
            for metric in self.metrics_collector.get_metrics():
                tenant_key = f"{metric.tenant_id}:{metric.subtenant_id or 'default'}"
                if tenant_key not in tenant_metrics:
                    tenant_metrics[tenant_key] = {}
                
                if metric.name not in tenant_metrics[tenant_key]:
                    tenant_metrics[tenant_key][metric.name] = []
                
                tenant_metrics[tenant_key][metric.name].append(metric.value)
            
            # Send to monitoring endpoint
            payload = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "tenant_metrics": tenant_metrics,
                "module": self.module_name,
                "application": self.app_name
            }
            
            url = f"{self.api_host}:{self.api_port}/api/v1/tenant_metrics"
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                logging.debug("Tenant metrics sent successfully")
            else:
                logging.warning(f"Failed to send tenant metrics: {response.status_code}")
                
        except Exception as e:
            logging.error(f"Error sending metrics to endpoint: {e}")
    
    # Convenience methods for recording common metrics
    def record_document_processed(self, tenant_id: str, subtenant_id: Optional[str] = None,
                                 component: Optional[str] = None, processing_time: Optional[float] = None):
        """Record that a document was processed."""
        self.metrics_collector.record_metric(
            "tenant_documents_processed_total", 1, tenant_id, subtenant_id, component, metric_type="counter"
        )
        
        if processing_time is not None:
            self.metrics_collector.record_metric(
                "tenant_processing_duration_seconds", processing_time, tenant_id, subtenant_id, component
            )
    
    def record_error(self, tenant_id: str, error_type: str, subtenant_id: Optional[str] = None,
                    component: Optional[str] = None):
        """Record an error occurrence."""
        self.metrics_collector.record_metric(
            "tenant_error_total", 1, tenant_id, subtenant_id, component, 
            tags={"error_type": error_type}, metric_type="counter"
        )
    
    def record_queue_size(self, queue_name: str, size: int, tenant_id: str, 
                         subtenant_id: Optional[str] = None):
        """Record queue size."""
        self.metrics_collector.record_metric(
            "tenant_queue_size", size, tenant_id, subtenant_id, 
            tags={"queue_name": queue_name}
        )
    
    def record_storage_usage(self, tenant_id: str, bytes_used: int, 
                           subtenant_id: Optional[str] = None):
        """Record storage usage."""
        self.metrics_collector.record_metric(
            "tenant_storage_usage_bytes", bytes_used, tenant_id, subtenant_id
        )
    
    def add_tenant_alert_rule(self, tenant_id: str, rule_name: str, 
                             metric_name: str, threshold: float, 
                             operator: str = "gt", severity: str = "warning",
                             component: Optional[str] = None):
        """Add a tenant-specific alert rule."""
        self.alert_manager.add_alert_rule(
            tenant_id, rule_name, metric_name, threshold, operator, severity, component
        )
    
    def add_tenant_notification_channel(self, tenant_id: str, channel_name: str,
                                      channel_type: str, config: Dict[str, Any]):
        """Add a notification channel for a tenant."""
        self.alert_manager.add_notification_channel(tenant_id, channel_name, channel_type, config)
    
    def get_tenant_metrics(self, tenant_id: str, subtenant_id: Optional[str] = None,
                          component: Optional[str] = None) -> List[TenantMetric]:
        """Get metrics for a specific tenant."""
        return self.metrics_collector.get_metrics(tenant_id, component)
    
    def get_tenant_alerts(self, tenant_id: str, resolved: Optional[bool] = None) -> List[TenantAlert]:
        """Get alerts for a specific tenant."""
        with self.alert_manager.alerts_lock:
            alerts = [
                alert for alert in self.alert_manager.alerts
                if alert.tenant_id == tenant_id
            ]
            
            if resolved is not None:
                alerts = [alert for alert in alerts if alert.resolved == resolved]
            
            return alerts
    
    def resolve_alert(self, alert_id: str):
        """Mark an alert as resolved."""
        with self.alert_manager.alerts_lock:
            for alert in self.alert_manager.alerts:
                if alert.alert_id == alert_id:
                    alert.resolved = True
                    break


# Global instance for easy access
enhanced_monitor = None

def get_enhanced_monitor(project_name: str = "ML_Pipeline", 
                        app_name: str = "DocumentProcessor", 
                        module_name: str = "enhanced_monitoring",
                        api_host: str = "http://localhost", 
                        api_port: str = "8080") -> EnhancedTenantMonitorService:
    """Get or create the enhanced monitor instance."""
    global enhanced_monitor
    if enhanced_monitor is None:
        enhanced_monitor = EnhancedTenantMonitorService(
            project_name, app_name, module_name, api_host, api_port
        )
    return enhanced_monitor


# Utility functions for easy integration
def record_tenant_processing_time(tenant_id: str, component: str, processing_time: float,
                                subtenant_id: Optional[str] = None):
    """Utility function to record processing time."""
    monitor = get_enhanced_monitor()
    monitor.record_document_processed(tenant_id, subtenant_id, component, processing_time)


def record_tenant_error(tenant_id: str, component: str, error_type: str,
                       subtenant_id: Optional[str] = None):
    """Utility function to record an error."""
    monitor = get_enhanced_monitor()
    monitor.record_error(tenant_id, error_type, subtenant_id, component)


def setup_tenant_monitoring(tenant_id: str, alert_rules: Optional[List[Dict]] = None,
                          notification_channels: Optional[List[Dict]] = None):
    """Setup monitoring for a new tenant."""
    monitor = get_enhanced_monitor()
    
    # Add alert rules
    if alert_rules:
        for rule in alert_rules:
            monitor.add_tenant_alert_rule(
                tenant_id=tenant_id,
                rule_name=rule["name"],
                metric_name=rule["metric"],
                threshold=rule["threshold"],
                operator=rule.get("operator", "gt"),
                severity=rule.get("severity", "warning"),
                component=rule.get("component")
            )
    
    # Add notification channels
    if notification_channels:
        for channel in notification_channels:
            monitor.add_tenant_notification_channel(
                tenant_id=tenant_id,
                channel_name=channel["name"],
                channel_type=channel["type"],
                config=channel["config"]
            )
    
    logging.info(f"Monitoring setup completed for tenant {tenant_id}") 