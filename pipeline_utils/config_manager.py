"""
Advanced configuration management system for multi-tenant ML pipeline.
Provides tenant-specific configuration with inheritance, validation, and hot reloading.
"""

import json
import logging
import threading
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
import hashlib
from uuid import uuid4

try:
    from sqlalchemy import create_engine, text
    from sqlalchemy.orm import sessionmaker
    from pipeline_utils.database_connector import DBConnector
except ImportError:
    logging.warning("SQLAlchemy not available, configuration manager will use in-memory storage")


class ConfigurationType(Enum):
    """Types of configuration entries."""
    USER = "user"
    SYSTEM = "system"
    TEMPLATE = "template"
    DEFAULT = "default"


class ConfigurationCategory(Enum):
    """Categories of configurations."""
    PROCESSING = "processing"
    MONITORING = "monitoring"
    STORAGE = "storage"
    QUEUE_ROUTING = "queue_routing"
    SECURITY = "security"
    PERFORMANCE = "performance"


@dataclass
class ConfigurationEntry:
    """Represents a single configuration entry."""
    id: str
    tenant_id: str
    subtenant_id: Optional[str]
    configuration_key: str
    configuration_value: Dict[str, Any]
    configuration_type: ConfigurationType
    version: int = 1
    is_active: bool = True
    is_encrypted: bool = False
    created_date: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    modified_date: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    created_by: Optional[str] = None
    modified_by: Optional[str] = None
    description: Optional[str] = None


@dataclass
class ConfigurationTemplate:
    """Represents a configuration template."""
    id: str
    template_name: str
    template_description: Optional[str]
    template_category: ConfigurationCategory
    template_configuration: Dict[str, Any]
    is_active: bool = True
    version: str = "1.0"
    created_date: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    created_by: Optional[str] = None


class ConfigurationValidator:
    """Validates configuration entries against schemas and security rules."""
    
    def __init__(self):
        self.schemas = self._load_configuration_schemas()
        self.security_rules = self._load_security_rules()
    
    def _load_configuration_schemas(self) -> Dict[str, Dict[str, Any]]:
        """Load configuration schemas for validation."""
        return {
            "processing_rules": {
                "type": "object",
                "properties": {
                    "minimum_classification_confidence": {"type": "number", "minimum": 0, "maximum": 1},
                    "enable_ocr": {"type": "boolean"},
                    "enable_handwriting_detection": {"type": "boolean"},
                    "classification_timeout_seconds": {"type": "integer", "minimum": 1, "maximum": 300},
                    "splitting_timeout_seconds": {"type": "integer", "minimum": 1, "maximum": 600},
                    "metadata_extraction_timeout_seconds": {"type": "integer", "minimum": 1, "maximum": 600},
                    "enable_parallel_processing": {"type": "boolean"},
                    "max_concurrent_documents": {"type": "integer", "minimum": 1, "maximum": 50}
                },
                "required": ["minimum_classification_confidence"]
            },
            "queue_routing": {
                "type": "object",
                "properties": {
                    "use_tenant_specific_queues": {"type": "boolean"},
                    "queue_prefix": {"type": "string", "maxLength": 50},
                    "enable_priority_queues": {"type": "boolean"},
                    "queue_isolation_level": {"type": "string", "enum": ["none", "tenant", "subtenant"]}
                }
            },
            "storage_settings": {
                "type": "object",
                "properties": {
                    "use_tenant_isolation": {"type": "boolean"},
                    "bucket_naming_strategy": {"type": "string", "enum": ["shared", "isolated", "prefixed"]},
                    "file_path_template": {"type": "string"},
                    "encryption_enabled": {"type": "boolean"},
                    "retention_days": {"type": "integer", "minimum": 1, "maximum": 3650},
                    "backup_enabled": {"type": "boolean"}
                }
            },
            "monitoring_config": {
                "type": "object",
                "properties": {
                    "enable_detailed_metrics": {"type": "boolean"},
                    "metric_collection_interval_seconds": {"type": "integer", "minimum": 10, "maximum": 3600},
                    "enable_performance_tracking": {"type": "boolean"},
                    "alert_escalation_enabled": {"type": "boolean"}
                }
            }
        }
    
    def _load_security_rules(self) -> Dict[str, Any]:
        """Load security validation rules."""
        return {
            "forbidden_keys": [
                "password", "secret", "private_key", "api_key", "token"
            ],
            "max_value_size": 10000,  # Maximum size of configuration value in characters
            "allowed_file_extensions": [".pdf", ".tiff", ".docx", ".rtf"],
            "dangerous_patterns": [
                r"<script.*?>.*?</script>",  # Script injection
                r"javascript:",              # JavaScript URLs
                r"data:.*base64",           # Data URLs
                r"\$\{.*\}",                # Variable interpolation
            ]
        }
    
    def validate_configuration(self, config_key: str, config_value: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate a configuration entry.
        
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        # Check if we have a schema for this configuration key
        if config_key in self.schemas:
            schema_errors = self._validate_against_schema(config_value, self.schemas[config_key])
            errors.extend(schema_errors)
        
        # Security validation
        security_errors = self._validate_security(config_key, config_value)
        errors.extend(security_errors)
        
        return len(errors) == 0, errors
    
    def _validate_against_schema(self, value: Any, schema: Dict[str, Any]) -> List[str]:
        """Validate value against JSON schema."""
        errors = []
        
        if schema.get("type") == "object" and isinstance(value, dict):
            # Check required properties
            required = schema.get("required", [])
            for req_prop in required:
                if req_prop not in value:
                    errors.append(f"Required property '{req_prop}' is missing")
            
            # Check properties
            properties = schema.get("properties", {})
            for prop_name, prop_value in value.items():
                if prop_name in properties:
                    prop_schema = properties[prop_name]
                    prop_errors = self._validate_property(prop_name, prop_value, prop_schema)
                    errors.extend(prop_errors)
        
        return errors
    
    def _validate_property(self, prop_name: str, prop_value: Any, prop_schema: Dict[str, Any]) -> List[str]:
        """Validate a single property against its schema."""
        errors = []
        expected_type = prop_schema.get("type")
        
        # Type validation
        if expected_type == "string" and not isinstance(prop_value, str):
            errors.append(f"Property '{prop_name}' must be a string")
        elif expected_type == "number" and not isinstance(prop_value, (int, float)):
            errors.append(f"Property '{prop_name}' must be a number")
        elif expected_type == "integer" and not isinstance(prop_value, int):
            errors.append(f"Property '{prop_name}' must be an integer")
        elif expected_type == "boolean" and not isinstance(prop_value, bool):
            errors.append(f"Property '{prop_name}' must be a boolean")
        
        # Range validation for numbers
        if isinstance(prop_value, (int, float)):
            if "minimum" in prop_schema and prop_value < prop_schema["minimum"]:
                errors.append(f"Property '{prop_name}' must be >= {prop_schema['minimum']}")
            if "maximum" in prop_schema and prop_value > prop_schema["maximum"]:
                errors.append(f"Property '{prop_name}' must be <= {prop_schema['maximum']}")
        
        # String validation
        if isinstance(prop_value, str):
            if "maxLength" in prop_schema and len(prop_value) > prop_schema["maxLength"]:
                errors.append(f"Property '{prop_name}' must be <= {prop_schema['maxLength']} characters")
            if "enum" in prop_schema and prop_value not in prop_schema["enum"]:
                errors.append(f"Property '{prop_name}' must be one of: {prop_schema['enum']}")
        
        return errors
    
    def _validate_security(self, config_key: str, config_value: Dict[str, Any]) -> List[str]:
        """Validate configuration for security issues."""
        errors = []
        
        # Check for forbidden keys
        config_str = json.dumps(config_value).lower()
        for forbidden_key in self.security_rules["forbidden_keys"]:
            if forbidden_key in config_str:
                errors.append(f"Configuration contains forbidden key: {forbidden_key}")
        
        # Check size limits
        config_size = len(json.dumps(config_value))
        if config_size > self.security_rules["max_value_size"]:
            errors.append(f"Configuration size ({config_size}) exceeds maximum ({self.security_rules['max_value_size']})")
        
        # Check for dangerous patterns
        import re
        for pattern in self.security_rules["dangerous_patterns"]:
            if re.search(pattern, config_str, re.IGNORECASE):
                errors.append(f"Configuration contains potentially dangerous pattern: {pattern}")
        
        return errors


class ConfigurationCache:
    """Caches configuration entries for fast access."""
    
    def __init__(self, cache_ttl_seconds: int = 300):
        self.cache: Dict[str, Tuple[ConfigurationEntry, datetime]] = {}
        self.cache_lock = threading.RLock()
        self.cache_ttl_seconds = cache_ttl_seconds
        
        # Start cache cleanup thread
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
    
    def get(self, cache_key: str) -> Optional[ConfigurationEntry]:
        """Get configuration from cache."""
        with self.cache_lock:
            if cache_key in self.cache:
                entry, cached_time = self.cache[cache_key]
                if datetime.now(timezone.utc) - cached_time < timedelta(seconds=self.cache_ttl_seconds):
                    return entry
                else:
                    # Expired, remove from cache
                    del self.cache[cache_key]
        return None
    
    def put(self, cache_key: str, entry: ConfigurationEntry):
        """Store configuration in cache."""
        with self.cache_lock:
            self.cache[cache_key] = (entry, datetime.now(timezone.utc))
    
    def invalidate(self, cache_key: str = None):
        """Invalidate cache entry or entire cache."""
        with self.cache_lock:
            if cache_key:
                self.cache.pop(cache_key, None)
            else:
                self.cache.clear()
    
    def _cleanup_loop(self):
        """Periodically clean up expired cache entries."""
        while True:
            try:
                time.sleep(60)  # Check every minute
                current_time = datetime.now(timezone.utc)
                
                with self.cache_lock:
                    expired_keys = []
                    for key, (entry, cached_time) in self.cache.items():
                        if current_time - cached_time >= timedelta(seconds=self.cache_ttl_seconds):
                            expired_keys.append(key)
                    
                    for key in expired_keys:
                        del self.cache[key]
                        
            except Exception as e:
                logging.error(f"Error in cache cleanup: {e}")


class TenantConfigurationManager:
    """
    Advanced configuration manager for multi-tenant ML pipeline.
    Provides configuration inheritance, validation, caching, and hot reloading.
    """
    
    def __init__(self, db_connector: Optional[DBConnector] = None):
        self.db_connector = db_connector
        self.validator = ConfigurationValidator()
        self.cache = ConfigurationCache()
        
        # In-memory storage for when database is not available
        self.memory_storage: Dict[str, ConfigurationEntry] = {}
        self.memory_templates: Dict[str, ConfigurationTemplate] = {}
        
        # Configuration change callbacks
        self.change_callbacks: List[callable] = []
        
        # Hot reload settings
        self.hot_reload_enabled = True
        self.reload_check_interval = 30  # seconds
        self.last_reload_check = datetime.now(timezone.utc)
        
        # Initialize default configurations
        self._initialize_default_configurations()
    
    def _initialize_default_configurations(self):
        """Initialize default configurations."""
        default_configs = {
            "processing_rules": {
                "minimum_classification_confidence": 0.7,
                "enable_ocr": True,
                "enable_handwriting_detection": False,
                "classification_timeout_seconds": 30,
                "splitting_timeout_seconds": 60,
                "metadata_extraction_timeout_seconds": 120
            },
            "queue_routing": {
                "use_tenant_specific_queues": True,
                "queue_prefix": "tenant",
                "enable_priority_queues": False,
                "queue_isolation_level": "tenant"
            },
            "storage_settings": {
                "use_tenant_isolation": True,
                "bucket_naming_strategy": "isolated",
                "file_path_template": "{tenant_id}/{subtenant_id}/{filename}",
                "encryption_enabled": False,
                "retention_days": 365,
                "backup_enabled": true
            },
            "monitoring_config": {
                "enable_detailed_metrics": True,
                "metric_collection_interval_seconds": 30,
                "enable_performance_tracking": True,
                "alert_escalation_enabled": True
            }
        }
        
        for key, value in default_configs.items():
            entry = ConfigurationEntry(
                id=str(uuid4()),
                tenant_id="*",  # Global default
                subtenant_id=None,
                configuration_key=key,
                configuration_value=value,
                configuration_type=ConfigurationType.DEFAULT,
                created_by="system"
            )
            self.memory_storage[f"*:None:{key}"] = entry
    
    def get_configuration(self, tenant_id: str, configuration_key: str, 
                         subtenant_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get configuration with inheritance chain: Subtenant -> Tenant -> Global Default.
        """
        # Try cache first
        cache_key = f"{tenant_id}:{subtenant_id}:{configuration_key}"
        cached_entry = self.cache.get(cache_key)
        if cached_entry:
            return cached_entry.configuration_value
        
        # Check for hot reload
        self._check_hot_reload()
        
        # Configuration inheritance chain
        search_keys = []
        
        # 1. Subtenant-specific configuration
        if subtenant_id:
            search_keys.append(f"{tenant_id}:{subtenant_id}:{configuration_key}")
        
        # 2. Tenant-specific configuration
        search_keys.append(f"{tenant_id}:None:{configuration_key}")
        
        # 3. Global default configuration
        search_keys.append(f"*:None:{configuration_key}")
        
        # Search in order of precedence
        for search_key in search_keys:
            entry = self._get_configuration_entry(search_key)
            if entry and entry.is_active:
                # Cache the result
                self.cache.put(cache_key, entry)
                return entry.configuration_value
        
        logging.warning(f"No configuration found for key: {configuration_key}, tenant: {tenant_id}")
        return None
    
    def set_configuration(self, tenant_id: str, configuration_key: str, 
                         configuration_value: Dict[str, Any], 
                         subtenant_id: Optional[str] = None,
                         configuration_type: ConfigurationType = ConfigurationType.USER,
                         modified_by: Optional[str] = None,
                         description: Optional[str] = None) -> Tuple[bool, List[str]]:
        """
        Set configuration for a tenant/subtenant.
        
        Returns:
            Tuple of (success, errors)
        """
        # Validate configuration
        is_valid, errors = self.validator.validate_configuration(configuration_key, configuration_value)
        if not is_valid:
            return False, errors
        
        # Get existing configuration to track changes
        existing_config = self.get_configuration(tenant_id, configuration_key, subtenant_id)
        
        # Create new configuration entry
        entry = ConfigurationEntry(
            id=str(uuid4()),
            tenant_id=tenant_id,
            subtenant_id=subtenant_id,
            configuration_key=configuration_key,
            configuration_value=configuration_value,
            configuration_type=configuration_type,
            modified_by=modified_by,
            description=description
        )
        
        # Store configuration
        storage_key = f"{tenant_id}:{subtenant_id}:{configuration_key}"
        
        try:
            if self.db_connector:
                self._store_configuration_in_db(entry, existing_config)
            else:
                self.memory_storage[storage_key] = entry
            
            # Invalidate cache
            self.cache.invalidate(storage_key)
            
            # Notify change callbacks
            self._notify_configuration_change(tenant_id, configuration_key, 
                                           existing_config, configuration_value)
            
            logging.info(f"Configuration updated: {configuration_key} for tenant {tenant_id}")
            return True, []
            
        except Exception as e:
            logging.error(f"Failed to store configuration: {e}")
            return False, [f"Storage error: {str(e)}"]
    
    def delete_configuration(self, tenant_id: str, configuration_key: str,
                           subtenant_id: Optional[str] = None,
                           deleted_by: Optional[str] = None) -> bool:
        """Delete a configuration entry."""
        storage_key = f"{tenant_id}:{subtenant_id}:{configuration_key}"
        
        try:
            if self.db_connector:
                self._delete_configuration_from_db(tenant_id, configuration_key, 
                                                 subtenant_id, deleted_by)
            else:
                if storage_key in self.memory_storage:
                    del self.memory_storage[storage_key]
            
            # Invalidate cache
            self.cache.invalidate(storage_key)
            
            logging.info(f"Configuration deleted: {configuration_key} for tenant {tenant_id}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to delete configuration: {e}")
            return False
    
    def get_configuration_history(self, tenant_id: str, configuration_key: str,
                                subtenant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get configuration change history."""
        if not self.db_connector:
            return []
        
        try:
            with self.db_connector.session_scope() as session:
                query = text("""
                    SELECT ch.*, tc.configuration_value
                    FROM configuration_history ch
                    LEFT JOIN tenant_configurations tc ON ch.configuration_id = tc.id
                    WHERE ch.tenant_id = :tenant_id 
                    AND ch.configuration_key = :config_key
                    AND (:subtenant_id IS NULL OR ch.subtenant_id = :subtenant_id)
                    ORDER BY ch.changed_date DESC
                """)
                
                result = session.execute(query, {
                    'tenant_id': tenant_id,
                    'config_key': configuration_key,
                    'subtenant_id': subtenant_id
                })
                
                history = []
                for row in result:
                    history.append({
                        'id': str(row.id),
                        'change_type': row.change_type,
                        'version': row.version,
                        'old_value': row.old_value,
                        'new_value': row.new_value,
                        'changed_date': row.changed_date.isoformat(),
                        'changed_by': row.changed_by,
                        'change_reason': row.change_reason
                    })
                
                return history
                
        except Exception as e:
            logging.error(f"Failed to get configuration history: {e}")
            return []
    
    def apply_configuration_template(self, tenant_id: str, template_name: str,
                                   subtenant_id: Optional[str] = None,
                                   applied_by: Optional[str] = None) -> Tuple[bool, List[str]]:
        """Apply a configuration template to a tenant."""
        template = self.get_configuration_template(template_name)
        if not template:
            return False, [f"Template '{template_name}' not found"]
        
        errors = []
        success_count = 0
        
        # Apply each configuration from the template
        for config_key, config_value in template.template_configuration.items():
            success, config_errors = self.set_configuration(
                tenant_id=tenant_id,
                configuration_key=config_key,
                configuration_value=config_value,
                subtenant_id=subtenant_id,
                configuration_type=ConfigurationType.TEMPLATE,
                modified_by=applied_by,
                description=f"Applied from template: {template_name}"
            )
            
            if success:
                success_count += 1
            else:
                errors.extend([f"{config_key}: {error}" for error in config_errors])
        
        if success_count > 0:
            logging.info(f"Applied template '{template_name}' to tenant {tenant_id}: "
                        f"{success_count} configurations updated")
        
        return success_count > 0, errors
    
    def get_configuration_template(self, template_name: str) -> Optional[ConfigurationTemplate]:
        """Get a configuration template by name."""
        if self.db_connector:
            try:
                with self.db_connector.session_scope() as session:
                    query = text("""
                        SELECT * FROM configuration_templates 
                        WHERE template_name = :template_name AND is_active = true
                    """)
                    
                    result = session.execute(query, {'template_name': template_name}).fetchone()
                    
                    if result:
                        return ConfigurationTemplate(
                            id=str(result.id),
                            template_name=result.template_name,
                            template_description=result.template_description,
                            template_category=ConfigurationCategory(result.template_category),
                            template_configuration=result.template_configuration,
                            is_active=result.is_active,
                            version=result.version,
                            created_date=result.created_date,
                            created_by=result.created_by
                        )
            except Exception as e:
                logging.error(f"Failed to get configuration template: {e}")
        
        return self.memory_templates.get(template_name)
    
    def register_change_callback(self, callback: callable):
        """Register a callback to be called when configurations change."""
        self.change_callbacks.append(callback)
    
    def _get_configuration_entry(self, storage_key: str) -> Optional[ConfigurationEntry]:
        """Get configuration entry from storage."""
        if self.db_connector:
            try:
                parts = storage_key.split(':')
                tenant_id, subtenant_id, config_key = parts[0], parts[1], parts[2]
                subtenant_id = None if subtenant_id == 'None' else subtenant_id
                
                with self.db_connector.session_scope() as session:
                    query = text("""
                        SELECT * FROM tenant_configurations 
                        WHERE tenant_id = :tenant_id 
                        AND configuration_key = :config_key
                        AND (:subtenant_id IS NULL OR subtenant_id = :subtenant_id)
                        AND is_active = true
                        ORDER BY version DESC
                        LIMIT 1
                    """)
                    
                    result = session.execute(query, {
                        'tenant_id': tenant_id,
                        'config_key': config_key,
                        'subtenant_id': subtenant_id
                    }).fetchone()
                    
                    if result:
                        return ConfigurationEntry(
                            id=str(result.id),
                            tenant_id=result.tenant_id,
                            subtenant_id=result.subtenant_id,
                            configuration_key=result.configuration_key,
                            configuration_value=result.configuration_value,
                            configuration_type=ConfigurationType(result.configuration_type),
                            version=result.version,
                            is_active=result.is_active,
                            created_date=result.created_date,
                            modified_date=result.modified_date,
                            created_by=result.created_by,
                            modified_by=result.modified_by,
                            description=result.description
                        )
            except Exception as e:
                logging.error(f"Failed to get configuration from database: {e}")
        
        return self.memory_storage.get(storage_key)
    
    def _store_configuration_in_db(self, entry: ConfigurationEntry, existing_config: Optional[Dict[str, Any]]):
        """Store configuration entry in database."""
        with self.db_connector.session_scope() as session:
            # Insert new configuration
            insert_query = text("""
                INSERT INTO tenant_configurations 
                (id, tenant_id, subtenant_id, configuration_key, configuration_value, 
                 configuration_type, version, is_active, created_date, modified_date, 
                 created_by, modified_by, description)
                VALUES (:id, :tenant_id, :subtenant_id, :config_key, :config_value, 
                        :config_type, :version, :is_active, :created_date, :modified_date,
                        :created_by, :modified_by, :description)
            """)
            
            session.execute(insert_query, {
                'id': entry.id,
                'tenant_id': entry.tenant_id,
                'subtenant_id': entry.subtenant_id,
                'config_key': entry.configuration_key,
                'config_value': json.dumps(entry.configuration_value),
                'config_type': entry.configuration_type.value,
                'version': entry.version,
                'is_active': entry.is_active,
                'created_date': entry.created_date,
                'modified_date': entry.modified_date,
                'created_by': entry.created_by,
                'modified_by': entry.modified_by,
                'description': entry.description
            })
            
            # Record change in history
            history_query = text("""
                INSERT INTO configuration_history 
                (id, configuration_id, tenant_id, subtenant_id, configuration_key,
                 old_value, new_value, change_type, version, changed_date, changed_by)
                VALUES (:id, :config_id, :tenant_id, :subtenant_id, :config_key,
                        :old_value, :new_value, :change_type, :version, :changed_date, :changed_by)
            """)
            
            session.execute(history_query, {
                'id': str(uuid4()),
                'config_id': entry.id,
                'tenant_id': entry.tenant_id,
                'subtenant_id': entry.subtenant_id,
                'config_key': entry.configuration_key,
                'old_value': json.dumps(existing_config) if existing_config else None,
                'new_value': json.dumps(entry.configuration_value),
                'change_type': 'update' if existing_config else 'create',
                'version': entry.version,
                'changed_date': entry.modified_date,
                'changed_by': entry.modified_by
            })
    
    def _delete_configuration_from_db(self, tenant_id: str, configuration_key: str,
                                    subtenant_id: Optional[str], deleted_by: Optional[str]):
        """Delete configuration from database (soft delete)."""
        with self.db_connector.session_scope() as session:
            # Soft delete by setting is_active = false
            update_query = text("""
                UPDATE tenant_configurations 
                SET is_active = false, modified_date = :modified_date, modified_by = :modified_by
                WHERE tenant_id = :tenant_id 
                AND configuration_key = :config_key
                AND (:subtenant_id IS NULL OR subtenant_id = :subtenant_id)
            """)
            
            session.execute(update_query, {
                'tenant_id': tenant_id,
                'config_key': configuration_key,
                'subtenant_id': subtenant_id,
                'modified_date': datetime.now(timezone.utc),
                'modified_by': deleted_by
            })
    
    def _check_hot_reload(self):
        """Check if configurations need to be reloaded."""
        if not self.hot_reload_enabled:
            return
        
        current_time = datetime.now(timezone.utc)
        if current_time - self.last_reload_check < timedelta(seconds=self.reload_check_interval):
            return
        
        self.last_reload_check = current_time
        
        # For now, just invalidate cache to force reload from database
        # In a more sophisticated implementation, we could check modification timestamps
        self.cache.invalidate()
    
    def _notify_configuration_change(self, tenant_id: str, configuration_key: str,
                                   old_value: Optional[Dict[str, Any]], 
                                   new_value: Dict[str, Any]):
        """Notify registered callbacks about configuration changes."""
        change_info = {
            'tenant_id': tenant_id,
            'configuration_key': configuration_key,
            'old_value': old_value,
            'new_value': new_value,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        for callback in self.change_callbacks:
            try:
                callback(change_info)
            except Exception as e:
                logging.error(f"Error in configuration change callback: {e}")


# Global configuration manager instance
config_manager = None

def get_config_manager(db_connector: Optional[DBConnector] = None) -> TenantConfigurationManager:
    """Get or create the global configuration manager instance."""
    global config_manager
    if config_manager is None:
        config_manager = TenantConfigurationManager(db_connector)
    return config_manager


# Utility functions for easy configuration access
def get_tenant_config(tenant_id: str, config_key: str, subtenant_id: Optional[str] = None,
                     default: Any = None) -> Any:
    """Utility function to get tenant configuration."""
    manager = get_config_manager()
    config = manager.get_configuration(tenant_id, config_key, subtenant_id)
    return config if config is not None else default


def set_tenant_config(tenant_id: str, config_key: str, config_value: Dict[str, Any],
                     subtenant_id: Optional[str] = None, modified_by: Optional[str] = None) -> bool:
    """Utility function to set tenant configuration."""
    manager = get_config_manager()
    success, errors = manager.set_configuration(
        tenant_id, config_key, config_value, subtenant_id, 
        ConfigurationType.USER, modified_by
    )
    if not success:
        logging.error(f"Failed to set configuration: {errors}")
    return success


def apply_template_to_tenant(tenant_id: str, template_name: str, 
                           subtenant_id: Optional[str] = None) -> bool:
    """Utility function to apply configuration template to tenant."""
    manager = get_config_manager()
    success, errors = manager.apply_configuration_template(tenant_id, template_name, subtenant_id)
    if not success:
        logging.error(f"Failed to apply template: {errors}")
    return success 