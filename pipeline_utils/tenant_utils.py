"""
Tenant utilities for extracting and managing tenant information from various channels.
"""

import os
import re
import json
import logging
from typing import Dict, Optional, Tuple, Any
from enum import Enum

logger = logging.getLogger(__name__)


class ChannelType(Enum):
    SFTP = "sftp"
    SERVICEBUS_TOPIC = "servicebus_topic"
    SERVICEBUS_QUEUE = "servicebus_queue"
    SQS = "sqs"
    S3 = "s3"


class TenantExtractionStrategy(Enum):
    """Different strategies for extracting tenant information"""
    FILENAME_PATTERN = "filename_pattern"
    FOLDER_STRUCTURE = "folder_structure"
    MESSAGE_PROPERTIES = "message_properties"
    MESSAGE_BODY = "message_body"
    OBJECT_METADATA = "object_metadata"
    OBJECT_TAGS = "object_tags"


class TenantInfoExtractor:
    """
    Utility class for extracting tenant and subtenant information from different channels.
    """
    
    def __init__(self):
        self.extraction_patterns = {
            # Default patterns - can be overridden per tenant
            ChannelType.SFTP: {
                TenantExtractionStrategy.FOLDER_STRUCTURE: r'^(?P<tenant_id>[^/]+)/(?P<subtenant_id>[^/]+)/',
                TenantExtractionStrategy.FILENAME_PATTERN: r'^(?P<tenant_id>[^_]+)_(?P<subtenant_id>[^_]+)_.*'
            },
            ChannelType.SERVICEBUS_TOPIC: {
                TenantExtractionStrategy.MESSAGE_PROPERTIES: ['tenant_id', 'subtenant_id'],
                TenantExtractionStrategy.MESSAGE_BODY: ['tenantId', 'subtenantId']
            },
            ChannelType.SERVICEBUS_QUEUE: {
                TenantExtractionStrategy.MESSAGE_PROPERTIES: ['tenant_id', 'subtenant_id'],
                TenantExtractionStrategy.MESSAGE_BODY: ['tenantId', 'subtenantId']
            },
            ChannelType.SQS: {
                TenantExtractionStrategy.MESSAGE_PROPERTIES: ['tenant_id', 'subtenant_id'],
                TenantExtractionStrategy.MESSAGE_BODY: ['tenantId', 'subtenantId']
            },
            ChannelType.S3: {
                TenantExtractionStrategy.OBJECT_METADATA: ['tenant-id', 'subtenant-id'],
                TenantExtractionStrategy.OBJECT_TAGS: ['TenantId', 'SubtenantId']
            }
        }
    
    def extract_tenant_info(
        self, 
        channel: ChannelType, 
        file_path: Optional[str] = None, 
        message_content: Optional[Dict[str, Any]] = None,
        message_properties: Optional[Dict[str, Any]] = None,
        object_metadata: Optional[Dict[str, Any]] = None,
        object_tags: Optional[Dict[str, Any]] = None,
        strategy: Optional[TenantExtractionStrategy] = None
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        Extract tenant and subtenant information based on the channel and available data.
        
        Args:
            channel: The channel type (SFTP, ServiceBus, SQS, S3)
            file_path: File path (for SFTP, S3)
            message_content: Message body content (for ServiceBus, SQS)
            message_properties: Message properties/attributes (for ServiceBus, SQS)
            object_metadata: Object metadata (for S3)
            object_tags: Object tags (for S3)
            strategy: Specific extraction strategy to use
        
        Returns:
            Tuple of (tenant_id, subtenant_id) or (None, None) if not found
        """
        try:
            if channel == ChannelType.SFTP:
                return self._extract_from_sftp(file_path, strategy)
            elif channel in [ChannelType.SERVICEBUS_TOPIC, ChannelType.SERVICEBUS_QUEUE]:
                return self._extract_from_servicebus(message_content, message_properties, strategy)
            elif channel == ChannelType.SQS:
                return self._extract_from_sqs(message_content, message_properties, strategy)
            elif channel == ChannelType.S3:
                return self._extract_from_s3(file_path, object_metadata, object_tags, strategy)
            else:
                logger.warning(f"Unsupported channel type: {channel}")
                return None, None
        except Exception as e:
            logger.error(f"Error extracting tenant info from {channel}: {e}")
            return None, None
    
    def _extract_from_sftp(
        self, 
        file_path: Optional[str], 
        strategy: Optional[TenantExtractionStrategy] = None
    ) -> Tuple[Optional[str], Optional[str]]:
        """Extract tenant info from SFTP file path"""
        if not file_path:
            return None, None
        
        # Try folder structure first
        if not strategy or strategy == TenantExtractionStrategy.FOLDER_STRUCTURE:
            pattern = self.extraction_patterns[ChannelType.SFTP][TenantExtractionStrategy.FOLDER_STRUCTURE]
            match = re.search(pattern, file_path)
            if match:
                return match.group('tenant_id'), match.group('subtenant_id')
        
        # Try filename pattern
        if not strategy or strategy == TenantExtractionStrategy.FILENAME_PATTERN:
            filename = os.path.basename(file_path)
            pattern = self.extraction_patterns[ChannelType.SFTP][TenantExtractionStrategy.FILENAME_PATTERN]
            match = re.search(pattern, filename)
            if match:
                return match.group('tenant_id'), match.group('subtenant_id')
        
        return None, None
    
    def _extract_from_servicebus(
        self, 
        message_content: Optional[Dict[str, Any]], 
        message_properties: Optional[Dict[str, Any]],
        strategy: Optional[TenantExtractionStrategy] = None
    ) -> Tuple[Optional[str], Optional[str]]:
        """Extract tenant info from ServiceBus message"""
        
        # Try message properties first
        if message_properties and (not strategy or strategy == TenantExtractionStrategy.MESSAGE_PROPERTIES):
            prop_names = self.extraction_patterns[ChannelType.SERVICEBUS_TOPIC][TenantExtractionStrategy.MESSAGE_PROPERTIES]
            tenant_id = message_properties.get(prop_names[0])
            subtenant_id = message_properties.get(prop_names[1])
            if tenant_id:
                return tenant_id, subtenant_id
        
        # Try message body
        if message_content and (not strategy or strategy == TenantExtractionStrategy.MESSAGE_BODY):
            body_names = self.extraction_patterns[ChannelType.SERVICEBUS_TOPIC][TenantExtractionStrategy.MESSAGE_BODY]
            tenant_id = message_content.get(body_names[0])
            subtenant_id = message_content.get(body_names[1])
            if tenant_id:
                return tenant_id, subtenant_id
        
        return None, None
    
    def _extract_from_sqs(
        self, 
        message_content: Optional[Dict[str, Any]], 
        message_properties: Optional[Dict[str, Any]],
        strategy: Optional[TenantExtractionStrategy] = None
    ) -> Tuple[Optional[str], Optional[str]]:
        """Extract tenant info from SQS message"""
        # Same logic as ServiceBus for now
        return self._extract_from_servicebus(message_content, message_properties, strategy)
    
    def _extract_from_s3(
        self, 
        file_path: Optional[str],
        object_metadata: Optional[Dict[str, Any]], 
        object_tags: Optional[Dict[str, Any]],
        strategy: Optional[TenantExtractionStrategy] = None
    ) -> Tuple[Optional[str], Optional[str]]:
        """Extract tenant info from S3 object"""
        
        # Try object metadata first
        if object_metadata and (not strategy or strategy == TenantExtractionStrategy.OBJECT_METADATA):
            meta_names = self.extraction_patterns[ChannelType.S3][TenantExtractionStrategy.OBJECT_METADATA]
            tenant_id = object_metadata.get(meta_names[0])
            subtenant_id = object_metadata.get(meta_names[1])
            if tenant_id:
                return tenant_id, subtenant_id
        
        # Try object tags
        if object_tags and (not strategy or strategy == TenantExtractionStrategy.OBJECT_TAGS):
            tag_names = self.extraction_patterns[ChannelType.S3][TenantExtractionStrategy.OBJECT_TAGS]
            tenant_id = object_tags.get(tag_names[0])
            subtenant_id = object_tags.get(tag_names[1])
            if tenant_id:
                return tenant_id, subtenant_id
        
        # Try file path pattern (similar to SFTP)
        if file_path:
            pattern = r'^(?P<tenant_id>[^/]+)/(?P<subtenant_id>[^/]+)/'
            match = re.search(pattern, file_path)
            if match:
                return match.group('tenant_id'), match.group('subtenant_id')
        
        return None, None
    
    def validate_tenant_info(self, tenant_id: Optional[str], subtenant_id: Optional[str]) -> bool:
        """
        Validate that tenant information is properly formatted and exists.
        
        Args:
            tenant_id: The tenant ID to validate
            subtenant_id: The subtenant ID to validate
        
        Returns:
            True if valid, False otherwise
        """
        if not tenant_id:
            return False
        
        # Basic validation rules
        if not re.match(r'^[a-zA-Z0-9_-]+$', tenant_id):
            logger.warning(f"Invalid tenant_id format: {tenant_id}")
            return False
        
        if subtenant_id and not re.match(r'^[a-zA-Z0-9_-]+$', subtenant_id):
            logger.warning(f"Invalid subtenant_id format: {subtenant_id}")
            return False
        
        # TODO: Add database validation to check if tenant exists
        return True
    
    def generate_tenant_prefix(self, tenant_id: str, subtenant_id: Optional[str] = None) -> str:
        """
        Generate a consistent prefix for tenant-based storage paths.
        
        Args:
            tenant_id: The tenant ID
            subtenant_id: The subtenant ID (optional)
        
        Returns:
            A formatted prefix string
        """
        if subtenant_id:
            return f"{tenant_id}/{subtenant_id}"
        return tenant_id
    
    def update_extraction_pattern(
        self, 
        channel: ChannelType, 
        strategy: TenantExtractionStrategy, 
        pattern: Any
    ):
        """
        Update extraction pattern for a specific channel and strategy.
        
        Args:
            channel: The channel type
            strategy: The extraction strategy
            pattern: The new pattern (regex string, list of field names, etc.)
        """
        if channel not in self.extraction_patterns:
            self.extraction_patterns[channel] = {}
        
        self.extraction_patterns[channel][strategy] = pattern
        logger.info(f"Updated extraction pattern for {channel.value} - {strategy.value}")


class TenantConfig:
    """
    Configuration management for tenant-specific settings and processing rules.
    """
    
    def __init__(self):
        self.tenant_configs = {}
        self._load_default_configs()
    
    def _load_default_configs(self):
        """Load default configurations for all tenants"""
        self.default_config = {
            'processing_rules': {
                'enable_duplicate_detection': True,
                'enable_metadata_validation': True,
                'enable_classification_confidence_check': True,
                'minimum_classification_confidence': 0.7,
                'minimum_metadata_confidence': 0.6,
                'force_qa_review': False,
                'enable_language_detection': True,
                'enable_handwriting_detection': True,
                'enable_summary_extraction': True
            },
            'queue_routing': {
                'use_tenant_specific_queues': False,
                'use_tenant_queues': False,  # Alias for compatibility
                'qa_queue_prefix': '',
                'upload_queue_prefix': '',
                'monitoring_queue_prefix': ''
            },
            'storage_settings': {
                'use_tenant_specific_buckets': False,
                'bucket_prefix': '',
                'folder_structure': 'tenant_id/subtenant_id',
                'retention_days': 365,
                'large_file_threshold': 100  # MB
            },
            'document_types': {
                'enabled_types': [],  # Empty means all types enabled
                'disabled_types': [],
                'custom_validation_rules': {},
                'enable_first_page_detection': True,
                'enable_last_page_detection': True,
                'enable_spacer_detection': True,
                'duplicate_threshold': 0.85,
                'enable_merging': True,
                'enable_rfa_merging': True,
                'custom_merging_rules': {}
            },
            'metadata_extraction': {
                'extraction_strategies': [],  # Use default strategies if empty
                'custom_patterns': {},
                'required_fields': {},
                'enable_summary_extraction': True
            },
            'upload_settings': {
                'use_tenant_paths': True,
                'custom_naming_convention': None,
                'destinations': {},
                'notifications': {}
            }
        }
    
    def get_tenant_config(self, tenant_id: str, subtenant_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get configuration for a specific tenant/subtenant combination.
        
        Args:
            tenant_id: The tenant ID
            subtenant_id: The subtenant ID (optional)
        
        Returns:
            Configuration dictionary for the tenant
        """
        config_key = f"{tenant_id}:{subtenant_id}" if subtenant_id else tenant_id
        
        if config_key in self.tenant_configs:
            return self.tenant_configs[config_key]
        elif tenant_id in self.tenant_configs:
            return self.tenant_configs[tenant_id]
        else:
            return self.default_config.copy()
    
    def set_tenant_config(self, tenant_id: str, config: Dict[str, Any], subtenant_id: Optional[str] = None):
        """
        Set configuration for a specific tenant/subtenant.
        
        Args:
            tenant_id: The tenant ID
            config: Configuration dictionary
            subtenant_id: The subtenant ID (optional)
        """
        config_key = f"{tenant_id}:{subtenant_id}" if subtenant_id else tenant_id
        
        # Merge with default config
        merged_config = self.default_config.copy()
        self._deep_merge(merged_config, config)
        
        self.tenant_configs[config_key] = merged_config
    
    def _deep_merge(self, target: Dict, source: Dict):
        """Deep merge two dictionaries"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value
    
    def get_processing_rule(self, tenant_id: str, rule_name: str, subtenant_id: Optional[str] = None) -> Any:
        """Get a specific processing rule for a tenant"""
        config = self.get_tenant_config(tenant_id, subtenant_id)
        return config.get('processing_rules', {}).get(rule_name)
    
    def should_use_tenant_queues(self, tenant_id: str, subtenant_id: Optional[str] = None) -> bool:
        """Check if tenant should use tenant-specific queues"""
        config = self.get_tenant_config(tenant_id, subtenant_id)
        return config.get('queue_routing', {}).get('use_tenant_specific_queues', False)
    
    def get_qa_queue_name(self, base_queue_name: str, tenant_id: str, subtenant_id: Optional[str] = None) -> str:
        """Get the QA queue name for a tenant"""
        if self.should_use_tenant_queues(tenant_id, subtenant_id):
            config = self.get_tenant_config(tenant_id, subtenant_id)
            prefix = config.get('queue_routing', {}).get('qa_queue_prefix', tenant_id)
            return f"{prefix}_{base_queue_name}"
        return base_queue_name
    
    def get_bucket_name(self, base_bucket_name: str, tenant_id: str, subtenant_id: Optional[str] = None) -> str:
        """Get the storage bucket name for a tenant"""
        config = self.get_tenant_config(tenant_id, subtenant_id)
        if config.get('storage_settings', {}).get('use_tenant_specific_buckets', False):
            prefix = config.get('storage_settings', {}).get('bucket_prefix', tenant_id)
            return f"{prefix}-{base_bucket_name}"
        return base_bucket_name
    
    def get_storage_path(self, tenant_id: str, subtenant_id: Optional[str] = None, filename: str = '') -> str:
        """Get the storage path for a tenant's files"""
        config = self.get_tenant_config(tenant_id, subtenant_id)
        folder_structure = config.get('storage_settings', {}).get('folder_structure', 'tenant_id/subtenant_id')
        
        # Replace placeholders
        path = folder_structure.replace('tenant_id', tenant_id)
        if subtenant_id:
            path = path.replace('subtenant_id', subtenant_id)
        else:
            path = path.replace('/subtenant_id', '').replace('subtenant_id/', '').replace('subtenant_id', '')
        
        if filename:
            path = f"{path}/{filename}" if path else filename
        
        return path.strip('/')


# Global tenant configuration instance
default_tenant_config = TenantConfig()


def get_tenant_aware_queue_name(tenant_id: Optional[str], base_queue_name: str, 
                                subtenant_id: Optional[str] = None) -> str:
    """
    Generate tenant-aware queue name for routing messages.
    
    Args:
        tenant_id: Tenant ID
        base_queue_name: Base queue name
        subtenant_id: Optional subtenant ID
    
    Returns:
        Tenant-aware queue name with fallback to base queue
    """
    if not tenant_id:
        return base_queue_name
    
    try:
        tenant_config = default_tenant_config.get_tenant_config(tenant_id, subtenant_id)
        
        # Check if tenant-specific queues are enabled
        if tenant_config.get('queue_routing', {}).get('use_tenant_queues', False):
            if subtenant_id:
                return f"{tenant_id}_{subtenant_id}_{base_queue_name}"
            else:
                return f"{tenant_id}_{base_queue_name}"
        else:
            # Use base queue for all tenants
            return base_queue_name
    except Exception as e:
        print(f"Error generating tenant-aware queue name: {e}")
        return base_queue_name


def get_tenant_processing_config(tenant_id: Optional[str] = None, 
                                subtenant_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Utility function to get tenant-specific processing configuration.
    
    Args:
        tenant_id: The tenant ID
        subtenant_id: The subtenant ID
    
    Returns:
        Processing configuration dictionary
    """
    if not tenant_id:
        return default_tenant_config.default_config['processing_rules']
    
    return default_tenant_config.get_tenant_config(tenant_id, subtenant_id).get('processing_rules', {})


def create_tenant_aware_message(base_message: Dict[str, Any], tenant_id: Optional[str] = None, 
                                subtenant_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Utility function to add tenant information to a message.
    
    Args:
        base_message: The base message dictionary
        tenant_id: The tenant ID
        subtenant_id: The subtenant ID
    
    Returns:
        Message with tenant information added
    """
    message = base_message.copy()
    if tenant_id:
        message['tenant_id'] = tenant_id
    if subtenant_id:
        message['subtenant_id'] = subtenant_id
    
    return message


# Default extractor instance
default_tenant_extractor = TenantInfoExtractor() 