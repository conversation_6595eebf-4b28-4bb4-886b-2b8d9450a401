from contextlib import contextmanager
from urllib.parse import quote_plus

from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.exc import SQLAlchemyError


class DBConnector:
    """
    Enhanced PostgreSQL database connector using SQLAlchemy.

    Required Parameters:
        pgsql_host: The hostname or IP address of the PostgreSQL server.
        pgsql_port: The port number on which PostgreSQL is listening.
        pgsql_username: Username for database authentication.
        pgsql_password: Password for database authentication.
        pgsql_db_name: Name of the PostgreSQL database.

    Optional:
        pgsql_sslmode: SSL mode to use for the connection (e.g., 'prefer', 'require', 'allow').

    Additional engine configuration parameters can be provided as keyword arguments.
    """

    def __init__(self,
                 pgsql_host: str,
                 pgsql_port: int,
                 pgsql_username: str,
                 pgsql_password: str,
                 pgsql_db_name: str,
                 pgsql_sslmode: str = None,
                 **engine_kwargs):
        # Validate required parameters.
        if not all([pgsql_host, pgsql_port, pgsql_username, pgsql_password, pgsql_db_name]):
            raise ValueError(
                "All of pgsql_host, pgsql_port, pgsql_username, pgsql_password, and pgsql_db_name must be provided.")

        # Encode credentials in case they have special characters.
        username = quote_plus(pgsql_username)
        password = quote_plus(pgsql_password)

        # Build the base connection string.
        connection_string = f"postgresql://{username}:{password}@{pgsql_host}:{pgsql_port}/{pgsql_db_name}"

        # Append sslmode if provided.
        if pgsql_sslmode:
            connection_string += f"?sslmode={pgsql_sslmode}"

        # Default engine options; these can be overridden via engine_kwargs.
        default_kwargs = {
            'pool_size': 20,  # Maximum number of persistent connections.
            'max_overflow': 0,  # Prevents connections beyond the pool_size.
            'pool_pre_ping': True,  # Validate connections before using them.
            'pool_timeout': 30,  # Wait at most 30 seconds for a connection.
            'pool_recycle': 1800,  # Recycle connections after 30 minutes.
            'echo': False  # Set to True to log all SQL statements.
        }
        default_kwargs.update(engine_kwargs)

        # Create the engine.
        self.engine = create_engine(connection_string, **default_kwargs)

        # Setup event listeners for connection pool events.
        self._setup_event_listeners()

        # Create a scoped session factory to ensure thread safety.
        self.SessionFactory = scoped_session(sessionmaker(bind=self.engine))
        print(f"DBConnector initialized with host: {pgsql_host}, db: {pgsql_db_name}")

    def _setup_event_listeners(self):
        """Setup SQLAlchemy event listeners to log connection pool events."""

        @event.listens_for(self.engine, "connect")
        def connect(dbapi_connection, connection_record):
            print(f"New DBAPI connection established: {connection_record}")

    def get_engine(self):
        """Return the SQLAlchemy engine."""
        return self.engine

    def get_session(self):
        """Return a new session from the scoped session factory."""
        return self.SessionFactory()

    @contextmanager
    def session_scope(self):
        """
        Provide a transactional scope for a series of operations.

        Example usage:
            with db_connector.session_scope() as session:
                # perform database operations using session
        """
        session = self.get_session()
        try:
            yield session
            session.commit()
        except SQLAlchemyError as exc:
            session.rollback()
            print(f"Session rollback due to SQLAlchemyError: {exc}")
            raise
        except Exception as exc:
            session.rollback()
            print(f"Session rollback due to unexpected exception: {exc}")
            raise
        finally:
            session.close()

    def run_transaction(self, func, *args, **kwargs):
        """
        Execute a function within a transactional session.

        Example:
            def my_query(session, param):
                # perform some DB operations
                return result
            result = db_connector.run_transaction(my_query, param)
        """
        with self.session_scope() as session:
            return func(session, *args, **kwargs)

    def test_connection(self):
        """
        Test the database connection by executing a simple query.

        Returns:
            True if the connection is successful, False otherwise.
        """
        try:
            with self.engine.connect() as conn:
                conn.execute("SELECT 1")
            print("Database connection test succeeded.")
            return True
        except Exception as exc:
            print(f"Database connection test failed: {exc}")
            return False

    def dispose(self):
        """
        Dispose the engine and its connection pool.
        """
        self.engine.dispose()
        print("Database engine disposed.")


