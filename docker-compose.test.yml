version: "3.8"

services:
  # Test database
  test-postgres:
    image: postgres:14
    container_name: test-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
      POSTGRES_DB: test_pipeline
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5439:5432"
    volumes:
      - test_postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d test_pipeline"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Test RabbitMQ
  test-rabbitmq:
    image: rabbitmq:3-management
    container_name: test-rabbitmq
    restart: unless-stopped
    environment:
      RABBITMQ_ERLANG_COOKIE: "test_cookie"
      RABBITMQ_DEFAULT_USER: "test_user"
      RABBITMQ_DEFAULT_PASS: "test_password"
      RABBITMQ_DEFAULT_VHOST: "/"
    ports:
      - "15673:15672"  # Management UI
      - "5673:5672"    # AMQP port
    volumes:
      - test_rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Test MinIO
  test-minio:
    image: minio/minio
    container_name: test-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: test_access_key
      MINIO_ROOT_PASSWORD: test_secret_key
    ports:
      - "9017:9000"  # API port
      - "9018:9001"  # Console port
    volumes:
      - test_minio_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Python test runner
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    container_name: test-runner
    depends_on:
      test-postgres:
        condition: service_healthy
      test-rabbitmq:
        condition: service_healthy
      test-minio:
        condition: service_healthy
    environment:
      # Database configuration
      PGSQL_HOST: test-postgres
      PGSQL_PORT: 5432
      PGSQL_USERNAME: test_user
      PGSQL_PASSWORD: test_password
      PGSQL_DB_NAME: test_pipeline
      
      # Remote database (same as local for tests)
      PGSQL_HOST_REMOTE: test-postgres
      PGSQL_PORT_REMOTE: 5432
      PGSQL_USERNAME_REMOTE: test_user
      PGSQL_PASSWORD_REMOTE: test_password
      PGSQL_DB_NAME_REMOTE: test_pipeline
      PGSQL_SSL_MODE_REMOTE: disable
      
      # RabbitMQ configuration
      RABBITMQ_HOST: test-rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USERNAME: test_user
      RABBITMQ_PASSWORD: test_password
      RABBITMQ_TO_CLASSIFY_QUEUE_NAME: test_to_classify
      RABBITMQ_TO_SPLIT_QUEUE_NAME: test_to_split
      RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME: test_to_extract_metadata
      RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME: test_to_metadata_postprocess
      RABBITMQ_TO_VALIDATE_QUEUE_NAME: test_to_validate
      RABBITMQ_TO_UPLOAD_QUEUE_NAME: test_to_upload
      RABBITMQ_TO_BACKEND_QA_QUEUE_NAME: test_to_backend_qa
      
      # MinIO configuration
      MINIO_URI: test-minio:9000
      MINIO_ACCESS_KEY: test_access_key
      MINIO_SECRET_KEY: test_secret_key
      MINIO_FILES_BUCKET: test-files
      MINIO_SECURE: "false"
      
      # Tenant configuration
      DEFAULT_TENANT_ID: test_default
      DEFAULT_SUBTENANT_ID: test_default
      TENANT_EXTRACTION_ENABLED: "true"
      
      # Testing flags
      TESTING: "true"
      PYTHONPATH: "/app"
    volumes:
      - .:/app
      - test_results:/app/test_results
    working_dir: /app
    command: ["bash", "/app/run_tests.sh"]

volumes:
  test_postgres_data:
  test_rabbitmq_data:
  test_minio_data:
  test_results:

networks:
  default:
    name: test-pipeline-network 