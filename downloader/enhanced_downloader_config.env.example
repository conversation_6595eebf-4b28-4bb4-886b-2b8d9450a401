# Enhanced Downloader Configuration for Multi-Tenant Support
# Copy this file to .env and customize the values for your environment

# === EXISTING CONFIGURATION (from original downloader) ===

# Project and app configuration
PROJECT_NAME=ml-pipeline
APP_NAME=downloader
TENANT_NAME=default

# MinIO configuration
MINIO_URI=127.0.0.1:9015
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key
MINIO_FILES_BUCKET=from-sftp
MINIO_SECURE=true

# SFTP Remote configuration
SFTP_DISABLED=false
SFTP_REMOTE_HOST=127.0.0.1
SFTP_REMOTE_PORT=2222
SFTP_REMOTE_USER=sftp_user
SFTP_REMOTE_PASSWORD=sftp_password
SFTP_REMOTE_PATH=Documents

# RabbitMQ configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5682
RABBITMQ_USERNAME=rabbit_user
RABBITMQ_PASSWORD=rabbit_password
RABBITMQ_TO_CLASSIFY_QUEUE_NAME=to_classify

# Remote RabbitMQ configuration
REMOTE_RABBITMQ_HOST=remote.rabbitmq.host
REMOTE_RABBITMQ_PORT=5671
REMOTE_RABBITMQ_VHOST=/
REMOTE_RABBITMQ_USERNAME=remote_rabbit_user
REMOTE_RABBITMQ_PASSWORD=remote_rabbit_password
REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME=packet_status

# PostgreSQL configuration (Local)
PGSQL_HOST=localhost
PGSQL_PORT=5438
PGSQL_USERNAME=postgres_user
PGSQL_PASSWORD=postgres_password
PGSQL_DB_NAME=ml_pipeline_local

# Remote PostgreSQL configuration
PGSQL_HOST_REMOTE=remote.postgres.host
PGSQL_PORT_REMOTE=5432
PGSQL_USERNAME_REMOTE=remote_postgres_user
PGSQL_PASSWORD_REMOTE=remote_postgres_password
PGSQL_DB_NAME_REMOTE=ml_pipeline_remote
PGSQL_SSL_MODE_REMOTE=require

# Downloader configuration
SUPPORTED_EXTENTIONS=pdf,tif,tiff,rtf,docx
SUPPORTED_EXTENTIONS_CONTAINERS=zip,eml,msg
SUPPORTED_EXTENTIONS_DOCUMENTS_PREPROCESS=tif,tiff,rtf,docx

# Monitoring configuration
MONITOR_HOST=http://monitoring.host
MONITOR_PORT=8080

# Service Bus configuration
SERVICE_BUS_CONNECTION_STRING=Endpoint=sb://your-namespace.servicebus.windows.net/;SharedAccessKeyName=your-key-name;SharedAccessKey=your-key
TOPIC_NAME=your-topic-name
SUBSCRIPTION_NAME=your-subscription-name
SERVICE_BUS_QUEUE_NAME=your-queue-name

# S3 configuration
S3_BUCKET_NAME=your-s3-bucket
S3_REGION=us-east-1
S3_ENDPOINT_URL=null

# SSL configuration for RabbitMQ
REMOTE_SSL_CAFILE_PATH=/path/to/ca.pem
REMOTE_SSL_CERTFILE_PATH=/path/to/cert.pem
REMOTE_SSL_KEYFILE_PATH=/path/to/key.pem

# === NEW CONFIGURATION FOR MULTI-TENANT SUPPORT ===

# Channel configuration - choose one: sftp, servicebus_topic, servicebus_queue, sqs
CHANNEL=sftp

# SQS configuration (only needed if CHANNEL=sqs)
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/your-queue-name
SQS_REGION=us-east-1
SQS_ACCESS_KEY_ID=your_aws_access_key_id
SQS_SECRET_ACCESS_KEY=your_aws_secret_access_key
SQS_MAX_MESSAGES=1
SQS_WAIT_TIME=5

# Tenant configuration
DEFAULT_TENANT_ID=default
DEFAULT_SUBTENANT_ID=default
TENANT_EXTRACTION_ENABLED=true

# === TENANT EXTRACTION CONFIGURATION EXAMPLES ===

# For SFTP: Files should be organized in folder structure like:
# - tenant1/subtenant1/document.pdf
# - tenant2/subtenant2/document.pdf
# Or use filename patterns like:
# - tenant1_subtenant1_document.pdf

# For ServiceBus: Messages should include tenant information in:
# - Message properties: tenant_id, subtenant_id
# - Message body: tenantId, subtenantId

# For SQS: Messages should include tenant information in:
# - Message attributes: tenant_id, subtenant_id  
# - Message body: tenantId, subtenantId

# === EXAMPLE MESSAGE FORMATS ===

# ServiceBus message format:
# {
#   "sasUri": "https://storage.blob.core.windows.net/container/file.pdf?token",
#   "blobName": "file.pdf",
#   "tenantId": "tenant1",
#   "subtenantId": "subtenant1"
# }

# SQS message format:
# {
#   "fileUrl": "https://s3.amazonaws.com/bucket/file.pdf?token",
#   "fileName": "file.pdf",
#   "tenantId": "tenant1", 
#   "subtenantId": "subtenant1"
# }

# === MIGRATION NOTES ===

# 1. Run the database migration first:
#    python migrations/add_tenant_support_migration.py

# 2. Ensure tenant tables are populated:
#    INSERT INTO tenants (tenant_id, tenant_name) VALUES ('tenant1', 'Tenant 1');
#    INSERT INTO subtenants (subtenant_id, tenant_id, subtenant_name) VALUES ('subtenant1', 'tenant1', 'Subtenant 1');

# 3. Update your data sources to include tenant information:
#    - SFTP: Organize files in tenant/subtenant folder structure
#    - ServiceBus/SQS: Include tenant info in message properties or body

# 4. Test with TENANT_EXTRACTION_ENABLED=false first to use default tenant
#    Then enable tenant extraction once your data sources are ready 