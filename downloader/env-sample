# Project and app configuration
PROJECT_NAME=record_ranger
APP_NAME=downloader
TENANT_NAME=your_tenant_name

# MinIO configuration
MINIO_URI=127.0.0.1:9015
MINIO_ACCESS_KEY=your_access_key
MINIO_SECRET_KEY=your_secret_key
MINIO_FILES_BUCKET=from-sftp
MINIO_SECURE=true

# SFTP Remote configuration
SFTP_REMOTE_HOST=127.0.0.1
SFTP_REMOTE_PORT=2222
SFTP_REMOTE_USER=your_sftp_user
SFTP_REMOTE_PASSWORD=your_sftp_password
SFTP_REMOTE_PATH=Documents

# RabbitMQ configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5682
RABBITMQ_USERNAME=your_rabbitmq_user
RABBITMQ_PASSWORD=your_rabbitmq_password
RABBITMQ_TO_CLASSIFY_QUEUE_NAME=to_classify

# Remote RabbitMQ configuration
REMOTE_RABBITMQ_HOST=your_remote_host
REMOTE_RABBITMQ_PORT=5671
REMOTE_RABBITMQ_VHOST=/
REMOTE_RABBITMQ_USERNAME=your_remote_user
REMOTE_RABBITMQ_PASSWORD=your_remote_password
REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME=your_status_queue

# PostgreSQL configuration
PGSQL_HOST=localhost
PGSQL_PORT=5438
PGSQL_USERNAME=your_db_user
PGSQL_PASSWORD=your_db_password
PGSQL_DB_NAME=your_db_name

# Remote PostgreSQL configuration
PGSQL_HOST_REMOTE=your_remote_db_host
PGSQL_PORT_REMOTE=5432
PGSQL_USERNAME_REMOTE=your_remote_db_user
PGSQL_PASSWORD_REMOTE=your_remote_db_password
PGSQL_DB_NAME_REMOTE=your_remote_db_name
PGSQL_SSL_MODE_REMOTE=require

# Downloader configuration
SUPPORTED_EXTENTIONS=pdf,tif,tiff,rtf,docx
SUPPORTED_EXTENTIONS_CONTAINERS=
SUPPORTED_EXTENTIONS_DOCUMENTS_PREPROCESS=tif,tiff,rtf,docx
CHANNEL=sftp

# Monitoring configuration
MONITOR_HOST=http://your_monitor_host
MONITOR_PORT=your_monitor_port

# Service Bus configuration
SERVICE_BUS_CONNECTION_STRING=your_connection_string
TOPIC_NAME=your_topic_name
SUBSCRIPTION_NAME=your_subscription_name
SERVICE_BUS_QUEUE_NAME=your_queue_name

# S3 configuration
S3_BUCKET_NAME=your_s3_bucket
S3_REGION=us-east-1
S3_ENDPOINT_URL=null

# SSL configuration for RabbitMQ
REMOTE_SSL_CAFILE_PATH=/etc/nginx/ssl/your_ca_file.pem
REMOTE_SSL_CERTFILE_PATH=/etc/nginx/ssl/your_cert_file.pem
REMOTE_SSL_KEYFILE_PATH=/etc/nginx/ssl/your_key_file.pem 