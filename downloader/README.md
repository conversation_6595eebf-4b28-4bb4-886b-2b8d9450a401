# Docker manual for downloader

## Building a Docker for downloader module

1. Start build by 
```docker build -t dx-downloader-app .```

## Using a Docker image for downloader module

The downloader now uses environment variables instead of config.yml for configuration.

1. Set up environment variables for all required configuration parameters:

```
# Example of setting environment variables
export MINIO_URI=127.0.0.1:9015
export MINIO_ACCESS_KEY=your_access_key
export MINIO_SECRET_KEY=your_secret_key
# ... and so on for all required variables
```

2. Mount pgSQL models configuration 
```-v ./{local_path}/models/:/models/```

3. Pass ports required for external services using `-p` option or use `--network host` to use host network
```-p 5438:5438 -p 5682:5682 -p ...```

4. Run the Docker container with environment variables:

```
docker run \
  -e MINIO_URI=${MINIO_URI} \
  -e MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY} \
  -e MINIO_SECRET_KEY=${MINIO_SECRET_KEY} \
  -e SFTP_REMOTE_HOST=${SFTP_REMOTE_HOST} \
  # ... other environment variables
  --network host \
  dx-downloader-app
```

Alternatively, you can use an environment file:

```
docker run --env-file .env --network host dx-downloader-app
```

## Required Environment Variables

Here are the key categories of environment variables needed:

- Project and app configuration (PROJECT_NAME, APP_NAME, TENANT_NAME)
- MinIO configuration (MINIO_URI, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, etc.)
- SFTP Remote configuration (SFTP_REMOTE_HOST, SFTP_REMOTE_PORT, etc.)
- RabbitMQ configuration (RABBITMQ_HOST, RABBITMQ_PORT, etc.)
- Remote RabbitMQ configuration (REMOTE_RABBITMQ_HOST, etc.)
- PostgreSQL configuration (PGSQL_HOST, PGSQL_PORT, etc.)
- Remote PostgreSQL configuration (PGSQL_HOST_REMOTE, etc.)
- Downloader configuration (SUPPORTED_EXTENTIONS, CHANNEL, etc.)
- Monitoring configuration (MONITOR_HOST, MONITOR_PORT)
- Service Bus configuration (SERVICE_BUS_CONNECTION_STRING, etc.)
- S3 configuration (S3_BUCKET_NAME, S3_REGION, etc.)
- SSL configuration for RabbitMQ (REMOTE_SSL_CAFILE_PATH, etc.)