FROM 112623991000.dkr.ecr.us-east-2.amazonaws.com/python:3.10.15

RUN apt-get update && apt-get install -y poppler-utils libreoffice-base-nogui

WORKDIR /app

# Copy requirements and install dependencies
COPY downloader/requirements.txt /app/requirements.txt
RUN python3 -m pip install -r requirements.txt

# Copy application files
COPY downloader/downloader.py /app/downloader.py
COPY pipeline_utils/ /app/pipeline_utils/
COPY models/ /app/models/

# Start the application
CMD ["python3", "-u", "downloader.py"]
