from __future__ import annotations

import io
import logging
from dataclasses import dataclass
from typing import Optional

import boto3
from botocore.config import Config as BotoConfig
from botocore.exceptions import BotoCoreError, ClientError

from google.cloud import storage as gcs
from google.api_core import exceptions as gcs_exc

_LOG = logging.getLogger("downloader.raw")


@dataclass(frozen=True, slots=True)
class RawCfg:
    type: str  # "gcp" | "s3"
    prefix: str = ""

    # gcp-specific
    gcp_bucket: str | None = None
    gcp_credentials_path: str | None = None

    # s3-specific
    s3_bucket: str | None = None
    s3_region: str | None = None
    s3_secure: bool | None = None
    s3_endpoint: str | None = None


class StorageError(RuntimeError):
    ...


class _BaseStorage:
    """Duck-typed base; concrete classes implement upload/download."""

    def upload(self, data: bytes, key: str) -> bool:
        raise NotImplementedError

    def download(self, key: str) -> Optional[bytes]:
        raise NotImplementedError


class _GCPStorage(_BaseStorage):
    def __init__(self, cfg: RawCfg) -> None:
        if cfg.gcp_credentials_path:
            self._client = gcs.Client.from_service_account_json(cfg.gcp_credentials_path)
        else:
            self._client = gcs.Client()
        self._bucket = self._client.bucket(cfg.gcp_bucket)
        self._prefix = cfg.prefix.rstrip("/")

    # internal helper
    def _path(self, key: str) -> str:
        return f"{self._prefix}/{key}" if self._prefix else key

    # public API
    def upload(self, data: bytes, key: str) -> bool:
        blob = self._bucket.blob(self._path(key))
        try:
            buf = io.BytesIO(data)
            blob.upload_from_file(buf)  # default rewind=False → buf at pos0
            return True
        except gcs_exc.GoogleAPIError as exc:
            _LOG.error("GCP upload failed: %s", exc)
            return False

    def download(self, key: str) -> Optional[bytes]:
        blob = self._bucket.blob(self._path(key))
        try:
            return blob.download_as_bytes()
        except gcs_exc.NotFound:
            _LOG.warning("GCP object %s not found", key)
            return None
        except gcs_exc.GoogleAPIError as exc:
            _LOG.error("GCP download error: %s", exc)
            return None


class _S3Storage(_BaseStorage):
    def __init__(self, cfg: RawCfg) -> None:
        self._bucket = cfg.s3_bucket
        self._prefix = cfg.prefix.rstrip("/")
        self._client = boto3.client(
            "s3",
            region_name=cfg.s3_region,
            endpoint_url=cfg.s3_endpoint or None,
            use_ssl=True if cfg.s3_secure is None else bool(cfg.s3_secure),
            config=BotoConfig(retries={"max_attempts": 6, "mode": "standard"}),
        )

    def _path(self, key: str) -> str:
        return f"{self._prefix}/{key}" if self._prefix else key

    def upload(self, data: bytes, key: str) -> bool:
        try:
            self._client.upload_fileobj(
                io.BytesIO(data),
                Bucket=self._bucket,
                Key=self._path(key),
                ExtraArgs={"ACL": "private"},
            )
            return True
        except (BotoCoreError, ClientError) as exc:
            _LOG.error("S3 upload failed: %s", exc)
            return False

    def download(self, key: str) -> Optional[bytes]:
        buf = io.BytesIO()
        try:
            self._client.download_fileobj(
                Bucket=self._bucket,
                Key=self._path(key),
                Fileobj=buf,
            )
            return buf.getvalue()
        except ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchKey":
                _LOG.warning("S3 object %s not found", key)
                return None
            _LOG.error("S3 download error: %s", e)
            return None


class RawStorage:
    """Factory + façade returning GCP or S3 implementation transparently."""

    def __init__(self, backend: _BaseStorage):
        self._backend = backend

    # public delegate methods
    def upload(self, data: bytes, key: str) -> bool:
        return self._backend.upload(data, key)

    def download(self, key: str) -> Optional[bytes]:
        return self._backend.download(key)

    # factory
    @classmethod
    def from_config(cls, cfg_dict: dict) -> "RawStorage":
        cfg = RawCfg(**cfg_dict)
        if cfg.type == "gcp":
            backend = _GCPStorage(cfg)
        elif cfg.type == "s3":
            backend = _S3Storage(cfg)
        else:
            raise StorageError(f"Unsupported raw_storage.type '{cfg.type}'")
        return cls(backend)
