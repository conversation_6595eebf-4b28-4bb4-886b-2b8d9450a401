name: atom_downloader
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - ca-certificates=2023.12.12=h06a4308_0
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.4.4=h6a678d5_0
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - ncurses=6.4=h6a678d5_0
  - openssl=3.0.12=h7f8727e_0
  - pip=23.3.1=py39h06a4308_0
  - python=3.9.18=h955ad1f_0
  - readline=8.2=h5eee18b_0
  - setuptools=68.2.2=py39h06a4308_0
  - sqlite=3.41.2=h5eee18b_0
  - tk=8.6.12=h1ccaba5_0
  - tzdata=2023d=h04d1e81_0
  - wheel=0.41.2=py39h06a4308_0
  - xz=5.4.5=h5eee18b_0
  - zlib=1.2.13=h5eee18b_0
  - pip:
      - argon2-cffi==23.1.0
      - argon2-cffi-bindings==21.2.0
      - bcrypt==4.1.2
      - certifi==2023.11.17
      - cffi==1.16.0
      - cryptography==41.0.7
      - greenlet==3.0.3
      - minio==7.2.9
      - paramiko==3.4.0
      - pika==1.3.2
      - psycopg2-binary==2.9.9
      - pycparser==2.21
      - pycryptodome==3.20.0
      - pynacl==1.5.0
      - pyyaml==6.0.1
      - sqlalchemy==2.0.35
      - sqlalchemy-utils==0.41.1
      - typing-extensions==4.9.0
      - urllib3==2.1.0
      - uuid6==2024.1.12
prefix: /home/<USER>/miniconda3/envs/atom_downloader
