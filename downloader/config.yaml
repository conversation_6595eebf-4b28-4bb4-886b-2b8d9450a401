project_name: "record_ranger" # for monitoring service
app_name: "downloader"
CRYPTOGRAPHY_KEY: 'your-encryption-key-here' # ENCRYPTION_KEY = Fernet.generate_key()
TENANT_NAME: "default"

s3:
    S3_BUCKET_NAME: "record-ranger-files"
    S3_REGION: "us-east-1"
    S3_ENDPOINT_URL: null  # Set to null for AWS S3, or specify custom endpoint for S3-compatible storage

minio:
    MINIO_URI: "127.0.0.1:9015"
    MINIO_ACCESS_KEY: "minioadmin"
    MINIO_SECRET_KEY: "minioadmin"
    MINIO_FILES_BUCKET: 'from-sftp'
    MINIO_OBJECT_URI_PREFIX: 'files'
    MINIO_SECURE: true
    MINIO_EXPIRATION_TIME: 1209600 # its 14 days in sec
    MINIO_PRESIGNED_URL_HOST: "localhost:9015" # template: "server.domain.com:9015/"
    MINIO_PRESIGNED_URL_SECURE: true

sftp_remote:
    SFTP_HOST: "127.0.0.1"
    SFTP_PORT: 2222
    SFTP_USER: "sftp_user"
    SFTP_PASSWORD: "sftp_password"
    SFTP_PATH: 'Documents'

sftp_upload:
    SFTP_HOST: "127.0.0.1"
    SFTP_PORT: 2223
    SFTP_USER: "sftp_user"
    SFTP_PASSWORD: "sftp_password"
    SFTP_PATH: 'Documents'
    SORT_NAMING_BY_DATE: False  # True / False if client=ANS SORT_NAMING_BY_DATE=True

rabbitmq:
    RABBITMQ_HOST: "localhost"
    RABBITMQ_PORT: 5672
    RABBITMQ_USERNAME: "guest"
    RABBITMQ_PASSWORD: "guest"
    RABBITMQ_TO_CLASSIFY_QUEUE_NAME: 'to_classify'
    RABBITMQ_TO_SPLIT_QUEUE_NAME: 'to_split'
    RABBITMQ_TO_EXTRACT_METADATA_QUEUE_NAME: 'to_extract_metadata'
    RABBITMQ_TO_METADATA_POSTPROCESS_QUEUE_NAME: 'to_metadata_postprocess'
    RABBITMQ_TO_VALIDATE_QUEUE_NAME: 'to_validate'
    RABBITMQ_TO_QA_POSTPROCESS_QUEUE_NAME: 'to_qa_postprocess'
    RABBITMQ_TO_UPLOAD_QUEUE_NAME: 'to_upload'
    REMOTE_RABBITMQ_HOST: "localhost"
    REMOTE_RABBITMQ_PORT: 5672
    REMOTE_RABBITMQ_USERNAME: "guest"
    REMOTE_RABBITMQ_PASSWORD: "guest"
    REMOTE_RABBITMQ_VHOST: "/"
    REMOTE_RABBITMQ_TO_BACKEND_QA_QUEUE_NAME: 'to_backend_qa'
    REMOTE_RABBITMQ_FROM_BACKEND_QA_QUEUE_NAME: "from_backend_qa"
    REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME: "packet_status"
    REMOTE_RABBITMQ_FROM_BACKEND_SCREENSHOT: "from_backend_screenshot"

pgsql:
    PGSQL_HOST: "localhost"
    PGSQL_PORT: 5438
    PGSQL_USERNAME: "postgres"
    PGSQL_PASSWORD: "postgres"
    PGSQL_DB_NAME: "record_ranger"
    PGSQL_HOST_REMOTE: "localhost"
    PGSQL_PORT_REMOTE: 5438
    PGSQL_USERNAME_REMOTE: "postgres"
    PGSQL_PASSWORD_REMOTE: "postgres"
    PGSQL_DB_NAME_REMOTE: "record_ranger_remote"
    PGSQL_SSL_MODE_REMOTE: "prefer"
    RETENTION_HOURS: 340
    RETENTION_HOURS_REMOTE: 740

monitoring:
    MONITOR_HOST: "http://localhost"
    MONITOR_PORT: 8080

downloader:
    SUPPORTED_EXTENTIONS: ['pdf', 'tif', 'tiff', 'rtf', 'docx'] #['pdf', 'tif', 'tiff', 'msg', 'rtf', 'eml', 'zip', 'docx']
    SUPPORTED_EXTENTIONS_CONTAINERS: [] #['msg', 'eml', 'zip',]
    SUPPORTED_EXTENTIONS_DOCUMENTS_PREPROCESS: ['tif', 'tiff', 'rtf', 'docx']
    CHANNEL: "sftp" # "servicebus_queue" or "servicebus_topic" or "sftp"

classifier:
    DEVICE: cuda:0 # usually cpu or cuda:0
    GENERAL_MODEL_PATH: './general_model.pkl'
    PACKET_MODEL_PATH: './packet_model.pkl'

splitter:
    MINIMAL_CONFIDENCE_THRESHOLD: 0.5

metadata_extractor:
    DEVICE: cuda:0 # usually cpu or cuda:0


validate_and_route:
    MINIMAL_CLASSIFICATION_CONFIDENCE: 0.985
    MINIMAL_METADATA_CONFIDENCE: 0.985
    FORCE_ROUTE: 1 # 0 - disable; 1 - force all to qa tool; 2 - force all to upload
    MINIMUM_SIZE_OF_LARGE_FILE: 250
    METADATA_DISPLAY: ['namingData', 'metaData']   # ['namingData', 'metaData'] to be displayed on the frontend
    CHUNK_SIZE: 200  # default median size of chunk
    CHUNK_SIZE_RANGE: 100     # chunk can be CHUNK_SIZE +/- CHUNK_SIZE_RANGE
    HEADER_CONF_THRESHOLD: 0.91    # minimal conf of 1st page classification nodel

uploader:
    ADD_VALIDATION_REPORT_DATA: no
    CHANNEL: "sftp" # "servicebus_topic" or "sftp"

azure_uploader:
    AZURE_STORAGE_CONNECTION_STRING: "DefaultEndpointsProtocol=https;AccountName=example;AccountKey=yourkey;EndpointSuffix=core.windows.net"
    AZURE_STORAGE_CONTAINER_NAME: "record-ranger"
    SERVICE_BUS_CONNECTION_STRING: 'Endpoint=sb://example.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=yourkey'
    SERVICE_BUS_TOPIC_NAME: 'record-ranger-upload-topic'

servicebus:
    SERVICE_BUS_CONNECTION_STRING: 'Endpoint=sb://example.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=yourkey'
    TOPIC_NAME: 'record-ranger-topic'
    QUEUE_NAME: 'record-ranger-queue'
    SUBSCRIPTION_NAME: 'record-ranger-subscription'
