[pytest]
testpaths = tests/test_classifier
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: mark a test as a unit test
    integration: mark a test as an integration test
    slow: mark a test as slow
    pdf: mark a test that processes PDF files
    ocr: mark a test that uses OCR
    minio: mark a test that uses Minio
    rabbitmq: mark a test that uses RabbitMQ
    database: mark a test that uses the database
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
