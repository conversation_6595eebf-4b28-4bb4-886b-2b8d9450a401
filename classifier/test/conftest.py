"""
Fixtures for classifier tests.
"""
import io
import json
import os
from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest
import yaml
import pandas as pd
from minio import Minio
from PIL import Image
import numpy as np
import fitz
from uuid6 import uuid7

# Path to test data directory
TEST_DATA_DIR = os.path.join(os.path.dirname(__file__), "test_data")


@pytest.fixture
def mock_config():
    """Mock configuration."""
    return {
        "minio": {
            "MINIO_URI": "localhost:9000",
            "MINIO_ACCESS_KEY": "minioadmin",
            "MINIO_SECRET_KEY": "minioadmin",
            "MINIO_FILES_BUCKET": "test-bucket",
            "MINIO_SECURE": False,
        },
        "rabbitmq": {
            "RABBITMQ_HOST": "localhost",
            "RABBITMQ_PORT": 5672,
            "RABBITMQ_USERNAME": "guest",
            "RABBITMQ_PASSWORD": "guest",
            "RABBITMQ_TO_CLASSIFY_QUEUE_NAME": "to_classify",
            "RABBITMQ_TO_SPLIT_QUEUE_NAME": "to_split",
        },
        "pgsql": {
            "PGSQL_HOST": "localhost",
            "PGSQL_PORT": 5432,
            "PGSQL_USERNAME": "postgres",
            "PGSQL_PASSWORD": "postgres",
            "PGSQL_DB_NAME": "test_db",
        },
        "classifier": {
            "GENERAL_MODEL_PATH": "general_model.pkl",
            "PACKET_MODEL_PATH": "packet_model.pkl",
        },
        "monitoring": {
            "MONITOR_HOST": "localhost",
            "MONITOR_PORT": 8000,
        },
        "project_name": "test_project",
        "app_name": "test_app",
    }


@pytest.fixture
def mock_yaml_load(mock_config):
    """Mock yaml.safe_load to return mock config."""
    with patch("yaml.safe_load") as mock_load:
        mock_load.return_value = mock_config
        yield mock_load


@pytest.fixture
def mock_minio_client():
    """Mock Minio client."""
    with patch("minio.Minio") as mock_minio:
        mock_client = MagicMock()
        mock_minio.return_value = mock_client
        yield mock_client


@pytest.fixture
def mock_rmq_service():
    """Mock RabbitMQ service."""
    with patch("pipeline_utils.rabbitmq_connector.PikaServiceFactory") as mock_factory:
        mock_service = MagicMock()
        mock_factory.return_value.create_service.return_value = mock_service
        yield mock_service


@pytest.fixture
def mock_db_connector():
    """Mock database connector."""
    with patch("pipeline_utils.database_connector.DBConnector") as mock_db:
        mock_connector = MagicMock()
        mock_db.return_value = mock_connector
        mock_connector.session_scope.return_value.__enter__.return_value = MagicMock()
        yield mock_connector


@pytest.fixture
def mock_monitor_service():
    """Mock monitor service."""
    with patch("pipeline_utils.monitoring.MonitorService") as mock_monitor:
        mock_service = MagicMock()
        mock_monitor.return_value = mock_service
        yield mock_service


@pytest.fixture
def mock_ocr():
    """Mock PaddleOCR."""
    with patch("paddleocr.PaddleOCR") as mock_paddle:
        mock_ocr = MagicMock()
        mock_paddle.return_value = mock_ocr
        # Configure mock OCR to return sample text
        mock_ocr.ocr.return_value = [
            [
                [
                    [[0, 0], [100, 0], [100, 20], [0, 20]],
                    ["Sample OCR text", 0.99],
                ],
                [
                    [[0, 30], [100, 30], [100, 50], [0, 50]],
                    ["More sample text", 0.98],
                ],
            ]
        ]
        yield mock_ocr


@pytest.fixture
def mock_model():
    """Mock Model class."""
    with patch("classifier.classifier.Model") as mock_model_class:
        mock_model = MagicMock()
        mock_model_class.return_value = mock_model

        # Configure predict method to return sample predictions
        mock_model.predict.return_value = pd.DataFrame([
            {"class": 1, "confidence": 0.95, "Text": "Sample text 1"},
            {"class": 2, "confidence": 0.85, "Text": "Sample text 2"},
        ])

        # Configure predict_packet method to return sample predictions
        mock_model.predict_packet.return_value = [
            {"class": 1, "confidence": 0.95},
            {"class": 2, "confidence": 0.85},
        ]

        # Configure _classes attribute
        mock_model._classes = {
            0: 'Other',
            1: 'RFA',
            2: 'Medical Records',
        }

        yield mock_model


@pytest.fixture
def sample_pdf_bytes():
    """Create a sample PDF file in memory."""
    # Create a simple PDF with text
    doc = fitz.open()
    page = doc.new_page()
    page.insert_text((50, 50), "Sample PDF text for testing")
    page.insert_text((50, 70), "Second line of text")

    # Save to bytes
    pdf_bytes = doc.write()
    doc.close()

    return pdf_bytes


@pytest.fixture
def sample_pdf_file(sample_pdf_bytes, tmp_path):
    """Save sample PDF to a file and return the path."""
    pdf_path = tmp_path / "sample.pdf"
    with open(pdf_path, "wb") as f:
        f.write(sample_pdf_bytes)
    return pdf_path


@pytest.fixture
def mock_message_tuple():
    """Create a mock message tuple (ch, method, properties, body)."""
    ch = MagicMock()
    method = MagicMock()
    method.delivery_tag = "test-tag"
    properties = MagicMock()

    # Create message body
    message = {
        "file_id": str(uuid7()),
        "filename": "test_file.pdf"
    }
    body = json.dumps(message).encode("utf-8")

    return ch, method, properties, body


@pytest.fixture
def mock_document():
    """Create a mock Document object."""
    document = MagicMock()
    document.uuid = str(uuid7())
    document.filename = "test_file.pdf"
    document.status = "to_classify"
    return document


@pytest.fixture
def mock_perform_ocr(mock_ocr):
    """Mock the perform_ocr function."""
    with patch("classifier.classifier.perform_ocr") as mock_perform:
        # Return sample OCR results
        mock_perform.return_value = [
            [
                [
                    [[0, 0], [100, 0], [100, 20], [0, 20]],
                    ["Sample OCR text", 0.99],
                ],
                [
                    [[0, 30], [100, 30], [100, 50], [0, 50]],
                    ["More sample text", 0.98],
                ],
            ]
        ]
        yield mock_perform


@pytest.fixture
def mock_classify():
    """Mock the classify function."""
    with patch("classifier.classifier.classify") as mock_classify_func:
        # Return sample classification results
        predictions = [
            {"class": 1, "confidence": 0.95},
            {"class": 2, "confidence": 0.85},
        ]
        predictions_header = [
            {"class": 23, "confidence": 0.90},
            {"class": 24, "confidence": 0.80},
        ]
        texts = [
            [
                [
                    [[0, 0], [100, 0], [100, 20], [0, 20]],
                    ["Sample OCR text", 0.99],
                ],
            ],
            [
                [
                    [[0, 30], [100, 30], [100, 50], [0, 50]],
                    ["More sample text", 0.98],
                ],
            ],
        ]
        mock_classify_func.return_value = (predictions, predictions_header, texts)
        yield mock_classify_func


@pytest.fixture
def mock_update_document_record_in_db():
    """Mock the update_document_record_in_db function."""
    with patch("classifier.classifier.update_document_record_in_db") as mock_update:
        yield mock_update
