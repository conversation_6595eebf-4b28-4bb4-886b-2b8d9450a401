"""
Tests for PDF processing functionality in classifier.py.
"""
import io
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
import pandas as pd
import numpy as np
from PIL import Image

# Import the functions directly
# These will be patched in the tests
from classifier.classifier import perform_ocr, classify


class TestPerformOCR:
    """Test class for the perform_ocr function."""

    def test_perform_ocr_with_pdf(self, sample_pdf_bytes, mock_ocr):
        """Test OCR processing of a PDF file."""
        # Call the perform_ocr function with sample PDF bytes
        result = perform_ocr(sample_pdf_bytes)

        # Verify OCR was called
        assert mock_ocr.ocr.called

        # Verify result is a list
        assert isinstance(result, list)

    def test_perform_ocr_with_empty_pdf(self, mock_ocr):
        """Test OCR processing of an empty PDF file."""
        # Create an empty PDF
        empty_pdf = b"%PDF-1.7\n%\xe2\xe3\xcf\xd3\n1 0 obj\n<</Type/Catalog/Pages 2 0 R>>\nendobj\n2 0 obj\n<</Type/Pages/Kids[]/Count 0>>\nendobj\nxref\n0 3\n0000000000 65535 f \n0000000015 00000 n \n0000000060 00000 n \ntrailer\n<</Size 3/Root 1 0 R>>\nstartxref\n110\n%%EOF\n"

        # Configure mock OCR to return empty result
        mock_ocr.ocr.return_value = [[]]

        # Call the perform_ocr function with empty PDF
        result = perform_ocr(empty_pdf)

        # Verify OCR was called
        assert mock_ocr.ocr.called

        # Verify result is a list
        assert isinstance(result, list)
        assert len(result) > 0  # Should have at least one page (empty)

    def test_perform_ocr_with_multi_page_pdf(self, mock_ocr):
        """Test OCR processing of a multi-page PDF file."""
        # Create a multi-page PDF
        import fitz
        doc = fitz.open()
        for i in range(3):  # 3 pages
            page = doc.new_page()
            page.insert_text((50, 50), f"Page {i+1} text")
        
        multi_page_pdf = doc.write()
        doc.close()

        # Configure mock OCR to return different results for each page
        mock_ocr.ocr.side_effect = [
            [
                [
                    [[0, 0], [100, 0], [100, 20], [0, 20]],
                    [f"Page {i+1} text", 0.99],
                ]
            ]
            for i in range(3)
        ]

        # Call the perform_ocr function with multi-page PDF
        result = perform_ocr(multi_page_pdf)

        # Verify OCR was called multiple times (once per page)
        assert mock_ocr.ocr.call_count == 3

        # Verify result is a list with multiple items
        assert isinstance(result, list)
        assert len(result) == 3


class TestClassify:
    """Test class for the classify function."""

    def test_classify_with_pdf(
        self, sample_pdf_bytes, mock_ocr, mock_model
    ):
        """Test classification of a PDF file."""
        # Call the classify function with sample PDF bytes
        with patch("classifier.classifier.perform_ocr") as mock_perform_ocr:
            # Configure mock_perform_ocr to return sample OCR results
            mock_perform_ocr.return_value = [
                [
                    [
                        [[0, 0], [100, 0], [100, 20], [0, 20]],
                        ["Sample OCR text", 0.99],
                    ],
                    [
                        [[0, 30], [100, 30], [100, 50], [0, 50]],
                        ["More sample text", 0.98],
                    ],
                ]
            ]
            
            # Call classify
            predictions, predictions_header, texts = classify(sample_pdf_bytes)

        # Verify perform_ocr was called
        mock_perform_ocr.assert_called_once_with(sample_pdf_bytes)

        # Verify model.predict was called
        assert mock_model.predict.called

        # Verify model.predict_packet was called
        assert mock_model.predict_packet.called

        # Verify results
        assert isinstance(predictions, list)
        assert isinstance(predictions_header, pd.DataFrame)
        assert isinstance(texts, list)

    def test_classify_with_empty_pdf(self, mock_ocr, mock_model):
        """Test classification of an empty PDF file."""
        # Create an empty PDF
        empty_pdf = b"%PDF-1.7\n%\xe2\xe3\xcf\xd3\n1 0 obj\n<</Type/Catalog/Pages 2 0 R>>\nendobj\n2 0 obj\n<</Type/Pages/Kids[]/Count 0>>\nendobj\nxref\n0 3\n0000000000 65535 f \n0000000015 00000 n \n0000000060 00000 n \ntrailer\n<</Size 3/Root 1 0 R>>\nstartxref\n110\n%%EOF\n"

        # Call the classify function with empty PDF
        with patch("classifier.classifier.perform_ocr") as mock_perform_ocr:
            # Configure mock_perform_ocr to return empty OCR results
            mock_perform_ocr.return_value = [[]]
            
            # Call classify
            predictions, predictions_header, texts = classify(empty_pdf)

        # Verify perform_ocr was called
        mock_perform_ocr.assert_called_once_with(empty_pdf)

        # Verify model.predict was called
        assert mock_model.predict.called

        # Verify model.predict_packet was called
        assert mock_model.predict_packet.called

        # Verify results
        assert isinstance(predictions, list)
        assert isinstance(predictions_header, pd.DataFrame)
        assert isinstance(texts, list)

    def test_classify_text_flattening(self, mock_ocr, mock_model):
        """Test text flattening in classify function."""
        # Create a sample PDF
        sample_pdf = b"%PDF-1.7\n%\xe2\xe3\xcf\xd3\n1 0 obj\n<</Type/Catalog/Pages 2 0 R>>\nendobj\n2 0 obj\n<</Type/Pages/Kids[3 0 R]/Count 1>>\nendobj\n3 0 obj\n<</Type/Page/Parent 2 0 R/Resources<</Font<</F1<</Type/Font/Subtype/Type1/BaseFont/Helvetica>>>>/ProcSet[/PDF/Text]>>/MediaBox[0 0 612 792]/Contents 4 0 R>>\nendobj\n4 0 obj\n<</Length 44>>stream\nBT\n/F1 12 Tf\n100 700 Td\n(Test PDF) Tj\nET\nendstream\nendobj\nxref\n0 5\n0000000000 65535 f \n0000000015 00000 n \n0000000060 00000 n \n0000000111 00000 n \n0000000254 00000 n \ntrailer\n<</Size 5/Root 1 0 R>>\nstartxref\n345\n%%EOF\n"

        # Call the classify function with sample PDF
        with patch("classifier.classifier.perform_ocr") as mock_perform_ocr:
            # Configure mock_perform_ocr to return complex OCR results
            mock_perform_ocr.return_value = [
                [
                    [
                        [[0, 0], [100, 0], [100, 20], [0, 20]],
                        ["First line", 0.99],
                    ],
                    [
                        [[0, 30], [100, 30], [100, 50], [0, 50]],
                        ["Second line", 0.98],
                    ],
                ],
                [
                    [
                        [[0, 0], [100, 0], [100, 20], [0, 20]],
                        ["Third line", 0.97],
                    ],
                ],
                None,  # Empty page
            ]
            
            # Call classify
            predictions, predictions_header, texts = classify(sample_pdf)

        # Verify perform_ocr was called
        mock_perform_ocr.assert_called_once_with(sample_pdf)

        # Verify model.predict was called with flattened text
        # The DataFrame should have 3 rows with text:
        # "First line Second line", "Third line", and ""
        model_predict_call = mock_model.predict.call_args[0][0]
        assert isinstance(model_predict_call, pd.DataFrame)
        assert len(model_predict_call) == 3
        assert model_predict_call.iloc[0]['Text'] == "First line Second line"
        assert model_predict_call.iloc[1]['Text'] == "Third line"
        assert model_predict_call.iloc[2]['Text'] == ""
