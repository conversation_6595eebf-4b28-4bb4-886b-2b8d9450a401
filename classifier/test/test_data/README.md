# Test Data

This directory contains sample PDF files for testing the classifier module.

## Sample Files

- `sample.pdf`: A sample PDF file with text for testing OCR and classification

## Usage

These files are used by the tests in `test_pdf_processing.py` to test the PDF processing functionality.

The actual PDF files would be created during test setup using PyMuPDF (fitz) library, or you can manually add PDF files to this directory.

Example code to create a sample PDF:

```python
import fitz

def create_sample_pdf(output_path):
    """Create a sample PDF file with text."""
    doc = fitz.open()
    page = doc.new_page()
    page.insert_text((50, 50), "Sample PDF text for testing")
    page.insert_text((50, 70), "Second line of text")
    page.insert_text((50, 90), "Third line of text")
    doc.save(output_path)
    doc.close()
    return output_path
```
