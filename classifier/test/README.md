# Classifier Tests

## 📋 Overview

This directory contains comprehensive tests for the `callback` function in `classifier/classifier.py`, focusing specifically on PDF processing capabilities including OCR text extraction, text summarization, and JSON output formatting.

## 🧪 Test Structure

The tests are organized as follows:

- `conftest.py`: Contains fixtures and setup for all tests
- `test_callback.py`: Tests for the main `callback` function
- `test_pdf_processing.py`: Tests for PDF processing functionality

## 📁 Test Data

Sample PDF files for testing are stored in the `test_data` directory. These files are used to test the OCR and classification functionality.

## 🚀 Running the Tests

### Prerequisites

Before running the tests, make sure you have the following installed:

- Python 3.10 or higher
- pytest and other test dependencies (see `requirements-test.txt`)

### Using the Makefile

The project includes a Makefile with targets for setting up the environment and running tests:

```bash
# Set up the environment (create venv and install dependencies)
make setup

# Run the tests
make test

# Run tests with coverage report
make test-coverage

# Clean up (remove venv and cache files)
make clean
```

### Running Tests Directly

If you prefer to run tests directly with pytest:

```bash
# Run all classifier tests
pytest tests/test_classifier -v

# Run specific test file
pytest tests/test_classifier/test_callback.py -v

# Run tests with coverage
pytest tests/test_classifier --cov=classifier --cov-report=term --cov-report=html
```

## 📊 Test Coverage

The tests aim to provide comprehensive coverage of the `callback` function and related PDF processing functionality, including:

- Successful message processing
- Error handling (Minio S3Error, general exceptions)
- Empty predictions handling
- PDF processing (single page, multi-page, empty PDFs)
- OCR text extraction
- Text flattening and summarization
- Classification with different models

## 🔍 Mocking Strategy

The tests use extensive mocking to isolate the code under test from external dependencies:

- Minio client for S3 storage
- RabbitMQ service for message queuing
- Database connector for data persistence
- PaddleOCR for text extraction
- Model class for classification

This ensures that tests are fast, reliable, and don't depend on external services or network connections.

## 📝 Adding New Tests

When adding new tests:

1. Add fixtures to `conftest.py` if needed
2. Create test functions in the appropriate test file
3. Use existing mocks and fixtures where possible
4. Add sample data to `test_data` directory if needed
5. Run tests to ensure they pass

## 🔄 Continuous Integration

These tests are designed to be run in a CI/CD pipeline. They are isolated from external dependencies and should run reliably in any environment.
