"""
Tests for the callback function in classifier.py.
"""
import io
import json
from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest
from minio.error import S3Error

# Import the callback function directly
# This will be patched in the tests
from classifier.classifier import callback


class TestCallback:
    """Test class for the callback function."""

    def test_callback_successful_processing(
        self,
        mock_message_tuple,
        mock_minio_client,
        mock_rmq_service,
        mock_classify,
        mock_update_document_record_in_db,
        sample_pdf_bytes,
        mock_monitor_service,
    ):
        """Test successful processing of a message."""
        # Setup minio client to return sample PDF
        response_mock = MagicMock()
        response_mock.read.return_value = sample_pdf_bytes
        mock_minio_client.get_object.return_value = response_mock

        # Call the callback function
        callback(mock_message_tuple)

        # Verify minio client was called to get the file
        ch, method, properties, body = mock_message_tuple
        message = json.loads(body.decode("utf-8"))
        mock_minio_client.get_object.assert_called_once()

        # Verify classify was called
        mock_classify.assert_called_once()

        # Verify document record was updated
        mock_update_document_record_in_db.assert_called_once()

        # Verify message was sent to split queue
        mock_rmq_service.send_message.assert_called_once()

        # Verify message was acknowledged
        mock_rmq_service.safe_ack.assert_called_once_with(ch, method.delivery_tag)

        # Verify monitor service was updated
        mock_monitor_service.update_status.assert_called_once()

    def test_callback_minio_error(
        self,
        mock_message_tuple,
        mock_minio_client,
        mock_rmq_service,
        mock_classify,
        mock_update_document_record_in_db,
        mock_monitor_service,
    ):
        """Test handling of Minio S3Error."""
        # Setup minio client to raise S3Error
        mock_minio_client.get_object.side_effect = S3Error("test-resource", "test-error")

        # Call the callback function
        callback(mock_message_tuple)

        # Verify minio client was called
        mock_minio_client.get_object.assert_called_once()

        # Verify classify was not called
        mock_classify.assert_not_called()

        # Verify document record was not updated
        mock_update_document_record_in_db.assert_not_called()

        # Verify message was sent back to classify queue
        ch, method, properties, body = mock_message_tuple
        mock_rmq_service.send_message.assert_called_once()

        # Verify message was acknowledged
        mock_rmq_service.safe_ack.assert_called_once_with(ch, method.delivery_tag)

        # Verify monitor service was updated
        mock_monitor_service.update_status.assert_called_once()

    def test_callback_general_exception(
        self,
        mock_message_tuple,
        mock_minio_client,
        mock_rmq_service,
        mock_classify,
        mock_update_document_record_in_db,
        mock_monitor_service,
    ):
        """Test handling of general exceptions."""
        # Setup minio client to raise a general exception
        mock_minio_client.get_object.side_effect = Exception("Test exception")

        # Call the callback function
        callback(mock_message_tuple)

        # Verify minio client was called
        mock_minio_client.get_object.assert_called_once()

        # Verify classify was not called
        mock_classify.assert_not_called()

        # Verify document record was not updated
        mock_update_document_record_in_db.assert_not_called()

        # Verify message was sent back to classify queue
        ch, method, properties, body = mock_message_tuple
        mock_rmq_service.send_message.assert_called_once()

        # Verify message was acknowledged
        mock_rmq_service.safe_ack.assert_called_once_with(ch, method.delivery_tag)

        # Verify monitor service was updated
        mock_monitor_service.update_status.assert_called_once()

    def test_callback_empty_predictions(
        self,
        mock_message_tuple,
        mock_minio_client,
        mock_rmq_service,
        mock_classify,
        mock_update_document_record_in_db,
        sample_pdf_bytes,
        mock_monitor_service,
    ):
        """Test handling of empty predictions."""
        # Setup minio client to return sample PDF
        response_mock = MagicMock()
        response_mock.read.return_value = sample_pdf_bytes
        mock_minio_client.get_object.return_value = response_mock

        # Setup classify to return empty predictions
        mock_classify.return_value = ([], [], [])

        # Call the callback function
        callback(mock_message_tuple)

        # Verify minio client was called
        mock_minio_client.get_object.assert_called_once()

        # Verify classify was called
        mock_classify.assert_called_once()

        # Verify document record was not updated
        mock_update_document_record_in_db.assert_not_called()

        # Verify message was not sent to split queue
        mock_rmq_service.send_message.assert_not_called()

        # Verify message was acknowledged
        ch, method, properties, body = mock_message_tuple
        mock_rmq_service.safe_ack.assert_called_once_with(ch, method.delivery_tag)

        # Verify monitor service was updated
        mock_monitor_service.update_status.assert_called_once()
