.PHONY: setup clean test test-coverage lint format help venv install-deps install-test-deps

# Variables
PYTHON := python3
VENV_NAME := venv
VENV_ACTIVATE := $(VENV_NAME)/bin/activate
PYTHON_VERSION := 3.10

# Default target
.DEFAULT_GOAL := help

# Help target
help:
	@echo "Available targets:"
	@echo "  setup            - Set up Python virtual environment and install dependencies"
	@echo "  clean            - Remove virtual environment and cache files"
	@echo "  test             - Run tests"
	@echo "  test-coverage    - Run tests with coverage report"
	@echo "  lint             - Run linting checks"
	@echo "  format           - Format code"
	@echo "  venv             - Create virtual environment"
	@echo "  install-deps     - Install dependencies"
	@echo "  install-test-deps - Install test dependencies"

# Create virtual environment
venv:
	@echo "Creating virtual environment..."
	@if command -v pyenv 1>/dev/null 2>&1; then \
		pyenv install $(PYTHON_VERSION) -s && \
		pyenv local $(PYTHON_VERSION) && \
		$(PYTHON) -m venv $(VENV_NAME); \
	else \
		$(PYTHON) -m venv $(VENV_NAME); \
	fi
	@echo "Virtual environment created."

# Install dependencies
install-deps: venv
	@echo "Installing dependencies..."
	@. $(VENV_ACTIVATE) && pip install --upgrade pip
	@. $(VENV_ACTIVATE) && pip install -r ../requirements.txt
	@echo "Dependencies installed."

# Install test dependencies
install-test-deps: venv
	@echo "Installing test dependencies..."
	@. $(VENV_ACTIVATE) && pip install --upgrade pip
	@. $(VENV_ACTIVATE) && pip install -r requirements-test.txt
	@echo "Test dependencies installed."

# Setup target
setup: venv install-deps install-test-deps
	@echo "Setup complete."

# Clean target
clean:
	@echo "Cleaning up..."
	@rm -rf $(VENV_NAME)
	@rm -rf .pytest_cache
	@rm -rf .coverage
	@rm -rf htmlcov
	@rm -rf __pycache__
	@rm -rf */__pycache__
	@rm -rf */*/__pycache__
	@rm -rf */*/*/__pycache__
	@echo "Cleanup complete."

# Test target
test: install-test-deps
	@echo "Running tests..."
	@. $(VENV_ACTIVATE) && pytest tests/test_classifier -v

# Test with coverage
test-coverage: install-test-deps
	@echo "Running tests with coverage..."
	@. $(VENV_ACTIVATE) && pytest tests/test_classifier --cov=classifier --cov-report=term --cov-report=html

# Lint target
lint: install-test-deps
	@echo "Running linting checks..."
	@. $(VENV_ACTIVATE) && ruff check .

# Format target
format: install-test-deps
	@echo "Formatting code..."
	@. $(VENV_ACTIVATE) && ruff format .
