import traceback
import yaml
import pika
import numpy as np
import pandas as pd
import pickle
import re
import nltk
import json
import os
import io
import sys
import fitz
import requests

from PIL import Image

Image.MAX_IMAGE_PIXELS = None

from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
# from pdf2image import convert_from_bytes

from minio import Minio
from minio.error import S3Error

from io import BytesIO

from sqlalchemy.orm import declarative_base
from uuid6 import uuid7

from time import sleep
from datetime import datetime, timezone

from paddleocr import PaddleOCR

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import Document, Splitted_Document
from pipeline_utils.monitoring import MonitorService, Status
from pipeline_utils.rabbitmq_connector import PikaServiceFactory, PikaService
from pipeline_utils.database_connector import DBConnector
from pipeline_utils.tenant_utils import TenantInfoExtractor, ChannelType, default_tenant_extractor, get_tenant_processing_config, default_tenant_config

ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False, use_gpu=True)

nltk.download('stopwords')
nltk.download('wordnet')

MINIO_URI = os.environ.get('MINIO_URI')
MINIO_ACCESS_KEY = os.environ.get('MINIO_ACCESS_KEY')
MINIO_SECRET_KEY = os.environ.get('MINIO_SECRET_KEY')
MINIO_FILES_BUCKET = os.environ.get('MINIO_FILES_BUCKET')
MINIO_SECURE = os.environ.get('MINIO_SECURE', 'false').lower() == 'true'

RABBITMQ_HOST = os.environ.get('RABBITMQ_HOST')
RABBITMQ_PORT = os.environ.get('RABBITMQ_PORT')
RABBITMQ_USERNAME = os.environ.get('RABBITMQ_DEFAULT_USER')
RABBITMQ_PASSWORD = os.environ.get('RABBITMQ_DEFAULT_PASS')
RABBITMQ_TO_CLASSIFY_QUEUE_NAME = os.environ.get('RABBITMQ_TO_CLASSIFY_QUEUE_NAME')
RABBITMQ_TO_SPLIT_QUEUE_NAME = os.environ.get('RABBITMQ_TO_SPLIT_QUEUE_NAME')


PGSQL_HOST = os.environ.get('DB_HOST')
PGSQL_PORT = os.environ.get('DB_PORT')
PGSQL_USERNAME = os.environ.get('DB_USER')
PGSQL_PASSWORD = os.environ.get('DB_PASSWORD')
PGSQL_DB_NAME = os.environ.get('DB_NAME')

Base = declarative_base()

db_connector = DBConnector(
    pgsql_host=PGSQL_HOST,
    pgsql_port=PGSQL_PORT,
    pgsql_username=PGSQL_USERNAME,
    pgsql_password=PGSQL_PASSWORD,
    pgsql_db_name=PGSQL_DB_NAME
)
Base.metadata.create_all(db_connector.get_engine())

GENERAL_MODEL_PATH = os.environ.get('CLASSIFIER_GENERAL_MODEL_PATH', "/models/general_model.pkl")
PACKET_MODEL_PATH = os.environ.get('CLASSIFIER_PACKET_MODEL_PATH', "/models/packet_model.pkl")
HEADER_CONTENT_MODEL_PATH = os.getenv("CLASSIFIER_HEADER_CONTENT_MODEL_PATH", "/models/header_content_model.pkl")

print(f"GENERAL_MODEL_PATH: {GENERAL_MODEL_PATH}")
print(f"PACKET_MODEL_PATH: {PACKET_MODEL_PATH}")
print(f"HEADER_CONTENT_MODEL_PATH: {HEADER_CONTENT_MODEL_PATH}")


rmq_service_factory = PikaServiceFactory(
    host=RABBITMQ_HOST,
    port=RABBITMQ_PORT,
    username=RABBITMQ_USERNAME,
    password=RABBITMQ_PASSWORD,
    ssl_options=None,
    heartbeat=3000,
)
rmq_service = rmq_service_factory.create_service()
rmq_service.start()

minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)

# API_HOST = os.environ.get('MONITOR_HOST')
# API_PORT = os.environ.get('MONITOR_PORT')
PROJECT_NAME = os.environ.get('PROJECT_NAME')
APP_NAME = os.environ.get('APP_NAME')


class Model:
    def __init__(self, model_path=None, save_path=None):
        """ Init model.

        Args:
            model_path (str): path to model file.
            save_path (str): path to save model file.
        """

        self._model_path = model_path
        self._save_path = save_path

        self._model = None

        #  10: 'Utilization Review',
        #  11: 'Peer Review',
        self._classes = {0: 'Other',
                         1: 'RFA',
                         2: 'Medical Records',
                         3: 'Misc Correspondence',
                         4: 'Legal Correspondence',
                         5: 'State forms',
                         6: 'Ratings',
                         7: 'Subpoena',
                         8: 'EOB/EOR',
                         9: 'IMR/IME/QME',
                         12: 'Supplemental/Work Status',
                         13: 'Physician Bill (HCFA)',
                         14: 'Hospital Bill (UB)',
                         15: 'Translation Bills',
                         16: 'Determination - Med Auth',
                         17: 'Vocational Evaluations',
                         18: 'Case Management Notes',
                         19: 'Fax',
                         20: 'Injury/Illness/FROI',
                         21: 'Filler',
                         22: 'CRFA',
                         23: 'header',
                         24: 'content'}
        self._key_words = {
            'Other': None,
            'RFA': 'Request for Authorization, RFA, UR, Utilization Review, ' + \
                   'Peer Review, DWC, DWC FORM, 5021',
            'Medical Records': 'Progress Notes, Medical records, Objective, Notes, Exam, Findings',
            'Misc Correspondence': None,
            'Legal Correspondence': 'Attorney, Firm, Bureau, Legal',
            'State forms': 'C4, MG1, MG2, LA1022, DWC026, W9',
            'Ratings': 'Disability Raiting, Medical raitings, ratings',
            'Subpoena': 'Subpoena',
            'EOB/EOR': 'Explanation of benefits, Explanation of Review',
            'IMR/IME/QME': 'IMR, IME,QME (Independent Medical Revw/Evaluation or Qualified Medical Evaluator/Evaluation)',
            'Utilization Review': 'UR Utilization Review',
            'Peer Review': 'Peer Review, Claims Eval, Dane Street',
            'Supplemental/Work Status': 'work status, return to work status, suplemental work status, modified duty, full duty, PR-2',
            'Physician Bill (HCFA)': None,
            'Hospital Bill (UB)': None,
            'Translation Bills': 'translation, interpretation',
            'Determination - Med Auth': 'RFA Determination, UR Determination, Peer Determination',
            'Vocational Evaluations': 'Vocational Evaluation',
            'Case Management Notes': None,
            'Fax': 'Fax',
            'Injury/Illness/FROI': 'Doctor 1st report, Doctor First Report'
        }
        self._train_info = {'name': 'MLPClassifier', 'accuracy': 0, 'train_time': 0, 'classes_trained': 4,
                            'confusion_matrix': None, 'classification_report': None,
                            'files_amount': 0, 'pages_amount': 0}
        self._vectorizer = None

    def import_model(self, model_path):
        """Import model from file.

        Args:
            model_path (str): path to model file.
        """

        # check if model file exists
        if self._model_path is None:
            raise ValueError("Model path is not specified")
        print(f"Attempting to load model from: {self._model_path}")
        # check if model file is pkl file format
        if not self._model_path.endswith(".pkl"):
            raise ValueError("Model path is not in .pkl format")

        # try to import model and vectorizer
        # try:
        with open(model_path, "rb") as f:
            data = pickle.load(f)
            self._model = data["model"]
            self._vectorizer = data["vectorizer"]
            self._classes = data["classes"]
            self._key_words = data["key_words"]
            self._train_info = data["train_info"]
        # except:
        #     raise ValueError("Import model error: wrong model path or file corrupted")

    def preprocess(self, train_data):
        """
        Preprocess data.

        Args:
            train_data (pd.DataFrame): train data.

        Returns:
            X_train, X_test, y_train, y_test (pd.DataFrame): train and test data.
        """

        stop_words = stopwords.words('english')
        lemmatizer = WordNetLemmatizer()

        # preprocess text
        text = train_data['Text'].copy()
        # fill NaN or None values with an empty string
        text = text.fillna('')
        # remove numbers
        text = text.apply(lambda x: re.sub('[^a-zA-Z]', ' ', x))
        # small letters
        text = text.apply(lambda x: x.lower())
        # remove punctuation
        punctuations = '''!()-[]{};:'"\,<>./?@#$%^&*_~'''
        text = text.apply(lambda x: ''.join([char for char in x if char not in punctuations]))
        # remove stop words
        text = text.apply(lambda x: ' '.join([word for word in x.split() if word not in stop_words]))
        # lemmatization
        text = text.apply(lambda x: ' '.join([lemmatizer.lemmatize(word) for word in x.split()]))
        # remove short and long words
        text = text.apply(
            lambda x: ' '.join([word for word in x.split() if word != ' ' and len(word) > 1 and len(word) < 20]))
        train_data['Text'] = text

        X = train_data

        # word vectorization
        X = self._vectorizer.transform(X['Text']).toarray()
        return X

    def predict(self, data, save_text=False):
        """
        Predict target values.

        Args:
            data (pd.DataFrame): data.

        Returns:
            predictions (np.array): predictions.
        """

        self.import_model(self._model_path)

        # save text from 'Text' column
        if save_text:
            saved_text = data['Text']

        # Preprocess data
        data = self.preprocess(data)

        # Make predictions
        trained_classes = list(self._model.classes_)
        convert_classes = {v: k for k, v in enumerate(trained_classes)}

        predictions_proba = self._model.predict_proba(data)

        max_proba = np.max(predictions_proba, axis=1)
        max_pred = np.argmax(predictions_proba, axis=1)

        predictions = [
            {"class": trained_classes[i], "confidence": round(max_proba[idx], 2)}
            for idx, i in enumerate(max_pred)
        ]

        predictions_df = pd.DataFrame(predictions)

        if save_text:
            predictions_df['Text'] = saved_text

        return predictions_df

    def predict_packet(self, predictions_df):
        """
        Predict target values (med records/work status).

        Args:
            predictions (dict): predictions.

        Returns:
            predictions (np.array): predictions.
        """

        self.import_model(self._model_path)

        # combine text from header and content pages
        combined_packets = []
        saved_text, saved_pages, saved_confs = None, [], []
        for index, row in predictions_df.iterrows():
            if row['class'] in [23, 24]:
                if saved_text is None:
                    saved_text = row['Text']
                    # save page by index because no Page column
                    saved_pages.append(index)
                    saved_confs.append(row['confidence'])
                else:
                    potential_new_header = True
                    if row['class'] == 24:
                        # if there are content pages after header or between them pages with Class 'Other'
                        if index == saved_pages[-1] + 1 or index > saved_pages[-1] + 1 and all(
                                predictions_df.loc[saved_pages[-1] + 1:index, 'class'] == 0):
                            saved_text += ' ' + row['Text']
                            saved_pages.append(index)
                            saved_confs.append(row['confidence'])
                            potential_new_header = False
                    if potential_new_header:
                        # saved data as dict in combined_packets list
                        combined_packets.append({'Text': saved_text, 'Pages': saved_pages, 'Confidences': saved_confs})

                        # Reset saved_text, saved_pages, and saved_confs
                        saved_text = row['Text']
                        saved_pages = [index]
                        saved_confs = [row['confidence']]
        # Add the last packet
        if saved_text is not None:
            combined_packets.append({'Text': saved_text, 'Pages': saved_pages, 'Confidences': saved_confs})

        # Predict each packet and replace 23 and 24 with the prediction result
        for packet in combined_packets:
            packet_text = packet['Text']
            packet_text_df = pd.DataFrame([packet_text], columns=['Text'])
            packet_pages = packet['Pages']

            # Predict the packet
            data = self.preprocess(packet_text_df)
            packet_prediction = self._model.predict_proba(data)

            trained_classes = list(self._model.classes_)

            max_proba = np.max(packet_prediction, axis=1)
            max_pred = np.argmax(packet_prediction, axis=1)

            # Replace 23 and 24 with the prediction result
            for page in packet_pages:
                predictions_df.loc[page, 'class'] = trained_classes[max_pred[0]]
                # Update confidence
                current_confidence = predictions_df.loc[page, 'confidence']
                new_confidence = max_proba[0]
                average_confidence = (current_confidence + new_confidence) / 2
                predictions_df.loc[page, 'confidence'] = average_confidence

        # Create a list of predictions in the desired format
        predictions = [
            {"class": row['class'], "confidence": round(row['confidence'], 2)}
            for _, row in predictions_df.iterrows()
        ]

        return predictions

    """
    END OF Model CLASS
    """


model1 = Model(model_path=GENERAL_MODEL_PATH)
model2 = Model(model_path=PACKET_MODEL_PATH)

model_header = Model(model_path=HEADER_CONTENT_MODEL_PATH)

def perform_ocr(file):
    """ Split pdf file into pages (images)
    
    Args:
        file (bytes): pdf file stream.

    Returns:
        text (str): recognized text.
    """
    mat = fitz.Matrix(300 / 72, 300 / 72)
    doc = fitz.open(stream=file, filetype="pdf")

    texts = []
    for page in doc:
        pix = page.get_pixmap(matrix=mat)
        img_bytes = pix.tobytes("ppm")
        image = Image.open(io.BytesIO(img_bytes))
        numpydata = np.asarray(image)
        result = ocr.ocr(numpydata, cls=True)
        texts.append(result[0])
    return texts


def apply_tenant_classification_rules(predictions, predictions_header, tenant_id=None, subtenant_id=None):
    """
    Apply tenant-specific classification rules and filtering.
    
    Args:
        predictions: Classification predictions
        predictions_header: Header predictions  
        tenant_id: Tenant ID
        subtenant_id: Subtenant ID
    
    Returns:
        Filtered and adjusted predictions
    """
    if not tenant_id:
        return predictions, predictions_header
    
    try:
        # Get tenant-specific processing configuration
        processing_config = get_tenant_processing_config(tenant_id, subtenant_id)
        tenant_config = default_tenant_config.get_tenant_config(tenant_id, subtenant_id)
        
        # Apply confidence threshold adjustments
        min_confidence = processing_config.get('minimum_classification_confidence', 0.7)
        
        # Filter predictions based on tenant's enabled/disabled document types
        enabled_types = tenant_config.get('document_types', {}).get('enabled_types', [])
        disabled_types = tenant_config.get('document_types', {}).get('disabled_types', [])
        
        filtered_predictions = []
        for pred in predictions:
            doc_class = pred['class']
            confidence = pred['confidence']
            
            # Apply confidence threshold
            if confidence < min_confidence:
                # Fallback to 'Other' if confidence too low
                filtered_predictions.append({"class": "Other", "confidence": confidence})
                continue
            
            # Apply document type filtering
            if enabled_types and doc_class not in enabled_types:
                # Document type not enabled for this tenant
                filtered_predictions.append({"class": "Other", "confidence": confidence})
                continue
                
            if disabled_types and doc_class in disabled_types:
                # Document type disabled for this tenant
                filtered_predictions.append({"class": "Other", "confidence": confidence})
                continue
            
            # Keep original prediction
            filtered_predictions.append(pred)
        
        print(f"Applied tenant-specific classification rules for tenant {tenant_id}: "
              f"min_confidence={min_confidence}, enabled_types={enabled_types}, disabled_types={disabled_types}")
        
        return filtered_predictions, predictions_header
        
    except Exception as e:
        print(f"Error applying tenant classification rules: {e}")
        return predictions, predictions_header


def classify(in_memory_file, tenant_id=None, subtenant_id=None):
    """
    Splits in memory document to pages and classifies each

    arguments:
    in_memory_file: file object
    tenant_id: Tenant ID for tenant-specific processing
    subtenant_id: Subtenant ID for tenant-specific processing

    returns:
    pages (list)
    predictions (np.array)
    """

    texts = perform_ocr(in_memory_file)

    flattened_texts = []
    for text in texts:
        if text:
            text = ' '.join([line_info[-1][0] for line_info in text])
            flattened_texts.append(text)
        else:
            flattened_texts.append('')
    data = pd.DataFrame({'Text': flattened_texts})

    temp_predictions = model1.predict(data, save_text=True)
    predictions = model2.predict_packet(temp_predictions)

    predictions_header = model_header.predict(data)
    
    # Apply tenant-specific classification rules
    predictions, predictions_header = apply_tenant_classification_rules(
        predictions, predictions_header, tenant_id, subtenant_id
    )

    return predictions, predictions_header, texts


def update_document_record_in_db(file_id, predictions, texts, predictions_header):
    if isinstance(predictions_header, list):
        predictions_header_human_readable = [
            {"class": model_header._classes[int(cls["class"])], "confidence": cls["confidence"]}
            for cls in predictions_header
        ]
    elif isinstance(predictions_header, pd.DataFrame):
        predictions_header_human_readable = [
            {"class": model_header._classes[int(row['class'])], "confidence": row['confidence']}
            for _, row in predictions_header.iterrows()
        ]
    else:
        raise TypeError(f"Unsupported predictions_header type: {type(predictions_header)}")

    # update doc status in db, add ocr and predictions
    predictions_human_readable = [{"class": model1._classes[cls["class"]], "confidence": cls["confidence"]} for cls in
                                  predictions]
    # print(f'predictions: {predictions}')
    print(f'predictions_human_readable: {predictions_human_readable}')
    # print(f"predictions_header: {predictions_header}")
    print(f'predictions_header_human_readable: {predictions_header_human_readable}')

    with db_connector.session_scope() as session:
        document = session.query(Document).filter_by(uuid=file_id).first()
        document.initial_predictions_ml = predictions_human_readable
        document.initial_predictions_header_ml = predictions_header_human_readable
        document.status = 'to_split'  # Update status to 'to_split'
        document.text_ocr = texts
        document.predictions_ml_time = datetime.now(timezone.utc)
        document.metadata_ml = None


def callback(msg_tuple):
    ch, method, properties, body = msg_tuple

    # monitor.update_status(Status.UP)
    print(f" [x] Received {body}")
    queue_item = json.loads(body.decode('UTF-8'))

    file_id = queue_item['file_id']
    filename = queue_item['filename']
    # Extract tenant information from the message
    tenant_id = queue_item.get('tenant_id')
    subtenant_id = queue_item.get('subtenant_id')
    
    print(f"Processing document {filename} for tenant: {tenant_id}, subtenant: {subtenant_id}")

    try:
        response = minio_client.get_object(MINIO_FILES_BUCKET, file_id)
        in_memory_file = BytesIO(response.read())

        predictions, predictions_header, texts = classify(in_memory_file.read(), tenant_id, subtenant_id)

        if predictions:
            update_document_record_in_db(file_id, predictions, texts, predictions_header)
            
            # Pass tenant information to the next component (splitter)
            next_queue_item = {
                'file_id': file_id,
                'filename': filename,
                'tenant_id': tenant_id,
                'subtenant_id': subtenant_id
            }
            rmq_service.send_message(RABBITMQ_TO_SPLIT_QUEUE_NAME, json.dumps(next_queue_item))
            rmq_service.safe_ack(ch, method.delivery_tag)

    except S3Error as err:
        # Include tenant info when re-queuing
        queue_item_with_tenant = {
            'file_id': file_id,
            'filename': filename,
            'tenant_id': tenant_id,
            'subtenant_id': subtenant_id
        }
        rmq_service.send_message(RABBITMQ_TO_CLASSIFY_QUEUE_NAME, json.dumps(queue_item_with_tenant))
        rmq_service.safe_ack(ch, method.delivery_tag)
        print(err)

    except Exception as e:
        print(f"Error processing message: {e}")
        traceback.print_exc()

        # Include tenant info when re-queuing
        queue_item_with_tenant = {
            'file_id': file_id,
            'filename': filename,
            'tenant_id': tenant_id,
            'subtenant_id': subtenant_id
        }
        rmq_service.send_message(RABBITMQ_TO_CLASSIFY_QUEUE_NAME, json.dumps(queue_item_with_tenant))
        rmq_service.safe_ack(ch, method.delivery_tag)


rmq_service.read_messages(RABBITMQ_TO_CLASSIFY_QUEUE_NAME, callback)
# monitor = MonitorService(PROJECT_NAME, APP_NAME, 'classifier', API_HOST, API_PORT)
# monitor.start_monitoring()
# monitor.update_status(Status.UP)

rmq_service.run()
