import os
import sys
import time
from datetime import timedelta, datetime, timezone
from io import Bytes<PERSON>
from pprint import pprint

import fitz
from uuid6 import uuid7
import yaml
from io import BytesIO
from minio import Minio
from models import Document, DocumentChunk
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.orm.attributes import flag_modified

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# configuration = yaml.safe_load(open("config.yml"))

HEADER_CONF_THRESHOLD = os.environ.get("HEADER_CONF_THRESHOLD")

CHUNK_SIZE_RANGE = os.environ.get("CHUNK_SIZE_RANGE")
CHUNK_SIZE = os.environ.get("CHUNK_SIZE")

MINIO_URI = os.environ.get('MINIO_URI')
MINIO_ACCESS_KEY = os.environ.get('MINIO_ACCESS_KEY')
MINIO_SECRET_KEY = os.environ.get('MINIO_SECRET_KEY')
MINIO_FILES_BUCKET = os.environ.get('MINIO_FILES_BUCKET')
MINIO_SECURE = os.environ.get('MINIO_SECURE').lower() == 'true'

# PGSQL_HOST = configuration["pgsql"]["PGSQL_HOST"]
# PGSQL_PORT = configuration["pgsql"]["PGSQL_PORT"]
# PGSQL_USERNAME = configuration["pgsql"]["PGSQL_USERNAME"]
# PGSQL_PASSWORD = configuration["pgsql"]["PGSQL_PASSWORD"]
# PGSQL_DB_NAME = configuration["pgsql"]["PGSQL_DB_NAME"]

minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)

# Base = declarative_base()
#
# engine = create_engine(
#     f"postgresql://{PGSQL_USERNAME}:{PGSQL_PASSWORD}@{PGSQL_HOST}:{PGSQL_PORT}/{PGSQL_DB_NAME}"
# )
# Base.metadata.create_all(engine)
# Session = sessionmaker(bind=engine)
# session = None

def get_header_pages(predictions_header_human_readable):
    """
    Returns a list of tuples containing page index and confidence
    for pages classified as 'header'.

    Args:
        predictions_header_human_readable (list): List of dictionaries with 'class' and 'confidence'.

    Returns:
        list: List of tuples with (page, confidence).
    """
    return [(index + 1, item['confidence'])
            for index, item in enumerate(predictions_header_human_readable) if item['class'] == 'header']


def get_header_pages_high_conf(predictions_header_human_readable, header_conf_threshold):
    """
    Returns a list of page indices where the class is 'header' and confidence is above the threshold.

    Args:
        predictions_header_human_readable (list): List of dictionaries with 'class' and 'confidence'.
        header_conf_threshold (float): Confidence threshold for considering a page as 'header'.

    Returns:
        list: Indices (1-based) of pages classified as 'header' with confidence above the threshold.
    """
    return [
        index + 1
        for index, item in enumerate(predictions_header_human_readable)
        if item['class'] == 'header' and item['confidence'] >= header_conf_threshold
    ]


def split_chunks(predictions, split_indices):
    # Create chunks based on page_ranges split indices
    chunks = []
    current_chunk = []

    for prediction in predictions:
        # Flatten all pages in the current page_ranges
        all_pages = [page for range_ in prediction['page_ranges'] for page in range(range_[0], range_[1] + 1)]

        # Add current prediction to the chunk
        current_chunk.append(prediction)

        # Check if any page in the current prediction should split the chunk
        if any(page in split_indices for page in all_pages):
            chunks.append(current_chunk)
            current_chunk = []

    # Add the remaining chunk if not empty
    if current_chunk:
        chunks.append(current_chunk)

    return chunks


def split_predictions_with_headers(initial_predictions_header_ml, header_pages,
                                   header_pages_conf, chunk_size,
                                   chunk_size_range):
    """
    Splits predictions_human_readable into chunks of size chunk_size,
    ensuring that splits occur as close as possible to header pages with the highest confidence.

    Args:
        header_pages (list): List of tuples (page, confidence) identified as headers.
        header_pages_conf (list): List of page indices with confidence for headers.
        chunk_size (int): Desired size of each chunk.
        chunk_size_range (int): Range around chunk_size to search for header pages.

    Returns:
        list: List of chunks (each chunk is a list of dictionaries).
    """
    # Convert header_pages to a dictionary for quick lookups
    header_pages_dict = dict(header_pages)
    total_pages = len(initial_predictions_header_ml)
    split_indices = []

    # Identify split indices close to header pages
    for start in range(chunk_size, total_pages, chunk_size):
        # Search within the range [start - chunk_size_range, start + chunk_size_range]
        candidate_range = range(max(0, start - chunk_size_range), min(total_pages, start + chunk_size_range + 1))

        # Find the closest header page in the range using header_pages_conf
        closest_header_page = None
        for page in candidate_range:
            if page in header_pages_conf:
                closest_header_page = page
                break

        # If no header page is found within the range, fallback to header_pages
        if closest_header_page is None:
            # Find header page with the highest confidence in the range
            headers_in_range = {
                page: header_pages_dict[page]
                for page in candidate_range if page in header_pages_dict
            }
            if headers_in_range:
                # Select the page with the maximum confidence
                closest_header_page = max(headers_in_range, key=headers_in_range.get)
            else:
                # Fallback to default chunk_size-based split
                closest_header_page = start

        # Add the index to split_indices
        split_indices.append(closest_header_page - 1)  # Convert to 0-based index

    # Remove duplicates and ensure indices are sorted
    split_indices = sorted(set(split_indices))
    return split_indices


def get_chunked_document_page_count(boundary):
    """
    Calculates the number of pages a chunked document would have based on the given boundary.

    Returns:
        tuple: A tuple containing the total number of pages, the first page, and the last page.
    """
    total_pages = 0
    first_page = None
    last_page = None

    for chunk in boundary:  # Iterate over each dictionary in the list
        if 'page_ranges' in chunk:  # Ensure 'page_ranges' key exists
            for page_range in chunk['page_ranges']:
                start_page, end_page = page_range

                # Update total pages
                total_pages += (end_page - start_page + 1)

                # Update first_page if it's not set or if the current start_page is smaller
                if first_page is None or start_page < first_page:
                    first_page = start_page

                # Update last_page if it's not set or if the current end_page is larger
                if last_page is None or end_page > last_page:
                    last_page = end_page

    return total_pages, first_page, last_page


def extract_text_ocr_for_chunk(text_ocr, chunk):
    """
    Extracts the OCR text for all page ranges in a chunk.

    Args:
        text_ocr (list): The full OCR text of the parent document.
        chunk (list): A list of boundary dictionaries, each containing page_ranges.

    Returns:
        list: A list of OCR text for the pages in the chunk.
    """
    extracted_text = []
    for boundary in chunk:
        for page_range in boundary['page_ranges']:
            extracted_text.extend(text_ocr[page_range[0] - 1:page_range[1]])
    return extracted_text


def filter_dicts_by_pages_ref_range(data, start_page, end_page):
    """
    Filters a list of dictionaries to only include items where 'pagesRef' overlaps
    with a given page range.

    Args:
        data (list): List of dictionaries to filter.
        start_page (int): Start of the page range (inclusive).
        end_page (int): End of the page range (inclusive).

    Returns:
        list: Filtered list of dictionaries.
    """
    filtered_data = []

    for entry in data:
        if 'pagesRef' in entry:
            for page_range in entry['pagesRef']:
                # Check if any page in the range overlaps with the given range
                if page_range[0] <= end_page and page_range[1] >= start_page:
                    filtered_data.append(entry)
                    break

    return filtered_data


def save_chunked_document(session, pdf_stream, text_ocr, boundary, parent_document):
    """
    Saves a chunked document to storage and logs metadata.
    """
    file_id = uuid7()
    file_id_str = f'{file_id}'
    pages_count, start_page, end_page = get_chunked_document_page_count(boundary)
    data = filter_dicts_by_pages_ref_range(parent_document.metadata_ml, start_page, end_page)
    file_size = pdf_stream.getbuffer().nbytes
    print("file_id:", file_id, "\n", "text_ocr:", text_ocr, "\n", "boundary:", boundary, "\n", "parent_document:",
          parent_document, "\n", "pages_count:", pages_count, "\n", "file_size:", file_size, "\n", "data:", data)

    # Example placeholder for saving to DB or storage

    add_chunked_document_record_to_db(session, file_id, text_ocr, boundary, parent_document, pages_count, file_size, start_page,
                                      end_page, data)
    minio_client.put_object(MINIO_FILES_BUCKET, file_id_str, pdf_stream, file_size)
    return file_id


def split_document_chunks(session, document_boundaries, in_memory_file, parent_document):
    """
    Splits a parent PDF document into multiple subdocuments based on the provided boundaries.

    Args:
        document_boundaries (list): A list of lists, where each sublist contains dictionaries defining boundaries for subdocuments.
        in_memory_file (BytesIO): A BytesIO object containing the in-memory representation of the parent PDF document.
        parent_document (Document): The parent document object which contains metadata and text OCR information.

    Steps:
        1. Extract the OCR text from the parent document.
        2. Open the in-memory PDF file using PyMuPDF (fitz).
        3. Iterate over the chunks (lists of boundaries) and process each chunk as a single subdocument.
        4. Create a new PDF document for each chunk and save it.
    """
    text_ocr = parent_document.text_ocr
    doc = fitz.open("pdf", in_memory_file)
    chunk_ids = []

    for chunk in document_boundaries:
        # Create a new PDF for the current chunk
        subdocument = fitz.open()

        # Add the pages from the original document to the new document based on chunk boundaries
        for boundary in chunk:
            for page_range in boundary['page_ranges']:
                for page_num in range(page_range[0] - 1, page_range[1]):
                    subdocument.insert_pdf(doc, from_page=page_num, to_page=page_num)

        # Save the subdocument to an in-memory stream
        pdf_stream = BytesIO()
        subdocument.save(pdf_stream)
        subdocument.close()
        pdf_stream.seek(0)
        # Save the chunked document
        chunk_id = save_chunked_document(
            session,
            pdf_stream,
            extract_text_ocr_for_chunk(text_ocr, chunk),
            chunk,
            parent_document
        )
        chunk_ids.append(chunk_id)
        print(chunk_ids)
    return chunk_ids


def create_doc_chunk(parent_document):
    header_pages_conf = get_header_pages_high_conf(parent_document.initial_predictions_header_ml,
                                                   HEADER_CONF_THRESHOLD)
    header_pages = get_header_pages(parent_document.initial_predictions_header_ml)
    split_indices = split_predictions_with_headers(
        parent_document.initial_predictions_header_ml, header_pages,
        header_pages_conf,
        CHUNK_SIZE, CHUNK_SIZE_RANGE)
    print("split_indices", split_indices)

    chunks = split_chunks(parent_document.predictions_ml, split_indices)
    print("chunks", chunks)

    return chunks


def aggregate_page_ranges(boundary):
    """
    Aggregates all page_ranges from the boundary list.

    Args:
        boundary (list): A list of dictionaries, each containing the key 'page_ranges'.

    Returns:
        list: A combined list of all page_ranges.
    """
    all_page_ranges = []
    for b in boundary:
        if isinstance(b, dict) and 'page_ranges' in b:
            all_page_ranges.extend(b['page_ranges'])
    return all_page_ranges


def add_chunked_document_record_to_db(session, file_id, text_ocr, boundary, parent_document, pages_count, file_size, start_page,
                                      end_page, metadata_ml):
    document_chunk = DocumentChunk()
    document_chunk.uuid = file_id
    document_chunk.incoming_package = parent_document.incoming_package
    document_chunk.file_size = file_size
    document_chunk.parent_document = parent_document
    document_chunk.parent_document_uuid = parent_document.uuid
    document_chunk.predictions_ml = boundary
    document_chunk.pages_count = pages_count
    document_chunk.parent_document_start_page = start_page
    document_chunk.parent_document_end_page = end_page
    document_chunk.status = "to_qa"
    document_chunk.text_ocr = text_ocr
    document_chunk.metadata_ml = metadata_ml
    document_chunk.date = datetime.now(timezone.utc)
    session.add(document_chunk)
    session.commit()


def large_file_handling(session, parent_document):
    doc_chunks = create_doc_chunk(parent_document)
    response = minio_client.get_object(MINIO_FILES_BUCKET, str(parent_document.uuid))
    in_memory_file = BytesIO(response.read())

    chunks_ids = split_document_chunks(session, doc_chunks, in_memory_file, parent_document)
    return chunks_ids
