FROM 112623991000.dkr.ecr.us-east-2.amazonaws.com/python:3.10.15

RUN apt-get update && apt-get install -y \
    poppler-utils \
    gettext \
    librsync-dev

WORKDIR /app

COPY validate_and_route/requirements.txt /app/requirements.txt

RUN python3 -m pip install -r requirements.txt

COPY validate_and_route/ /app/
COPY models /app/models
COPY pipeline_utils /app/pipeline_utils
COPY validate_and_route/validate_and_route.py /app/validate_and_route.py

# Start the application
CMD ["python3", "-u", "validate_and_route.py"]