# Quick Test Running Guide

## 🚀 Run Tests in Docker (Recommended)

Start Docker Desktop, then:

```bash
# Start test environment and run all tests
docker-compose -f docker-compose.test.yml up --build

# Run specific test types
docker-compose -f docker-compose.test.yml run test-runner pytest tests/unit/ -v
docker-compose -f docker-compose.test.yml run test-runner pytest tests/integration/ -v

# Clean up
docker-compose -f docker-compose.test.yml down -v
```

## 🔧 Run Tests Locally (If dependencies available)

```bash
# Install dependencies
pip install pytest pytest-cov uuid6 sqlalchemy

# Run tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=pipeline_utils --cov-report=term
```

## 🏃‍♂️ Quick Validation (No dependencies)

```bash
# Simple test runner (works without pytest)
python simple_test_runner.py
```

## 📊 Test Categories

```bash
# Unit tests only
pytest tests/ -m unit

# Integration tests only  
pytest tests/ -m integration

# Tenant-specific tests
pytest tests/ -m tenant_aware

# Backward compatibility tests
pytest tests/ -m compatibility
```

## 🐛 Debug Single Test

```bash
pytest tests/unit/test_tenant_utils.py::TestTenantInfoExtractor::test_initialization -v -s
```

## ✅ Expected Results

- **Unit Tests**: ~30+ tests covering tenant utilities
- **Integration Tests**: ~10+ tests covering end-to-end workflows
- **Coverage**: >90% for tenant-related code
- **All Tests Pass**: Multi-tenant functionality validated

Happy testing! 🧪 