classifier/model.pkl
__pycache__
alembic*
containers/minio/
containers/proxy/
containers/rabbitmq/
containers/sftp/
containers/dbpg/
containers/data/
classifier/data/
rfa_worker/data/
metadata_extractor/data/
config.yml
docker-compose.yml
*.tar
*model.pkl
*run.sh*
*.pt
*service_uuid.json
*.env
*create_image*
*.conf
.venv
*versions
*compose.yaml
*gcp-key.json

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.DS_Store
