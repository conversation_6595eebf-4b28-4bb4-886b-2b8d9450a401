services:
  classifier:
    restart: always
    image: dx-classifier-app
    network_mode: "host"
    volumes:
      - ./classifier/:/app/
      - ./classifier/model.pkl:/app/model.pkl
      - $config_path:/app/config.yml 
      - ./models/:/models/ 
      - ./pipeline_utils/:/app/pipeline_utils/
    build:
      context: ./classifier
    deploy:
        resources:
          reservations:
            devices:
              - driver: nvidia
                count: 1
                capabilities: [gpu]

  metadata-extractor:
    restart: always
    image: dx-metadata-extractor
    # container_name: metadata_extractor
    network_mode: "host"
    environment:
      BASE_AUTH_USER_AND_PASSWORD: $BASE_AUTH_USER_AND_PASSWORD
      SERVER_HOST: $SERVER_HOST
      SERVER_PORT: $SERVER_PORT
    volumes:
      - ./models/:/models/ 
      - ./metadata_extractor/:/app/
      - $config_path:/app/config.yml 
      - ./pipeline_utils/:/app/pipeline_utils/
    build:
      context: ./metadata_extractor
    deploy:
        replicas: 3
        resources:
          reservations:
            devices:
              - driver: nvidia
                count: 1
                capabilities: [gpu]

  downloader:
    restart: always
    image: dx-downloader-app
    network_mode: "host"
    build:
      context: ./downloader
    environment:
      GOOGLE_APPLICATION_CREDENTIALS: /app/gcp-key.json
    volumes:
      - /etc/nginx/ssl:/etc/nginx/ssl:ro
      - $config_path:/app/config.yml 
      - ./models/:/models/ 
      - ./pipeline_utils/:/app/pipeline_utils/
      - ./gcp-key.json:/app/gcp-key.json
  uploader:
    restart: always
    image: dx-uploader-app
    network_mode: "host"
    build:
      context: ./uploader
    volumes:
      - /etc/nginx/ssl:/etc/nginx/ssl:ro
      - ./pipeline_utils/:/app/pipeline_utils/
      - $config_path:/app/config.yml 
      - ./models/:/models/ 
  splitter:
    restart: always
    image: dx-splitter-app
    network_mode: "host"
    build:
      context: ./splitter
    volumes:
      - $config_path:/app/config.yml 
      - ./models/:/models/ 
      - ./pipeline_utils/:/app/pipeline_utils/
  validate-route:
    restart: always
    image: dx-validate-route-app
    network_mode: "host"
    build:
      context: ./validate_and_route
    volumes:
      - /etc/nginx/ssl:/etc/nginx/ssl:ro
      - $config_path:/app/config.yml 
      - ./models/:/app/models/
      - ./pipeline_utils/:/app/pipeline_utils/
  metadata-post-processor:
    restart: always
    image: dx-metadata-post-process-app
    network_mode: "host"
    build:
      context: ./metadata_postprocessor
    volumes:
      - ./pipeline_utils/:/app/pipeline_utils/
      - $config_path:/app/config.yml 
      - ./models/:/models/ 
    
  qa-post-processor:
    restart: always
    volumes:
      - /etc/nginx/ssl:/etc/nginx/ssl:ro
      - ./pipeline_utils/:/app/pipeline_utils/
      - ./config.yml:/app/config.yml 
      - ./models/:/models/ 
    network_mode: "host"
    build:
      context: ./qa_post_processor
    image: dx-qa-post-process-app

  data-cleaner:
    restart: always
    volumes:
      - ./pipeline_utils/:/app/pipeline_utils/
      - ./config.yml:/app/config.yml
      - ./models/:/models/
      - ./gcp-key.json:/app/gcp-key.json
    network_mode: "host"
    build:
      context: ./data_cleaner
    image: dx-data-cleaner-app
  select-and-populate:
    restart: always
    image: dx-select-and-populate-app
    network_mode: "host"
    volumes:
      - /etc/nginx/ssl:/etc/nginx/ssl:ro
      - ./select_and_populate/:/app/
      - $config_path:/app/config.yml
      - ./pipeline_utils/:/app/pipeline_utils/
    build:
      context: ./select_and_populate
    deploy:
        resources:
          reservations:
            devices:
              - driver: nvidia
                count: 1
                capabilities: [gpu]
