import pika
import yaml
import json
import os
import sys
import traceback

from minio import Minio
from minio.error import S3Error

from sqlalchemy import create_engine, not_
from sqlalchemy.orm import declarative_base,sessionmaker

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import Document


configuration = yaml.safe_load(open("../config.yml"))

MINIO_URI = configuration['minio']['MINIO_URI']
MINIO_ACCESS_KEY = configuration['minio']['MINIO_ACCESS_KEY']
MINIO_SECRET_KEY = configuration['minio']['MINIO_SECRET_KEY']
MINIO_FILES_BUCKET = configuration['minio']['MINIO_FILES_BUCKET']
MINIO_OBJECT_URI_PREFIX = configuration['minio']['MINIO_OBJECT_URI_PREFIX']

RABBITMQ_HOST = configuration['rabbitmq']['RABBITMQ_HOST']
RABBITMQ_PORT = configuration['rabbitmq']['RABBITMQ_PORT']
RABBITMQ_USERNAME = configuration['rabbitmq']['RABBITMQ_USERNAME']
RABBITMQ_PASSWORD = configuration['rabbitmq']['RABBITMQ_PASSWORD']
RABBITMQ_TO_CLASSIFY_QUEUE_NAME = configuration['rabbitmq']['RABBITMQ_TO_CLASSIFY_QUEUE_NAME']

PGSQL_HOST = configuration['pgsql']['PGSQL_HOST']
PGSQL_PORT = configuration['pgsql']['PGSQL_PORT']
PGSQL_USERNAME = configuration['pgsql']['PGSQL_USERNAME']
PGSQL_PASSWORD = configuration['pgsql']['PGSQL_PASSWORD']
PGSQL_DB_NAME = configuration['pgsql']['PGSQL_DB_NAME']


engine = create_engine(f'postgresql://{PGSQL_USERNAME}:{PGSQL_PASSWORD}@{PGSQL_HOST}:{PGSQL_PORT}/{PGSQL_DB_NAME}')
Session = sessionmaker(bind=engine)


minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=True
)

def enqueue_documents(number_of_documents):
    session = Session()
    try:
        # Select documents with status 'to_classify'
        documents = session.query(Document).filter_by(status='to_classify').limit(number_of_documents).all()

        # Setup RabbitMQ connection
        credentials = pika.PlainCredentials(RABBITMQ_USERNAME, RABBITMQ_PASSWORD)
        connection = pika.BlockingConnection(pika.ConnectionParameters(host=RABBITMQ_HOST, port=RABBITMQ_PORT, credentials=credentials, heartbeat=500, blocked_connection_timeout=300))
        channel = connection.channel()
        channel.queue_declare(queue=RABBITMQ_TO_CLASSIFY_QUEUE_NAME, durable=True)
        channel.basic_qos(prefetch_count=1)

        for document in documents:
            queue_item = {'file_id': str(document.uuid), 'filename': document.file}
            channel.basic_publish(
                exchange='',
                routing_key=RABBITMQ_TO_CLASSIFY_QUEUE_NAME,
                body=json.dumps(queue_item),
                properties=pika.BasicProperties(delivery_mode=pika.DeliveryMode.Persistent)
            )
            # Update document status
            document.status = 'to_classify_in_queue'
        
        # Commit status updates
        session.commit()
        print(f"Enqueued and updated {len(documents)} documents.")

    except Exception as e:
        session.rollback()
        print(f"Error: {e}")
    finally:
        session.close()
        connection.close()


if __name__ == "__main__":
    if len(sys.argv) != 2 or not sys.argv[1].isdigit():
        print("Usage: python enqueue_documents.py <number_of_documents>")
        sys.exit(1)

    number_of_documents = int(sys.argv[1])
    enqueue_documents(number_of_documents)
