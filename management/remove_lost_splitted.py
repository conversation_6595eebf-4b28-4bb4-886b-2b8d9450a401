import pika
import yaml
import json
import os
import sys
import traceback

from minio import Minio
from minio.error import S3Error

from sqlalchemy import create_engine, not_
from sqlalchemy.orm import declarative_base,sessionmaker

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import Document, Splitted_Document


configuration = yaml.safe_load(open("../config.yml"))

MINIO_URI = configuration['minio']['MINIO_URI']
MINIO_ACCESS_KEY = configuration['minio']['MINIO_ACCESS_KEY']
MINIO_SECRET_KEY = configuration['minio']['MINIO_SECRET_KEY']
MINIO_FILES_BUCKET = configuration['minio']['MINIO_FILES_BUCKET']
MINIO_OBJECT_URI_PREFIX = configuration['minio']['MINIO_OBJECT_URI_PREFIX']

PGSQL_HOST = configuration['pgsql']['PGSQL_HOST']
PGSQL_PORT = configuration['pgsql']['PGSQL_PORT']
PGSQL_USERNAME = configuration['pgsql']['PGSQL_USERNAME']
PGSQL_PASSWORD = configuration['pgsql']['PGSQL_PASSWORD']
PGSQL_DB_NAME = configuration['pgsql']['PGSQL_DB_NAME']


engine = create_engine(f'postgresql://{PGSQL_USERNAME}:{PGSQL_PASSWORD}@{PGSQL_HOST}:{PGSQL_PORT}/{PGSQL_DB_NAME}')
Session = sessionmaker(bind=engine)

minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=True
)


# Start a session
session = Session()

# Step 1: Identify Splitted_Documents related to Documents with status 'to_classify'
splitted_docs_to_delete = session.query(Splitted_Document)\
    .join(Document, Splitted_Document.parent_document_uuid == Document.uuid)\
    .filter(Document.status == 'to_classify').all()

# Step 2: Delete corresponding files from MinIO
for splitted_doc in splitted_docs_to_delete:
    object_name = str(splitted_doc.uuid)
    try:
        minio_client.remove_object("your-bucket-name", object_name)
        print(f"Successfully deleted {object_name} from MinIO.")
    except Exception as e:
        print(f"Failed to delete {object_name} from MinIO: {e}")

# Step 3: Delete the Splitted_Document records from the database
splitted_doc_uuids_to_delete = [splitted_doc.uuid for splitted_doc in splitted_docs_to_delete]
session.query(Splitted_Document)\
    .filter(Splitted_Document.uuid.in_(splitted_doc_uuids_to_delete))\
    .delete(synchronize_session='fetch')
session.commit()

session.close()
