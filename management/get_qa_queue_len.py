import pika
import yaml
import json
import os
import sys
import traceback

from minio import Minio
from minio.error import S3Error

from sqlalchemy import create_engine, not_
from sqlalchemy.orm import declarative_base,sessionmaker

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import Document


configuration = yaml.safe_load(open("../config.yml"))

MINIO_URI = configuration['minio']['MINIO_URI']
MINIO_ACCESS_KEY = configuration['minio']['MINIO_ACCESS_KEY']
MINIO_SECRET_KEY = configuration['minio']['MINIO_SECRET_KEY']
MINIO_FILES_BUCKET = configuration['minio']['MINIO_FILES_BUCKET']
MINIO_OBJECT_URI_PREFIX = configuration['minio']['MINIO_OBJECT_URI_PREFIX']

RABBITMQ_HOST = configuration['rabbitmq']['RABBITMQ_HOST']
RABBITMQ_PORT = configuration['rabbitmq']['RABBITMQ_PORT']
RABBITMQ_USERNAME = configuration['rabbitmq']['RABBITMQ_USERNAME']
RABBITMQ_PASSWORD = configuration['rabbitmq']['RABBITMQ_PASSWORD']
RABBITMQ_TO_CLASSIFY_QUEUE_NAME = configuration['rabbitmq']['RABBITMQ_TO_CLASSIFY_QUEUE_NAME']

PGSQL_HOST = configuration['pgsql']['PGSQL_HOST']
PGSQL_PORT = configuration['pgsql']['PGSQL_PORT']
PGSQL_USERNAME = configuration['pgsql']['PGSQL_USERNAME']
PGSQL_PASSWORD = configuration['pgsql']['PGSQL_PASSWORD']
PGSQL_DB_NAME = configuration['pgsql']['PGSQL_DB_NAME']


engine = create_engine(f'postgresql://{PGSQL_USERNAME}:{PGSQL_PASSWORD}@{PGSQL_HOST}:{PGSQL_PORT}/{PGSQL_DB_NAME}')
Session = sessionmaker(bind=engine)

def get_qa_queue_len():
    session = Session()
    try:
        # Select documents with status 'to_classify'
        documents = session.query(Document).filter_by(status='pending').all()
        return(len(documents))
    except Exception as e:
        session.rollback()
        print(f"Error: {e}")
    finally:
        session.close()


if __name__ == "__main__":
    number_of_documents = get_qa_queue_len()
    print(f'number of documents in QA queue: {number_of_documents}')
