import pika
import yaml
import json
import os
import sys
import traceback

from minio import Minio
from minio.error import S3Error

from sqlalchemy import create_engine, not_
from sqlalchemy.orm import declarative_base,sessionmaker

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import Document, Splitted_Document


configuration = yaml.safe_load(open("config.yml"))

MINIO_URI = configuration['minio']['MINIO_URI']
MINIO_ACCESS_KEY = configuration['minio']['MINIO_ACCESS_KEY']
MINIO_SECRET_KEY = configuration['minio']['MINIO_SECRET_KEY']
MINIO_FILES_BUCKET = configuration['minio']['MINIO_FILES_BUCKET']
MINIO_OBJECT_URI_PREFIX = configuration['minio']['MINIO_OBJECT_URI_PREFIX']

PGSQL_HOST = configuration['pgsql']['PGSQL_HOST']
PGSQL_PORT = configuration['pgsql']['PGSQL_PORT']
PGSQL_USERNAME = configuration['pgsql']['PGSQL_USERNAME']
PGSQL_PASSWORD = configuration['pgsql']['PGSQL_PASSWORD']
PGSQL_DB_NAME = configuration['pgsql']['PGSQL_DB_NAME']


engine = create_engine(f'postgresql://{PGSQL_USERNAME}:{PGSQL_PASSWORD}@{PGSQL_HOST}:{PGSQL_PORT}/{PGSQL_DB_NAME}')
Session = sessionmaker(bind=engine)

minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=True
)


# Start a session
session = Session()

try:
    # Update status of Documents not 'done' and not 'in_review' and not 'to_qa_postprocess' and not 'to_upload'
    documents_to_update = session.query(Document)\
        .filter(not_(Document.status.in_(['done', 'in_review', 'to_qa_postprocess', 'to_upload'])))

    # Execute update
    documents_to_update.update({"status": "to_classify"}, synchronize_session='fetch')

    # Collect Splitted_Document file names/IDs before deletion
    splitted_documents_to_delete = session.query(Splitted_Document.uuid)\
        .join(Document, Splitted_Document.parent_document_uuid == Document.uuid)\
        .filter(not_(Document.status.in_(['done', 'in_review', 'to_qa_postprocess', 'to_upload']))).all()

    splitted_documents_files = [doc.uuid for doc in splitted_documents_to_delete]

    # Delete related Splitted_Documents
    # Since we just updated these documents to 'to_classify', their uuids match the previous query's condition
    documents_uuids_to_classify = [doc.uuid for doc in documents_to_update.all()]

    if documents_uuids_to_classify:
        session.query(Splitted_Document)\
            .filter(Splitted_Document.parent_document_uuid.in_(documents_uuids_to_classify))\
            .delete(synchronize_session='fetch')

    # Delete corresponding files from MinIO
    for splitted_document_uuid in splitted_documents_files:
        minio_client.remove_object(MINIO_FILES_BUCKET, f'{splitted_document_uuid}')
    print(f"Deleted {len(splitted_documents_files)} files from MinIO.")

except Exception as e:
    # Handle exceptions
    print(f"session.rollback()")
    session.rollback()
    traceback.print_exc()

finally:
    # Close the session
    session.commit()
    print(f"{len(documents_uuids_to_classify)} Document records updated and related {len(splitted_documents_files)} Splitted_Document records deleted successfully.")
    session.close()
