import ssl

import paramiko
import pika
import json
import os
import sys
import re

from datetime import datetime, timezone
from time import sleep
from pprint import pprint
from minio import Minio
from minio.error import S3Error
from io import BytesIO
from statistics import median

from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient
from azure.storage.blob import generate_blob_sas, BlobSasPermissions
from azure.servicebus import ServiceBusClient, ServiceBusMessage
import io
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass

from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.orm.attributes import flag_modified
import uuid
from sqlalchemy_utils import UUIDType

from pathvalidate import sanitize_filename

from pipeline_utils.rabbitmq_connector import PikaServiceFactory
from pipeline_utils.database_connector import DBConnector
from pipeline_utils.tenant_utils import get_tenant_processing_config, default_tenant_config

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from models import IncomingPackage, Document, Splitted_Document, RawIncomingPackages
from pipeline_utils import JSONFormat, fill_mapped_fields
from pipeline_utils.monitoring import MonitorService, Status

# Project and app configuration
PROJECT_NAME = os.environ.get('PROJECT_NAME', '')
APP_NAME = os.environ.get('APP_NAME', '')
TENANT_NAME = os.environ.get('TENANT_NAME', '')

# Azure configuration
AZURE_STORAGE_CONNECTION_STRING = os.environ.get('AZURE_STORAGE_CONNECTION_STRING', '')
AZURE_STORAGE_CONTAINER_NAME = os.environ.get('AZURE_STORAGE_CONTAINER_NAME', '')
AZURE_SERVICE_BUS_CONNECTION_STRING = os.environ.get('AZURE_SERVICE_BUS_CONNECTION_STRING', '')
AZURE_SERVICE_BUS_TOPIC_NAME = os.environ.get('AZURE_SERVICE_BUS_TOPIC_NAME', '')

# Uploader configuration
uploader_channel = os.environ.get('UPLOADER_CHANNEL', 'sftp')
ADD_VALIDATION_REPORT_DATA = os.environ.get('ADD_VALIDATION_REPORT_DATA', 'no').lower() == 'yes'

# SFTP configuration
SFTP_UPLOAD_HOST = os.environ.get('SFTP_UPLOAD_HOST', '127.0.0.1')
SFTP_UPLOAD_PORT = int(os.environ.get('SFTP_UPLOAD_PORT', '2222'))
SFTP_UPLOAD_USER = os.environ.get('SFTP_UPLOAD_USER', '')
SFTP_UPLOAD_PASSWORD = os.environ.get('SFTP_UPLOAD_PASSWORD', '')
SFTP_UPLOAD_PATH = os.environ.get('SFTP_UPLOAD_PATH', 'Documents')
SORT_NAMING_BY_DATE = os.environ.get('SORT_NAMING_BY_DATE', 'false').lower() == 'true'

# MinIO configuration
MINIO_URI = os.environ.get('MINIO_URI', '127.0.0.1:9015')
MINIO_ACCESS_KEY = os.environ.get('MINIO_ACCESS_KEY', '')
MINIO_SECRET_KEY = os.environ.get('MINIO_SECRET_KEY', '')
MINIO_FILES_BUCKET = os.environ.get('MINIO_FILES_BUCKET', 'from-sftp')
MINIO_SECURE = os.environ.get('MINIO_SECURE', 'true').lower() == 'true'

# RabbitMQ configuration
RABBITMQ_HOST = os.environ.get('RABBITMQ_HOST', 'localhost')
RABBITMQ_PORT = int(os.environ.get('RABBITMQ_PORT', '5682'))
RABBITMQ_USERNAME = os.environ.get('RABBITMQ_USERNAME', '')
RABBITMQ_PASSWORD = os.environ.get('RABBITMQ_PASSWORD', '')
RABBITMQ_TO_UPLOAD_QUEUE_NAME = os.environ.get('RABBITMQ_TO_UPLOAD_QUEUE_NAME', 'to_upload')

# Remote RabbitMQ configuration
REMOTE_RABBITMQ_HOST = os.environ.get('REMOTE_RABBITMQ_HOST', '')
REMOTE_RABBITMQ_PORT = int(os.environ.get('REMOTE_RABBITMQ_PORT', '5671'))
REMOTE_RABBITMQ_VHOST = os.environ.get('REMOTE_RABBITMQ_VHOST', '/')
REMOTE_RABBITMQ_USERNAME = os.environ.get('REMOTE_RABBITMQ_USERNAME', '')
REMOTE_RABBITMQ_PASSWORD = os.environ.get('REMOTE_RABBITMQ_PASSWORD', '')
REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME = os.environ.get('REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME', '')

# PostgreSQL configuration
PGSQL_HOST = os.environ.get('PGSQL_HOST', 'localhost')
PGSQL_PORT = int(os.environ.get('PGSQL_PORT', '5438'))
PGSQL_USERNAME = os.environ.get('PGSQL_USERNAME', '')
PGSQL_PASSWORD = os.environ.get('PGSQL_PASSWORD', '')
PGSQL_DB_NAME = os.environ.get('PGSQL_DB_NAME', '')

# Remote PostgreSQL configuration
PGSQL_HOST_REMOTE = os.environ.get('PGSQL_HOST_REMOTE', '')
PGSQL_PORT_REMOTE = int(os.environ.get('PGSQL_PORT_REMOTE', '5432'))
PGSQL_USERNAME_REMOTE = os.environ.get('PGSQL_USERNAME_REMOTE', '')
PGSQL_PASSWORD_REMOTE = os.environ.get('PGSQL_PASSWORD_REMOTE', '')
PGSQL_DB_NAME_REMOTE = os.environ.get('PGSQL_DB_NAME_REMOTE', '')
PGSQL_SSL_MODE_REMOTE = os.environ.get('PGSQL_SSL_MODE_REMOTE', 'require')

# Monitoring configuration
API_HOST = os.environ.get('MONITOR_HOST', 'http://_address_here_')
API_PORT = os.environ.get('MONITOR_PORT', '')

# SSL configuration for RabbitMQ
REMOTE_SSL_CAFILE_PATH = os.environ.get('REMOTE_SSL_CAFILE_PATH', '')
REMOTE_SSL_CERTFILE_PATH = os.environ.get('REMOTE_SSL_CERTFILE_PATH', '')
REMOTE_SSL_KEYFILE_PATH = os.environ.get('REMOTE_SSL_KEYFILE_PATH', '')

print(f"Validation report will be saved to custom1 field: {ADD_VALIDATION_REPORT_DATA}")

context = None
if REMOTE_SSL_CAFILE_PATH and REMOTE_SSL_CERTFILE_PATH and REMOTE_SSL_KEYFILE_PATH:
    try:
        context = ssl.create_default_context(cafile=REMOTE_SSL_CAFILE_PATH)
        context.load_cert_chain(certfile=REMOTE_SSL_CERTFILE_PATH,
                                keyfile=REMOTE_SSL_KEYFILE_PATH)
        print("SSL context for RabbitMQ configured successfully.")
    except Exception as e:
        print(f"Error configuring SSL context for RabbitMQ: {e}")
        context = None
else:
    print("SSL configuration not found in environment variables. Proceeding without SSL for RabbitMQ.")

# **THIS is the important line** – only wrap the context when it exists
ssl_options = pika.SSLOptions(context) if context else None

Base = declarative_base()
RemoteBase = declarative_base()

local_db_connector = DBConnector(
    pgsql_host=PGSQL_HOST,
    pgsql_port=PGSQL_PORT,
    pgsql_username=PGSQL_USERNAME,
    pgsql_password=PGSQL_PASSWORD,
    pgsql_db_name=PGSQL_DB_NAME
)

remote_db_connector = DBConnector(
    pgsql_host=PGSQL_HOST_REMOTE,
    pgsql_port=PGSQL_PORT_REMOTE,
    pgsql_username=PGSQL_USERNAME_REMOTE,
    pgsql_password=PGSQL_PASSWORD_REMOTE,
    pgsql_db_name=PGSQL_DB_NAME_REMOTE,
    pgsql_sslmode=PGSQL_SSL_MODE_REMOTE
)

# Create tables if not already created.
Base.metadata.create_all(local_db_connector.get_engine())
RemoteBase.metadata.create_all(remote_db_connector.get_engine())

SessionRemoteDB = remote_db_connector.get_session
Session = local_db_connector.get_session

rmq_service_factory = PikaServiceFactory(
    host=RABBITMQ_HOST,
    port=RABBITMQ_PORT,
    username=RABBITMQ_USERNAME,
    password=RABBITMQ_PASSWORD,
    ssl_options=None
)
rmq_service = rmq_service_factory.create_service()
rmq_service.start()

minio_client = Minio(
    MINIO_URI,
    access_key=MINIO_ACCESS_KEY,
    secret_key=MINIO_SECRET_KEY,
    secure=MINIO_SECURE
)

# Initializing Azure Blob Storage and Service Bus clients
if uploader_channel == "servicebus_topic":
    blob_service_client: BlobServiceClient = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
    service_bus_client: ServiceBusClient = ServiceBusClient.from_connection_string(AZURE_SERVICE_BUS_CONNECTION_STRING)


def mkdir_p(sftp, remote_directory):
    """Create remote directory recursively if it does not exist."""
    dirs_ = []
    dir_ = remote_directory
    while len(dir_) > 1:
        dirs_.append(dir_)
        dir_, _ = os.path.split(dir_)
    if len(dir_) == 1 and not dir_.startswith("/"):
        dirs_.append(dir_)  # For a remote path like 'dirname/subdir/'
    while len(dirs_):
        dir_ = dirs_.pop()
        try:
            sftp.listdir(dir_)
        except IOError:
            sftp.mkdir(dir_)


def trim_file_name(file_name: str, max_length: int = 245) -> str:
    """
    Trims the file name to ensure it does not exceed the maximum length (excluding the file extension).

    Args:
        file_name (str): The original file name.
        max_length (int): The maximum allowed length for the file name (excluding extension).

    Returns:
        str: The trimmed file name.
    """
    if len(file_name) > max_length:
        file_name = file_name[:max_length]
    return file_name


def send_metadata_to_sftp(metadata, file_path):
    # Prepare file stream object contains json with metadata
    json_str = json.dumps(metadata, sort_keys=False, indent=4)
    json_bytes = json_str.encode('utf-8')
    file_stream = BytesIO(json_bytes)

    # Setup SSH client for SFTP upload
    ssh_client_remote = paramiko.SSHClient()
    ssh_client_remote.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh_client_remote.connect(hostname=SFTP_UPLOAD_HOST, port=SFTP_UPLOAD_PORT, username=SFTP_UPLOAD_USER,
                              password=SFTP_UPLOAD_PASSWORD, look_for_keys=False)
    ftp = ssh_client_remote.open_sftp()

    # Check if the file exists and modify the file_path to prevent overwriting
    remote_path = f"{SFTP_UPLOAD_PATH}/{file_path}"
    base_path, extension = os.path.splitext(remote_path)
    index = 1
    remote_path = f"{base_path}{extension}"
    while True:
        try:
            ftp.stat(remote_path)  # This will raise an IOError if the file does not exist
            remote_path = f"{base_path} {index}{extension}"  # File exists, modify the name
            index += 1
        except IOError:
            break  # The file does not exist, safe to upload

    # Check if the remote directory exists, create if it doesn't
    mkdir_p(ftp, os.path.dirname(remote_path))
    # Upload the file
    ftp.putfo(file_stream, remote_path)

    # Clean up
    file_stream.close()
    ftp.close()
    ssh_client_remote.close()
    print(f"metadata file {remote_path} send to sftp")


def send_file_to_sftp(file_id, file_path):
    # Connect to the MinIO client and get the object
    response = minio_client.get_object(MINIO_FILES_BUCKET, file_id)
    file_stream = BytesIO(response.read())

    # Setup SSH client for SFTP upload
    ssh_client_remote = paramiko.SSHClient()
    ssh_client_remote.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh_client_remote.connect(hostname=SFTP_UPLOAD_HOST, port=SFTP_UPLOAD_PORT, username=SFTP_UPLOAD_USER,
                              password=SFTP_UPLOAD_PASSWORD, look_for_keys=False)
    ftp = ssh_client_remote.open_sftp()

    # Check if the file exists and modify the file_path to prevent overwriting
    remote_path = f"{SFTP_UPLOAD_PATH}/{file_path}"
    base_path, extension = os.path.splitext(remote_path)
    index = 1
    remote_path = f"{base_path}{extension}"
    while True:
        try:
            ftp.stat(remote_path)  # This will raise an IOError if the file does not exist
            remote_path = f"{base_path} {index}{extension}"  # File exists, modify the name
            index += 1
        except IOError:
            break  # The file does not exist, safe to upload

    # Check if the remote directory exists, create if it doesn't
    mkdir_p(ftp, os.path.dirname(remote_path))
    # Upload the file
    ftp.putfo(file_stream, remote_path)

    # Clean up
    file_stream.close()
    ftp.close()
    ssh_client_remote.close()
    print(f"file {remote_path} send to sftp")


def send_metadata_to_azure(metadata: Dict[str, Any], incoming_package) -> None:
    """
    Uploads metadata to Azure Blob Storage.

    Args:
        metadata (Dict[str, Any]): The metadata to upload.
        incoming_package: The package containing the metadata with fields `original_name` and `uuid`.
    """
    json_str = json.dumps(metadata, sort_keys=False, indent=4)
    json_bytes = json_str.encode('utf-8')
    file_stream = io.BytesIO(json_bytes)

    # Generate the file path based on incoming_package's original_name and uuid
    file_path = f"{incoming_package.original_name}_{incoming_package.uuid}_{uuid.uuid4()}.json"

    blob_client = blob_service_client.get_blob_client(container=AZURE_STORAGE_CONTAINER_NAME, blob=file_path)
    blob_client.upload_blob(file_stream, overwrite=True)

    print(f'Metadata uploaded to Azure Blob Storage with path: {file_path}')


def send_file_to_azure(file_id: str, file_path: str) -> str:
    """
    Uploads a file to Azure Blob Storage.

    Args:
        file_id (str): The ID of the file to upload.
        file_path (str): The path where the file will be stored in Azure Blob Storage.
    """
    response = minio_client.get_object(MINIO_FILES_BUCKET, file_id)
    file_stream = io.BytesIO(response.read())

    blob_client = blob_service_client.get_blob_client(container=AZURE_STORAGE_CONTAINER_NAME, blob=file_path)
    blob_client.upload_blob(file_stream, overwrite=True)

    print(f"File uploaded to Azure Blob Storage: {file_path} (URL: {blob_client.url})")

    return blob_client.url


def publish_message_to_servicebus(data: Dict[str, Any]) -> None:
    """
    Publishes a message with metadata to an Azure Service Bus topic.

    Args:
        data (Dict[str, Any]): The metadata to publish.
    """
    # Print the message to the console before sending it to Service Bus
    print(f"Message to be sent to Azure Service Bus: {json.dumps(data, indent=4)}")

    topic_client = service_bus_client.get_topic_sender(topic_name=AZURE_SERVICE_BUS_TOPIC_NAME)
    message = ServiceBusMessage(json.dumps(data))
    topic_client.send_messages(message)
    print(f"Published message to Azure Service Bus Topic: {AZURE_SERVICE_BUS_TOPIC_NAME}")


def get_blob_url(file_path: str) -> str:
    """
    Generates a SAS URL for a file in Azure Blob Storage.

    Args:
        file_path (str): The path of the file in Azure Blob Storage.

    Returns:
        str: The SAS URL for the file.
    """
    blob_client = blob_service_client.get_blob_client(container=AZURE_STORAGE_CONTAINER_NAME, blob=file_path)
    sas_token = generate_blob_sas(
        account_name=blob_client.account_name,
        container_name=blob_client.container_name,
        blob_name=blob_client.blob_name,
        account_key=blob_service_client.credential.account_key,
        permission=BlobSasPermissions(read=True),
        expiry=datetime.utcnow() + timedelta(days=14)  # TODO expiration time
    )
    return f"{blob_client.url}?{sas_token}"


def get_if_exists(metadata, field_names):
    for field_name in field_names:
        try:
            return metadata[field_name].upper()
        except:
            pass
    return ''


def get_only_digits(text):
    if not text:
        return None
    return ''.join(c for c in text if c.isdigit())


# def get_only_alpha(text):
# if not text:
# return None
# return ''.join(c for c in text if c.isalpha() or c == ' ')

def ensure_unique_filename(file_name, used_names_counter):
    """
    Check if 'file_name' was used before.
    If it was, increment the counter and append an index to ensure uniqueness.
    used_names_counter — a dictionary of the form { 'filename': count_of_usage }.
    """
    if file_name not in used_names_counter:
        used_names_counter[file_name] = 1
        return file_name
    else:
        used_names_counter[file_name] += 1
        return f"{file_name}_{used_names_counter[file_name]}"


def get_tenant_upload_config(tenant_id=None, subtenant_id=None):
    """
    Get tenant-specific upload configuration.
    
    Args:
        tenant_id: Tenant ID
        subtenant_id: Subtenant ID
    
    Returns:
        Dictionary containing upload configuration
    """
    if not tenant_id:
        return {
            'use_tenant_paths': False,
            'custom_naming_convention': None,
            'upload_destinations': {},
            'notification_settings': {}
        }
    
    try:
        processing_config = get_tenant_processing_config(tenant_id, subtenant_id)
        tenant_config = default_tenant_config.get_tenant_config(tenant_id, subtenant_id)
        
        upload_config = tenant_config.get('upload_settings', {})
        
        return {
            'use_tenant_paths': upload_config.get('use_tenant_paths', True),
            'custom_naming_convention': upload_config.get('custom_naming_convention'),
            'upload_destinations': upload_config.get('destinations', {}),
            'notification_settings': upload_config.get('notifications', {})
        }
    except Exception as e:
        print(f"Error getting tenant upload config: {e}")
        return {
            'use_tenant_paths': False,
            'custom_naming_convention': None,
            'upload_destinations': {},
            'notification_settings': {}
        }


def get_file_path(splitted_document, incoming_package, used_names_counter, tenant_id=None, subtenant_id=None):
    """
    Determines the appropriate file path and name for a splitted document with tenant-aware naming.

    Args:
        splitted_document: The splitted document object.
        incoming_package: The incoming package object.
        used_names_counter: Counter for ensuring unique names.
        tenant_id: Tenant ID for tenant-specific paths
        subtenant_id: Subtenant ID for tenant-specific paths

    Returns:
        tuple: (file_path, pdf_file_name, doc_date)
    """
    # Get tenant-specific upload configuration
    upload_config = get_tenant_upload_config(tenant_id, subtenant_id)
    
    metadata = splitted_document.metadata_human or splitted_document.metadata_ml

    patient_first_name = get_if_exists(metadata["namingData"], ['patientFirstName'])
    patient_middle_name = get_if_exists(metadata["namingData"], ['patientMiddleName'])
    patient_last_name = get_if_exists(metadata["namingData"], ['patientLastName'])
    data = {
        'Package Name': incoming_package.original_name,
        'UUID': splitted_document.uuid,
        'Document Name': splitted_document.document_type.upper(),
        'Packet Received Date': incoming_package.received_date.strftime("%Y%m%d"),
        # "20231227" for the first arriba package
        'Document Received Date': get_if_exists(metadata["namingData"], ['docReceivedDate']),
        'Sender Name': get_if_exists(metadata["namingData"], ['senderName']),
        'Document Date': get_only_digits(get_if_exists(metadata["namingData"], ['docDate'])),
        'Claim Number': get_if_exists(metadata["namingData"], ['claimNumber']),
        'State': get_if_exists(metadata["namingData"], ['docState']),
        'Patient Name': f"{patient_first_name} {patient_last_name}",
        'Patient Name RFA': f"{patient_first_name} {patient_last_name} {patient_middle_name}",
        'Provider or Facility Name': get_if_exists(metadata["namingData"], ['providerOrFacilityName'])
    }

    # Apply custom naming convention if specified by tenant
    custom_naming = upload_config.get('custom_naming_convention')
    if custom_naming:
        print(f"Applying custom naming convention for tenant {tenant_id}: {custom_naming}")
        # Custom naming logic could be implemented here based on the convention
    
    file_naming_list = {
        'Other': f"{data['Claim Number']} {data['Sender Name']} {data['Document Name']} {data['Patient Name']} {data['Document Received Date']} {data['Packet Received Date']}",
        'RFA': f"{data['Claim Number']} {data['Sender Name']} {data['Document Name']} {data['Patient Name RFA']} {data['State']} {data['Document Date']} {data['Document Received Date']} {data['Packet Received Date']}",
        'Medical Records': f"{data['Claim Number']} {data['Sender Name']} {data['Document Name']} {data['Patient Name']} {data['Provider or Facility Name']} {data['Document Received Date']} {data['Packet Received Date']}",
        'Misc Correspondence': f"{data['Claim Number']} {data['Sender Name']} {data['Document Name']} {data['Patient Name']} {data['Document Received Date']} {data['Packet Received Date']}",
        'Legal Correspondence': f"{data['Document Name']} {data['Packet Received Date']}",
        'State forms': f"{data['Document Name']} {data['Packet Received Date']}",
        'Ratings': f"{data['Document Name']} {data['Packet Received Date']}",
        'Subpoena': f"{data['Document Name']} {data['Packet Received Date']}",
        'EOB/EOR': f"{data['Document Name']} {data['Packet Received Date']}",
        'IMR/IME/QME': f"{data['Claim Number']} {data['Sender Name']} {data['Patient Name']} {data['Document Name']} {data['State']} {data['Document Date']} {data['Document Received Date']} {data['Packet Received Date']}",
        'Supplemental/Work Status': f"{data['Claim Number']} {data['Sender Name']} {data['Document Name']} {data['Patient Name']} {data['State']} {data['Document Date']} {data['Document Received Date']} {data['Packet Received Date']}",
        'Physician Bill (HCFA)': f"{data['Claim Number']} {data['Sender Name']} {data['Document Name']} {data['Patient Name']}  {data['Document Received Date']} {data['Packet Received Date']}",
        'Hospital Bill (UB)': f"{data['Claim Number']} {data['Sender Name']} {data['Document Name']} {data['Patient Name']} {data['Document Received Date']} {data['Packet Received Date']}",
        'Translation Bills': f"{data['Document Name']} {data['Packet Received Date']}",
        'Determination - Med Auth': f"{data['Claim Number']} {data['Sender Name']} {data['Document Name']} {data['Patient Name']} {data['State']} {data['Document Date']} {data['Document Received Date']} {data['Packet Received Date']}",
        'Vocational Evaluations': f"{data['Document Name']} {data['Packet Received Date']}",
        'Case Management Notes': f"{data['Claim Number']} {data['Sender Name']} {data['Document Name']} {data['Patient Name']} {data['Document Date']} {data['Document Received Date']} {data['Packet Received Date']}",
        'Fax': f"{data['Claim Number']} {data['Sender Name']} {data['Document Name']} {data['Patient Name']} {data['Document Received Date']} {data['Packet Received Date']}",
        'Injury/Illness/FROI': f"{data['Claim Number']} {data['Sender Name']} {data['Patient Name']} {data['Document Name']} {data['State']} {data['Document Date']} {data['Document Received Date']} {data['Packet Received Date']}"
    }
    file_naming_list = {k.lower(): v for k, v in file_naming_list.items()}
    file_name = file_naming_list[splitted_document.document_type.lower()]
    file_name = file_name.replace('None', '')
    file_name = sanitize_filename(file_name, replacement_text=' ')
    file_name = trim_file_name(file_name)
    file_name = ensure_unique_filename(file_name, used_names_counter)

    # Build file path with tenant isolation
    path_components = []
    
    # Add tenant-specific path prefix if enabled
    if upload_config['use_tenant_paths'] and tenant_id:
        path_components.append(f"tenant_{tenant_id}")
        if subtenant_id:
            path_components.append(f"subtenant_{subtenant_id}")
    
    # Add existing path structure
    received_date = incoming_package.received_date.strftime('%Y%m%d')
    transaction_id = str(incoming_package.original_name) + "_" + str(
        splitted_document.parent_document.uuid)
    
    path_components.extend([received_date, transaction_id])
    
    file_path = os.path.join(*path_components, file_name)
    
    print(f"Generated tenant-aware file path for tenant {tenant_id}: {file_path}")
    
    return file_path, file_name + '.pdf', data['Document Date']


def get_splitted_document(splitted_document_file_id):
    global session
    document = session.query(Splitted_Document).filter_by(uuid=splitted_document_file_id).first()
    return document


def check_if_all_docs_and_splitted_docs_are_processed(incoming_package, splitted_document_uuid):
    # target IncomingPackage UUID
    target_uuid = incoming_package.uuid

    # Query to find all Splitted_Document items related to the IncomingPackage with the given UUID
    splitted_documents = session.query(Splitted_Document). \
        join(Document, Document.uuid == Splitted_Document.parent_document_uuid). \
        join(IncomingPackage, IncomingPackage.uuid == Document.incoming_package_uuid). \
        filter(IncomingPackage.uuid == target_uuid). \
        all()

    for doc in splitted_documents:
        # if doc.uuid != splitted_document_uuid:
        if doc.status != 'to_upload':
            return False, None
    return True, splitted_documents


def get_package_json(incoming_package, splitted_documents, documents_info):
    # Extract parameters from incoming_data and add them to the JSON object
    incoming_data = incoming_package.incoming_data
    print(f"incoming_data type: {type(incoming_data)}")  # Diagnostics
    print(f"incoming_data content: {incoming_data}")  # Diagnostics

    if isinstance(incoming_data, str):
        incoming_data = json.loads(incoming_data)
    data = {
        "packetId": "",
        "transactionId": "",
        "packetFileName": incoming_package.original_name,
        "pagesCount": len(splitted_documents[0].parent_document.initial_predictions_ml),
        "packetReceivedTime": "",
        "packetConfidence": 0,
        "custom1": splitted_documents[0].parent_document.validation_report if ADD_VALIDATION_REPORT_DATA else "",
        "custom2": "",
        "custom3": "",
        "statusCode": 0,
        "documentsCount": len(splitted_documents),
        "documents": []
    }
    data["packetFileName"] = incoming_package.original_name
    data["packetId"] = str(incoming_package.uuid)

    if incoming_data:
        data["custom2"] = incoming_data.get("custom2")
        data["custom3"] = incoming_data.get("custom3")
        incoming_data.get("blobName") is not None and data.update({"blobName": incoming_data.get("blobName")})
        incoming_data.get("claimId") is not None and data.update({"claimId": incoming_data.get("claimId")})
        incoming_data.get("referralId") is not None and data.update({"referralId": incoming_data.get("referralId")})
        incoming_data.get("clientId") is not None and data.update({"clientId": incoming_data.get("clientId")})
        incoming_data.get("transactionTime") is not None and data.update(
            {"transactionTime": incoming_data.get("transactionTime")})
        incoming_data.get("id") is not None and data.update({"id": incoming_data.get("id")})
        incoming_data.get("tenantId") is not None and data.update({"tenantId": incoming_data.get("tenantId")})
        incoming_data.get("subTenantId") is not None and data.update({"subTenantId": incoming_data.get("subTenantId")})
        incoming_data.get("tenantName") is not None and data.update({"tenantName": incoming_data.get("tenantName")})
        incoming_data.get("echoParams") is not None and data.update({"echoParams": incoming_data.get("echoParams")})
    overall_confidences = []

    for doc_info in documents_info:
        metadata = {}
        metadata.update(doc_info.splitted_document.metadata_human or doc_info.splitted_document.metadata_ml or {})
        metadata.pop("namingData", None)
        metadata.pop("document_pages", None)
        metadata.pop("doc_type", None)
        metadata["docName"] = doc_info.file_name
        print(f"metadata['docName'] = {doc_info.file_name} in package_json")
        metadata["docType"] = doc_info.splitted_document.document_type
        metadata["pagesRef"] = doc_info.splitted_document.parent_document_pages
        metadata[
            "docConfidence"] = 100 if doc_info.splitted_document.metadata_human else doc_info.splitted_document.classification_confidence * 100
        data["documents"].append(metadata)

        doc_info.splitted_document.overall_confidence = 1.0 if doc_info.splitted_document.metadata_human else doc_info.splitted_document.overall_confidence
        if doc_info.splitted_document.overall_confidence:
            overall_confidences.append(doc_info.splitted_document.overall_confidence)

    data["packetConfidence"] = median(overall_confidences) * 100 if overall_confidences else None
    return data


def get_package_json_servicebus(incoming_package: IncomingPackage, splitted_documents: List[Splitted_Document]) -> Dict[
    str, Any]:
    """
    Creates a JSON object with package metadata, including SAS URLs for documents.

    Args:
        incoming_package (IncomingPackage): The incoming package containing the documents.
        splitted_documents (List[Splitted_Document]): The list of splitted documents.

    Returns:
        Dict[str, Any]: The JSON object with package metadata.
    """
    # Extract parameters from incoming_data and add them to the JSON object
    incoming_data = incoming_package.incoming_data
    print(f"incoming_data type: {type(incoming_data)}")  # Diagnostics
    print(f"incoming_data content: {incoming_data}")  # Diagnostics

    if isinstance(incoming_data, str):
        incoming_data = json.loads(incoming_data)

    data = {
        "packetId": "",
        "transactionId": "",
        "packetFileName": incoming_package.original_name,
        "pagesCount": len(splitted_documents[0].parent_document.initial_predictions_ml),
        "packetConfidence": 0,
        "custom1": "",
        "custom2": "",
        "custom3": "",
        "statusCode": 0,
        "documentsCount": len(splitted_documents),
        "documents": []
    }
    if incoming_data:
        data["custom1"] = incoming_data.get("custom1")
        data["custom2"] = incoming_data.get("custom2")
        data["custom3"] = incoming_data.get("custom3")
        incoming_data.get("blobName") is not None and data.update({"blobName": incoming_data.get("blobName")})
        incoming_data.get("claimId") is not None and data.update({"claimId": incoming_data.get("claimId")})
        incoming_data.get("referralId") is not None and data.update({"referralId": incoming_data.get("referralId")})
        incoming_data.get("clientId") is not None and data.update({"clientId": incoming_data.get("clientId")})
        incoming_data.get("transactionTime") is not None and data.update(
            {"transactionTime": incoming_data.get("transactionTime")})
        incoming_data.get("id") is not None and data.update({"id": incoming_data.get("id")})
        incoming_data.get("tenantId") is not None and data.update({"tenantId": incoming_data.get("tenantId")})
        incoming_data.get("subTenantId") is not None and data.update({"subTenantId": incoming_data.get("subTenantId")})
        incoming_data.get("tenantName") is not None and data.update({"tenantName": incoming_data.get("tenantName")})
        incoming_data.get("echoParams") is not None and data.update({"echoParams": incoming_data.get("echoParams")})
        incoming_data.get("id") is not None and data.update({"transactionId": incoming_data.get("id")})

    data["packetFileName"] = incoming_package.original_name
    data["packetId"] = str(incoming_package.uuid)

    overall_confidences = []
    # Dictionary to track duplicate filenames
    used_names_counter = {}
    sorted_splitted_documents = sorted(splitted_documents,
                                       key=lambda x: min(int(page[0]) for page in x.parent_document_pages))
    for splitted_document in sorted_splitted_documents:
        metadata = {}
        metadata.update(splitted_document.metadata_human or splitted_document.metadata_ml or {})
        metadata.pop("namingData", None)
        metadata.pop("document_pages", None)
        metadata.pop("doc_type", None)
        document_file_path, file_name, doc_date = get_file_path(splitted_document, incoming_package, used_names_counter)

        pdf_path = document_file_path + '.pdf'
        splitted_document_file_id = str(splitted_document.uuid)
        blob_url = send_file_to_azure(splitted_document_file_id, pdf_path)

        metadata["docName"] = file_name
        metadata["docType"] = splitted_document.document_type
        metadata["pagesRef"] = splitted_document.parent_document_pages
        metadata[
            "docConfidence"] = 100 if splitted_document.metadata_human else splitted_document.classification_confidence * 100
        metadata["sasUri"] = get_blob_url(pdf_path)
        data["documents"].append(metadata)

        splitted_document.overall_confidence = 1.0 if splitted_document.metadata_human else splitted_document.overall_confidence
        if splitted_document.overall_confidence:
            overall_confidences.append(splitted_document.overall_confidence)

    data["packetConfidence"] = median(overall_confidences) * 100 if overall_confidences else None

    return data


@dataclass
class DocumentInfo:
    """A model to store data for the second pass."""
    splitted_document: Splitted_Document
    document_file_path: str
    file_name: str
    doc_date: str
    pdf_path: str


def sort_documents_by_date(documents_info: List[DocumentInfo]) -> List[DocumentInfo]:
    """
    Sorts a list of DocumentInfo objects by document date.
    Documents without a date (None or empty) are placed at the end.

    :param documents_info: The list of DocumentInfo to sort.
    :return: A new list of DocumentInfo, sorted by date ascending.
    """
    return sorted(
        documents_info,
        key=lambda doc: (
            doc.doc_date is None or not doc.doc_date.strip(),  # True => goes to the end
            doc.doc_date.strip() if doc.doc_date else ''
        )
    )


def normalize_doc_date(doc_date: Optional[str], doc_id: str) -> Optional[str]:
    """
    Normalizes the document date to ensure it's in 'yyyymmdd' format or None.

    Args:
        doc_date (Optional[str]): The original document date.
        doc_id (str): The unique identifier of the document.

    Returns:
        Optional[str]: The normalized date if valid, else None.
    """
    if not doc_date or not doc_date.strip():
        return None
    else:
        doc_date_stripped = doc_date.strip()
        if len(doc_date_stripped) == 8 and doc_date_stripped.isdigit():
            return doc_date_stripped
        else:
            print(f"Invalid date format for document {doc_id}: '{doc_date}'. Setting date to None.")
            return None


def sort_documents_by_date_with_custom_none(documents_info: List[DocumentInfo]) -> List[DocumentInfo]:
    """
    Sorts documents by date, but if a document has no date,
    it is placed immediately after its predecessor in the original list.

    The original list is assumed to be in ascending order of page numbers.
    (i.e., sorted by the minimal page index in each splitted_document.)

    :param documents_info: The list of DocumentInfo objects to sort.
                           They should be pre-sorted by page numbers.
    :return: A new list of DocumentInfo sorted by date with custom handling for no-date docs.
    """

    # Separate documents that have a date vs those that do not.
    date_known = [d for d in documents_info if d.doc_date and d.doc_date.strip()]
    date_none = [d for d in documents_info if not d.doc_date or not d.doc_date.strip()]

    # Sort the known-date documents lexicographically by the date string.
    # (Ensure your dates are stored in a format that sorts correctly, e.g. YYYY-MM-DD.)
    date_known_sorted = sorted(date_known, key=lambda d: d.doc_date.strip())

    # We'll build the final sorted list by starting with all date-known docs sorted by date.
    final_list = list(date_known_sorted)

    def find_predecessor_in_final(doc: DocumentInfo) -> Optional[DocumentInfo]:
        """
        Finds the closest predecessor (in the original documents_info list)
        that is already placed in final_list.
        """
        # Get the index of 'doc' in the original (page-sorted) documents_info.
        idx = documents_info.index(doc)
        # Move backwards to find the first doc that is already in final_list.
        for i in range(idx - 1, -1, -1):
            if documents_info[i] in final_list:
                return documents_info[i]
        return None

    # Iterate over docs with no date, in their original (page-sorted) order,
    # and insert each of them after its predecessor that is already in final_list.
    for doc in date_none:
        predecessor = find_predecessor_in_final(doc)
        if predecessor is None:
            # If no predecessor is found, insert at the beginning.
            final_list.insert(0, doc)
        else:
            # Otherwise, insert right after the predecessor.
            pred_index = final_list.index(predecessor)
            final_list.insert(pred_index + 1, doc)

    return final_list


def upload_data(splitted_documents: List['Splitted_Document'], incoming_package: 'IncomingPackage') -> None:
    """
    Uploads documents and metadata to the appropriate storage
    based on the channel configuration (SFTP or Service Bus topic).

    Steps:
      1. Sort the splitted_documents by page numbers.
      2. Build a list of DocumentInfo objects for each splitted document.
      3. For each DocumentInfo:
         - Set status to 'uploaded'
         - Normalize its doc_date
         - If not sorting by date or not SFTP, upload immediately
      4. If SORT_NAMING_BY_DATE and channel == 'sftp',
         then sort documents by date with a custom rule:
         * a doc without a date is placed immediately after its predecessor
           in the original (page-sorted) list.
         Then upload them in that new order.
      5. Finally, create and upload the package JSON (index.json).

    :param splitted_documents: The list of Splitted_Document objects to upload.
    :param incoming_package: The IncomingPackage containing details about the original package.
    """
    # Sort splitted_documents by page numbers
    splitted_documents_sorted_by_pages = sorted(
        splitted_documents,
        key=lambda x: min(int(page[0]) for page in x.parent_document_pages)
    )

    # Build a list of DocumentInfo from the page-sorted documents
    used_names_counter = {}
    documents_info: List[DocumentInfo] = []
    for splitted_document in splitted_documents_sorted_by_pages:
        document_file_path, file_name, doc_date = get_file_path(
            splitted_document,
            incoming_package,
            used_names_counter
        )
        doc_info = DocumentInfo(
            splitted_document=splitted_document,
            document_file_path=document_file_path,
            file_name=file_name,
            doc_date=doc_date,
            pdf_path=document_file_path + '.pdf',
        )
        documents_info.append(doc_info)

    # First pass - set status, normalize dates, upload if not sorting
    for doc_info in documents_info:
        doc_info.splitted_document.status = 'uploaded'
        metadata = doc_info.splitted_document.metadata_human or doc_info.splitted_document.metadata_ml
        print(f"Metadata for document {doc_info.file_name}: {metadata}")

        splitted_document_file_id = str(doc_info.splitted_document.uuid)
        # Normalize the doc_date
        doc_info.doc_date = normalize_doc_date(doc_info.doc_date, splitted_document_file_id)

        if uploader_channel == 'sftp':
            if not SORT_NAMING_BY_DATE:
                # If we are not sorting by date, upload directly
                print(f"Uploading file {doc_info.pdf_path} to sftp server...")
                send_file_to_sftp(splitted_document_file_id, doc_info.pdf_path)
        elif uploader_channel == 'servicebus_topic':
            # For service bus, optionally upload to Azure or handle differently
            print(f"Uploading file {doc_info.pdf_path} to Azure Blob Storage...")
            # send_file_to_azure(splitted_document_file_id, doc_info.pdf_path)

    # If we need to sort by date on SFTP, do custom date sorting, then upload
    if SORT_NAMING_BY_DATE and uploader_channel == 'sftp':
        # Custom date sorting
        documents_info = sort_documents_by_date_with_custom_none(documents_info)

        print("Documents sorted by date (custom rule). Renaming and uploading in that order:")

        # Add a zero-padded sequential index at the beginning of file_name and pdf_path
        for i, doc_info in enumerate(documents_info, start=1):
            prefix = str(i).zfill(4)  # Creates "0001", "0002", ...
            old_file_name = doc_info.file_name
            new_file_name = f"{prefix}_{old_file_name}"

            doc_info.file_name = new_file_name

            # Update local pdf path accordingly
            old_pdf_path = doc_info.pdf_path
            base_dir = os.path.dirname(old_pdf_path)
            new_pdf_path = os.path.join(base_dir, new_file_name)
            doc_info.pdf_path = new_pdf_path

        # Now upload in sorted order, using the updated names
        for doc_info in documents_info:
            doc_id = str(doc_info.splitted_document.uuid)
            print(f"Uploading file {doc_info.pdf_path} (date={doc_info.doc_date}) to sftp server...")
            send_file_to_sftp(doc_id, doc_info.pdf_path)

    print("All files are uploaded (or queued for upload).")

    # Build the package JSON and upload/publish it
    if uploader_channel == 'servicebus_topic':
        package_json = get_package_json_servicebus(incoming_package, splitted_documents)
        publish_message_to_servicebus(package_json)
    else:
        package_json = get_package_json(incoming_package, splitted_documents, documents_info)

    # Prepare the path for the JSON metadata file
    name = 'index.json'
    received_date = incoming_package.received_date.strftime('%Y%m%d')
    transaction_id = f"{incoming_package.original_name}_{splitted_documents[0].parent_document.uuid}"
    json_path = os.path.join(received_date, transaction_id, name)

    if uploader_channel == 'sftp':
        print(f"Uploading file {json_path} to sftp server...")
        send_metadata_to_sftp(package_json, json_path)
    elif uploader_channel == 'servicebus_topic':
        print(f"Uploading file {json_path} to Azure Blob Storage...")
        send_metadata_to_azure(package_json, incoming_package)

    print("Upload process completed.")


def most_frequent_string(lst):
    if not lst:
        return ""

    string_count = {}
    max_count = 0
    most_frequent = None

    for item in lst:
        if isinstance(item, str):
            string_count[item] = string_count.get(item, 0) + 1
            if string_count[item] > max_count:
                max_count = string_count[item]
                most_frequent = item

    return most_frequent


def extract_best_values(all_metadata):
    claim_numbers = []
    patient_first_names = []
    patient_middle_names = []
    patient_last_names = []

    for metadata in all_metadata:
        if "namingData" in metadata:
            if "claimNumber" in metadata["namingData"]:
                claim_number = metadata["namingData"]["claimNumber"]
                if claim_number and re.match(r'^[a-zA-Z0-9#\s-]{6,16}$', claim_number):
                    claim_numbers.append(claim_number)

            if "patientFirstName" in metadata["namingData"]:
                patient_name = metadata["namingData"]["patientFirstName"]
                if patient_name and re.match(r'^[a-zA-Z\s,.]{4,30}$', patient_name):
                    patient_first_names.append(patient_name)

            if "patientMiddleName" in metadata["namingData"]:
                patient_name = metadata["namingData"]["patientMiddleName"]
                if patient_name and re.match(r'^[a-zA-Z\s,.]{4,30}$', patient_name):
                    patient_middle_names.append(patient_name)

            if "patientLastName" in metadata["namingData"]:
                patient_name = metadata["namingData"]["patientLastName"]
                if patient_name and re.match(r'^[a-zA-Z\s,.]{4,30}$', patient_name):
                    patient_last_names.append(patient_name)

    best_claim_number = most_frequent_string(claim_numbers)
    best_patient_first_name = most_frequent_string(patient_first_names)
    best_patient_middle_name = most_frequent_string(patient_middle_names)
    best_patient_last_name = most_frequent_string(patient_last_names)

    return best_claim_number.title(), best_patient_first_name.title(), best_patient_middle_name.title(), best_patient_last_name.title()


def collapse_metadata(expanded_json):
    """
    Restores the default key-value dictionary structure from the expanded internal structure format.

    Args:
        expanded_json (dict): The dictionary with expanded metadata where values are in the format {"value": "", "confidence": 0, "valid": None, "required": True}.

    Returns:
        dict: A new dictionary with the collapsed metadata, restoring the default key-value structure.

    Steps:
        1. Define a helper function 'process_value' to recursively process the values and collapse them.
        2. For each value in the dictionary:
            a. If the value is a dictionary and contains the key 'value', return the value associated with the 'value' key.
            b. If the value is a dictionary but does not contain the 'value' key, recursively process each key-value pair in the dictionary.
            c. If the value is a list, recursively process each item in the list.
            d. If the value is neither a dictionary nor a list, return the value as is.
        3. Return a new dictionary where each value is processed using the 'process_value' function.

    Notes:
        - This function is used to simplify the metadata structure by removing additional fields like 'confidence', 'valid' and "required".
    """

    def process_value(value):
        if isinstance(value, dict):
            if "value" in value:
                return value["value"]
            elif all(key in value for key in ['confidence', 'required', 'valid']):
                return None
            else:
                return {k: process_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [process_value(v) for v in value]
        else:
            return value

    return {key: process_value(value) for key, value in expanded_json.items()}


def update_dict(dict1, dict2):
    # print(f"\n\ndict2: {dict2}\n")
    for key, value in dict2.items():
        try:
            if isinstance(value, dict):
                # print(f"\n\n{dict1}\n{key}\n{value}\n")
                dict1[key] = update_dict(dict1.get(key, {}), value)
            else:
                dict1[key] = value
        except TypeError:
            print("TypeError (update_dict)")
            # sys.exit()
    return dict1


def update_metadata(splitted_documents):
    """
    Updates the metadata for a list of splitted documents.

    Args:
        splitted_documents (list): A list of splitted document objects to update metadata for.

    Steps:
        1. Declare the global session variable.
        2. Initialize an empty list to store all metadata from the documents.
        3. For each splitted document:
            a. If the document has machine learning (ML) metadata:
                i. Collapse the ML metadata.
                ii. Format the metadata according to the document type.
                iii. Update the formatted metadata with the collapsed metadata.
                iv. Set the document's ML metadata to the updated formatted metadata.
                v. Mark the ML metadata field as modified.
            b. If the document has human-provided metadata:
                i. Collapse the human metadata.
                ii. Format the metadata according to the document type.
                iii. Update the formatted metadata with the collapsed metadata.
                iv. Set the document's human metadata to the updated formatted metadata.
                v. Mark the human metadata field as modified.
            c. Append the document's human or ML metadata (or an empty dictionary if none exist) to the all_metadata list.
        4. Extract the best values for 'claimNumber' and 'patientName' from all the collected metadata.
        5. For each splitted document:
            a. Create a dictionary containing the best 'claimNumber' and 'patientName'.
            b. If the document has human-provided metadata:
                i. Update the human metadata with the best values.
                ii. Fill mapped fields in the human metadata.
                iii. Mark the human metadata field as modified.
            c. If the document has ML metadata:
                i. Update the ML metadata with the best values.
                ii. Fill mapped fields in the ML metadata.
                iii. Mark the ML metadata field as modified.
            d. If the document has no metadata, create ML metadata with the best values and mark it as modified.
        6. Commit the changes to the session to save all updates to the database.
    """

    global session

    # First, process each document's metadata by collapsing and formatting it.
    for splitted_document in splitted_documents:
        # Process machine learning metadata if available.
        if splitted_document.metadata_ml:
            collapsed_ml = collapse_metadata(splitted_document.metadata_ml)
            formatted_ml = JSONFormat.GetFieldsForDocument(splitted_document.document_type)
            update_dict(formatted_ml, collapsed_ml)
            splitted_document.metadata_ml = formatted_ml
            flag_modified(splitted_document, "metadata_ml")

        # Process human-provided metadata if available.
        if splitted_document.metadata_human:
            collapsed_human = collapse_metadata(splitted_document.metadata_human)
            formatted_human = JSONFormat.GetFieldsForDocument(splitted_document.document_type)
            update_dict(formatted_human, collapsed_human)
            splitted_document.metadata_human = formatted_human
            flag_modified(splitted_document, "metadata_human")

    # Now update the naming fields for each document individually.
    for splitted_document in splitted_documents:
        # Mark the appropriate metadata field as modified.
        if splitted_document.metadata_human:
            fill_mapped_fields(splitted_document.metadata_human)
            flag_modified(splitted_document, "metadata_human")
        else:
            fill_mapped_fields(splitted_document.metadata_ml)
            flag_modified(splitted_document, "metadata_ml")

    session.commit()


def send_status_to_monitoring(file_id_str, filename):
    message = {
        "document_uuid": file_id_str,
        "filename": filename,
        "tenant_name": TENANT_NAME,
        "status": "out"
    }
    remote_rmq_service_factory = PikaServiceFactory(
        host=REMOTE_RABBITMQ_HOST,
        port=REMOTE_RABBITMQ_PORT,
        virtual_host=REMOTE_RABBITMQ_VHOST,
        username=REMOTE_RABBITMQ_USERNAME,
        password=REMOTE_RABBITMQ_PASSWORD,
        ssl_options=ssl_options,
    )
    remote_rmq_service = remote_rmq_service_factory.create_service()
    remote_rmq_service.start()
    try:
        remote_rmq_service.send_message(routing_key=REMOTE_RABBITMQ_PACKET_STATUS_QUEUE_NAME,
                                        message=json.dumps(message))
    finally:
        remote_rmq_service.stop()


def callback(msg_tuple):
    ch, method, properties, body = msg_tuple
    """
    Callback function for RabbitMQ to process incoming messages.

    Args:
        ch: The channel object.
        method: The method frame.
        properties: The properties.
        body: The message body expected format: {'file_id': str, 'document_type': str, 'tenant_id': str, 'subtenant_id': str}
    """
    global session
    global sessionRemoteDB

    print(f" [x] Received {body}")
    queue_item = json.loads(body.decode('UTF-8'))

    splitted_document_file_id = queue_item['file_id']
    document_type = queue_item['document_type']
    # Extract tenant information from the message
    tenant_id = queue_item.get('tenant_id')
    subtenant_id = queue_item.get('subtenant_id')
    
    print(f"Uploading document type '{document_type}' for tenant: {tenant_id}, subtenant: {subtenant_id}")

    session = Session()
    try:
        splitted_document = get_splitted_document(splitted_document_file_id)
        if splitted_document:
            incoming_package = splitted_document.parent_document.incoming_package
            all_docs_and_splitted_docs_are_processed, splitted_documents = check_if_all_docs_and_splitted_docs_are_processed(
                incoming_package, splitted_document.uuid)
            if all_docs_and_splitted_docs_are_processed:
                print("All documents and splitted documents for the package are processed. \n\
                    Collecting metadata into json and uploading files to the appropriate storage..."
                      )
                update_metadata(splitted_documents)

                print("predictions_human:", splitted_document.parent_document.predictions_human)
                print("predictions_ml:", splitted_document.parent_document.predictions_ml)

                upload_data(splitted_documents, incoming_package)
                splitted_document.parent_document.status = 'uploaded'
                splitted_document.parent_document.uploaded = datetime.now(timezone.utc)
                incoming_package.status = 'uploaded'

                raw_incoming_package_id = incoming_package.raw_incoming_package_id

                sessionRemoteDB = SessionRemoteDB()
                data = sessionRemoteDB.query(RawIncomingPackages).filter_by(id=raw_incoming_package_id).first()

                data.status = "done"
                data.end_processing_date = datetime.now(timezone.utc)
                sessionRemoteDB.commit()
                
                print(f"Successfully uploaded package for tenant: {tenant_id}, subtenant: {subtenant_id}")
            else:
                print("Waiting for other documents to be processed")
        else:
            print(f"splitted document does not exist")

        # Commenting out status monitoring
        # if all_docs_and_splitted_docs_are_processed:
        #     send_status_to_monitoring(str(splitted_document.parent_document_uuid),
        #                               splitted_document.parent_document.file)

        session.commit()
        session.close()

        ch.basic_ack(delivery_tag=method.delivery_tag)
    except Exception as e:
        print("Error processing: ", e)
    # connection.close() # if uncomment, process only one message


rmq_service.read_messages(RABBITMQ_TO_UPLOAD_QUEUE_NAME, callback)
# monitor = MonitorService(PROJECT_NAME, APP_NAME, 'uploader', API_HOST, API_PORT)
# monitor.start_monitoring()
# monitor.update_status(Status.UP)
rmq_service.run()
