#!/usr/bin/python3

import psycopg2
import yaml


configuration = yaml.safe_load(open("../../config.yml"))

def drop_table():
    # Database connection parameters
    db_name = configuration['pgsql']['PGSQL_DB_NAME']
    db_user = configuration['pgsql']['PGSQL_USERNAME']
    db_password = configuration['pgsql']['PGSQL_PASSWORD']
    db_host = configuration['pgsql']['PGSQL_HOST']
    db_port = configuration['pgsql']['PGSQL_PORT']
    
    # Connect to the database
    conn = psycopg2.connect(
        dbname=db_name,
        user=db_user,
        password=db_password,
        host=db_host,
        port=db_port
    )

    # Create a cursor object
    cur = conn.cursor()

    # Execute a query to drop the table
    cur.execute("DROP TABLE documents")

    # Commit the transaction
    conn.commit()

    # Close the cursor and connection
    cur.close()
    conn.close()
    print("Table 'documents' has been dropped.")


# Main script
print("WARNING: You are about to drop the 'documents' table. This action cannot be undone.")
response = input("Type 'yes' to continue, or 'no' to cancel: ")

if response.lower() == 'yes':
    drop_table()
else:
    print("Operation cancelled. The table has not been dropped.")
