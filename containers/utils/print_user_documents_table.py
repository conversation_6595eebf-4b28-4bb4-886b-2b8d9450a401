#!/usr/bin/python3

import psycopg2
import yaml

from tabulate import tabulate

configuration = yaml.safe_load(open("../../config.yml"))

# Database connection parameters
db_name = configuration['pgsql']['PGSQL_DB_NAME']
db_user = configuration['pgsql']['PGSQL_USERNAME']
db_password = configuration['pgsql']['PGSQL_PASSWORD']
db_host = configuration['pgsql']['PGSQL_HOST']
db_port = configuration['pgsql']['PGSQL_PORT']

# Connect to the database
conn = psycopg2.connect(
    dbname=db_name,
    user=db_user,
    password=db_password,
    host=db_host,
    port=db_port
)

# Create a cursor object
cur = conn.cursor()

# Execute a query
cur.execute("SELECT * FROM user_documents")

row = cur.fetchone()
headers = tuple(desc[0] for desc in cur.description)

blobs_list = []
while row is not None:
    truncated_row = tuple(str(item)[:25] for item in row)  # Truncate each column to 25 chars
    blobs_list.append(truncated_row)
    row = cur.fetchone()
#    blobs_list.append(row)
#    row = cur.fetchone()

print(tabulate(blobs_list, headers))

# Close the cursor and connection
cur.close()
conn.close()
