version: "3.5"
services:
  sftp-atom-1:
    image:          atmoz/sftp
    container_name: sftp-atom-1
    restart:        always
    command:        <user>:<password>:<linux_user_id>
    ports:
      - "2222:22"
    volumes:
      - ./sftp/test100pdf:/home/<USER>/Documents

  sftp-atom-2:
    image:          atmoz/sftp
    container_name: sftp-atom-2
    restart:        always
    command:        <user>:<password>:<linux_user_id>
    ports:
      - "2223:22"
    volumes:
      - ./sftp/results:/home/<USER>/Documents

  minio-atom-1:
    image:          minio/minio
    container_name: minio-atom-1
    restart:        always
    command:        server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER:      put_your_data
      MINIO_ROOT_PASSWORD:  put_your_data
    ports:
      - '9015:9000'
      - '9016:9001'
    volumes:
      - ./minio:/data
      - "path to certificate":/root/.minio/certs/public.crt
      - "path to private key":/root/.minio/certs/private.key

  rabbit-atom-1:
    image:          rabbitmq:3-management
    container_name: rabbit-atom-1
    restart:        always
    environment:
      RABBITMQ_ERLANG_COOKIE:   "put_your_data"
      RABBITMQ_DEFAULT_USER:    "put_your_data"
      RABBITMQ_DEFAULT_PASS:    "put_your_data"
      RABBITMQ_DEFAULT_VHOST:   "/"
    ports:
      - '15682:15672'
      - '5682:5672'
      - '5681:5671'
    volumes:
       - ./rabbitmq:/var/lib/rabbitmq
       - ./rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
       - "path to certificates":/etc/ssl/ca

  dbpg-atom-1:
    image:              postgres:16
    container_name:     dbpg-atom-1
    restart:            always
    environment:
      POSTGRES_USER:     put_your_data
      POSTGRES_PASSWORD: put_your_data
      POSTGRES_DB:       put_your_data
      PGDATA:            /var/lib/postgresql/data/pgdata
      command: -c ssl=on -c ssl_cert_file=/var/lib/postgresql/server.crt -c ssl_key_file=/var/lib/postgresql/server.key
    ports:
      - '5438:5432'
    volumes:
      - ./dbpg:/var/lib/postgresql/data
      - "path to certificate":/var/lib/postgresql/server.crt 
      - "path to private key":/var/lib/postgresql/server.key
