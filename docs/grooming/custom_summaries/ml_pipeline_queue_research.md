# Ml Pipeline queues per process

Process containers

- classifier
- downloader
- llm-server
- metadata-extractor
- metadata-post-processor
- qa-backend
- qa-frontend
- qa-post-processor
- splitter
- uploader
- validate-route


/	
training_packet_status
classic	D Args	running	0	0	0	0.00/s	0.00/s	0.00/s
/	
training_queue_ariba_from_backend_qa
classic	D Args	running	0	0	0			
/	
training_queue_training_from_backend_qa
classic	D Args	running	0	0	0	0.00/s	0.00/s	0.00/s
/	
training_to_backend_over_qa
classic	D Args	running	0	0	0			
/	
training_to_backend_qa
classic	D Args	running	0	7	7	0.00/s	0.00/s	0.00/s
/	
training_to_classify
classic	D Args	running	0	0	0	0.00/s	0.00/s	0.00/s
/	
training_to_extract_metadata
classic	D Args	running	0	1	1	0.00/s	0.00/s	0.00/s
/	
training_to_postprocess_metadata
classic	D Args	running	977	2	979	0.00/s	0.00/s	0.00/s
/	
training_to_screenshot_postprocess
classic	D Args	running	0	0	0			
/	
training_to_split
classic	D Args	running	0	0	0	0.00/s	0.00/s	0.00/s
/	
training_to_upload
classic	D Args	running	0	0	0	0.00/s	0.00/s	0.00/s
/	
training_to_validate
classic	D Args	running	0	0	0	0.00/s	0.00/s	0.00/s
