# 🐰 Record Ranger ML Pipeline - RabbitMQ Queue Architecture Analysis

## 📋 Executive Summary

The Record Ranger ML pipeline utilizes a sophisticated RabbitMQ-based messaging architecture to orchestrate healthcare document processing workflows. The system processes documents through multiple stages including classification, splitting, metadata extraction, validation, and upload, with comprehensive quality assurance (QA) integration and tenant-aware routing capabilities.

### 🏗️ Key Architecture Characteristics
- **Asynchronous Processing**: Each stage operates independently via message queues
- **Tenant Isolation**: Multi-tenant support with configurable queue routing
- **HIPAA Compliance**: Secure message handling for healthcare data
- **Fault Tolerance**: Message acknowledgment and retry mechanisms
- **Scalability**: Horizontal scaling through containerized microservices

---

## 🔄 Core ML Pipeline Queues

### Primary Processing Flow

| Queue Name | Purpose | Producer(s) | Consumer(s) | Message Format |
|------------|---------|-------------|-------------|----------------|
| `to_classify` | Document classification requests | downloader | classifier | `{'file_id': str, 'filename': str, 'tenant_id': str, 'subtenant_id': str}` |
| `to_split` | Document splitting requests | classifier | splitter | `{'file_id': str, 'filename': str, 'tenant_id': str, 'subtenant_id': str}` |
| `to_extract_metadata` | Metadata extraction requests | splitter | metadata-extractor | `{'file_id': str, 'document_type': str, 'tenant_id': str, 'subtenant_id': str}` |
| `to_postprocess_metadata` | Metadata post-processing | metadata-extractor | metadata-post-processor | `{'file_id': str, 'document_type': str, 'tenant_id': str, 'subtenant_id': str}` |
| `to_validate` | Document validation requests | metadata-post-processor | validate-route | `{'file_id': str, 'document_type': str, 'tenant_id': str, 'subtenant_id': str}` |
| `to_upload` | Final upload requests | validate-route | uploader | `{'file_id': str, 'document_type': str, 'tenant_id': str, 'subtenant_id': str}` |

---

## 🎯 Quality Assurance (QA) Queues

### QA Backend Integration

| Queue Name | Purpose | Producer(s) | Consumer(s) | Message Format |
|------------|---------|-------------|-------------|----------------|
| `to_backend_qa` | Documents requiring QA review | validate-route | qa-backend | Complex document chunk data with metadata |
| `from_backend_qa` | QA-reviewed documents | qa-backend | qa-post-processor | `{'documentId': str, 'data': dict, 'token': str}` |
| `to_backend_over_qa` | Documents bypassing QA | validate-route | qa-backend | Document status updates |

### QA Post-Processing

| Queue Name | Purpose | Producer(s) | Consumer(s) | Notes |
|------------|---------|-------------|-------------|-------|
| `to_qa_postprocess` | QA post-processing tasks | qa-post-processor | uploader | Re-routes to upload after QA |

---

## 📊 Monitoring & Status Queues

### Pipeline Status Tracking

| Queue Name | Purpose | Producer(s) | Consumer(s) | Message Format |
|------------|---------|-------------|-------------|----------------|
| `packet_status` | Document processing status | downloader, uploader | qa-backend | `{'document_uuid': str, 'filename': str, 'tenant_name': str, 'status': str}` |

### Screenshot Processing

| Queue Name | Purpose | Producer(s) | Consumer(s) | Notes |
|------------|---------|-------------|-------------|-------|
| `to_screenshot` | Screenshot generation requests | Various | screenshot-processor | Optional feature |
| `to_screenshot_postprocess` | Screenshot post-processing | screenshot-processor | qa-backend | OCR text extraction |

---

## 🏢 Tenant-Aware Queue Routing

### Multi-Tenant Architecture

The system supports tenant-specific queue routing for enhanced isolation and customization:

#### Tenant Queue Naming Patterns
- **Standard**: `{base_queue_name}`
- **Tenant-Specific**: `{tenant_id}_{base_queue_name}`
- **Subtenant-Specific**: `{tenant_id}_{subtenant_id}_{base_queue_name}`

#### Environment-Specific Prefixes
- **Development**: Standard queue names
- **Stage**: Standard queue names  
- **Production**: Standard queue names
- **Ariba Environment**: `ariba_{base_queue_name}`
- **Training Environment**: `training_{base_queue_name}`

### Tenant Configuration Examples

```yaml
# Tenant-specific queue routing
queue_routing:
  use_tenant_specific_queues: true
  queue_prefix: "tenant_a"
  enable_priority_queues: false
  queue_isolation_level: "tenant"
```

---

## 🔧 Containerized Process Analysis

### 📥 Downloader Service
- **Consumes**: External data sources (SFTP, ServiceBus, SQS)
- **Produces**: `to_classify`, `packet_status`
- **Function**: Ingests documents from various channels
- **Tenant Support**: ✅ Extracts tenant info from file paths/messages

### 🏷️ Classifier Service  
- **Consumes**: `to_classify`
- **Produces**: `to_split`
- **Function**: AI-powered document classification
- **Tenant Support**: ✅ Applies tenant-specific classification rules

### ✂️ Splitter Service
- **Consumes**: `to_split`
- **Produces**: `to_extract_metadata`
- **Function**: Splits multi-page documents into individual documents
- **Tenant Support**: ✅ Tenant-specific splitting configuration

### 📝 Metadata Extractor Service
- **Consumes**: `to_extract_metadata`
- **Produces**: `to_postprocess_metadata`
- **Function**: Extracts structured metadata using ML models
- **Tenant Support**: ✅ Tenant-aware metadata extraction

### 🔄 Metadata Post-Processor Service
- **Consumes**: `to_postprocess_metadata`
- **Produces**: `to_validate`
- **Function**: Post-processes and validates extracted metadata
- **Tenant Support**: ✅ Tenant-specific validation rules

### ✅ Validate-Route Service
- **Consumes**: `to_validate`
- **Produces**: `to_upload`, `to_backend_qa`, `to_backend_over_qa`
- **Function**: Validates documents and routes based on confidence scores
- **Tenant Support**: ✅ Tenant-aware routing decisions

### 📤 Uploader Service
- **Consumes**: `to_upload`
- **Produces**: `packet_status`
- **Function**: Uploads processed documents to final destinations
- **Tenant Support**: ✅ Tenant-specific upload configurations

### 🎯 QA Backend Service
- **Consumes**: `to_backend_qa`, `to_backend_over_qa`, `packet_status`, `to_screenshot_postprocess`
- **Produces**: `from_backend_qa`
- **Function**: Manages quality assurance workflow
- **Tenant Support**: ✅ Tenant-isolated QA processes

### 🖥️ QA Frontend Service
- **Consumes**: None (HTTP-based)
- **Produces**: None (HTTP-based)
- **Function**: User interface for QA review
- **Tenant Support**: ✅ Tenant-specific UI contexts

### 🔄 QA Post-Processor Service
- **Consumes**: `from_backend_qa`
- **Produces**: `to_upload`
- **Function**: Processes QA-reviewed documents
- **Tenant Support**: ✅ Maintains tenant context

### 🤖 LLM Server Service
- **Consumes**: None (HTTP API)
- **Produces**: None (HTTP API)
- **Function**: Provides AI/ML inference capabilities
- **Tenant Support**: ⚠️ Stateless service, no direct queue interaction

---

## 🔍 Queue Naming Inconsistencies Found

### Potential Typos/Variations
1. **`to_postprocess_metadata`** vs **`to_metadata_postprocess`**
   - Most configs use `to_metadata_postprocess`
   - Some deployment configs show `to_postprocess_metadata`
   - **Recommendation**: Standardize on `to_metadata_postprocess`

2. **Tenant-specific queue variations**:
   - `ariba_to_backend_qa` (Ariba environment)
   - `training_to_backend_qa` (Training environment)
   - Standard: `to_backend_qa`

---

## 📈 Message Flow Patterns

### Standard Document Processing Flow
```
External Source → downloader → to_classify → classifier → to_split → splitter → 
to_extract_metadata → metadata-extractor → to_metadata_postprocess → 
metadata-post-processor → to_validate → validate-route → to_upload → uploader
```

### QA Review Flow
```
validate-route → to_backend_qa → qa-backend → from_backend_qa → 
qa-post-processor → to_upload → uploader
```

### Status Monitoring Flow
```
downloader → packet_status → qa-backend
uploader → packet_status → qa-backend
```

---

## 🛡️ Security & Compliance Considerations

### HIPAA Compliance Features
- **Message Encryption**: SSL/TLS for RabbitMQ connections
- **Tenant Isolation**: Separate queues and processing contexts
- **Audit Logging**: Message tracking through status queues
- **Access Control**: Service-specific credentials and permissions

### SSL Configuration
- Certificate-based authentication for remote RabbitMQ connections
- Environment-specific SSL certificate management
- Fallback to non-SSL for local development environments

---

## 🔧 Infrastructure Components

### RabbitMQ Configuration
- **Management Console**: Port 15672 (HTTP), 15682 (Custom)
- **AMQP Connections**: Port 5672 (Standard), 5671 (SSL), 5682 (Custom)
- **Virtual Hosts**: Tenant-specific isolation
- **Queue Durability**: Persistent queues for reliability

### Supporting Infrastructure
- **MinIO**: S3-compatible object storage for document files
- **PostgreSQL**: Metadata and state management
- **Nginx**: Reverse proxy and load balancing
- **Prometheus/Grafana**: Monitoring and alerting

---

## 📊 Performance & Scaling Considerations

### Queue Performance Characteristics
- **Prefetch Count**: Configurable per service (default: varies)
- **Message Acknowledgment**: Manual acknowledgment for reliability
- **Dead Letter Queues**: Error handling and retry mechanisms
- **Connection Pooling**: Service-specific connection management

### Scaling Patterns
- **Horizontal Scaling**: Multiple replicas per service
- **Queue Partitioning**: Tenant-based queue distribution
- **Load Balancing**: Round-robin message distribution
- **Resource Allocation**: GPU/CPU-specific node assignments

## Diagram 

```mermaid
graph TB
    %% External Data Sources
    SFTP[📁 SFTP Server]
    SBUS[🚌 Azure Service Bus]
    SQS[📮 AWS SQS]
    
    %% Core ML Pipeline Services
    DOWN[📥 Downloader]
    CLASS[🏷️ Classifier]
    SPLIT[✂️ Splitter]
    META[📝 Metadata Extractor]
    METAPOST[🔄 Metadata Post-Processor]
    VALID[✅ Validate-Route]
    UPLOAD[📤 Uploader]
    
    %% QA Services
    QABACK[🎯 QA Backend]
    QAFRONT[🖥️ QA Frontend]
    QAPOST[🔄 QA Post-Processor]
    
    %% Supporting Services
    LLM[🤖 LLM Server]
    
    %% Core Pipeline Queues
    Q1[to_classify]
    Q2[to_split]
    Q3[to_extract_metadata]
    Q4[to_metadata_postprocess]
    Q5[to_validate]
    Q6[to_upload]
    
    %% QA Queues
    QQA1[to_backend_qa]
    QQA2[from_backend_qa]
    QQA3[to_backend_over_qa]
    QQA4[to_qa_postprocess]
    
    %% Status & Monitoring Queues
    QSTAT[packet_status]
    QSCREEN1[to_screenshot]
    QSCREEN2[to_screenshot_postprocess]
    
    %% Storage Systems
    MINIO[(🗄️ MinIO Storage)]
    POSTGRES[(🐘 PostgreSQL)]
    
    %% External Destinations
    DEST1[📤 SFTP Destination]
    DEST2[☁️ Azure Blob Storage]
    
    %% Data Source Connections
    SFTP --> DOWN
    SBUS --> DOWN
    SQS --> DOWN
    
    %% Core Pipeline Flow
    DOWN --> Q1
    Q1 --> CLASS
    CLASS --> Q2
    Q2 --> SPLIT
    SPLIT --> Q3
    Q3 --> META
    META --> Q4
    Q4 --> METAPOST
    METAPOST --> Q5
    Q5 --> VALID
    
    %% Validation Routing
    VALID --> Q6
    VALID --> QQA1
    VALID --> QQA3
    
    %% QA Flow
    QQA1 --> QABACK
    QQA3 --> QABACK
    QABACK --> QQA2
    QQA2 --> QAPOST
    QAPOST --> Q6
    
    %% Upload Flow
    Q6 --> UPLOAD
    
    %% Status Monitoring
    DOWN --> QSTAT
    UPLOAD --> QSTAT
    QSTAT --> QABACK
    
    %% Screenshot Processing (Optional)
    QSCREEN1 --> QSCREEN2
    QSCREEN2 --> QABACK
    
    %% Storage Interactions
    DOWN -.-> MINIO
    CLASS -.-> MINIO
    SPLIT -.-> MINIO
    META -.-> MINIO
    METAPOST -.-> MINIO
    VALID -.-> MINIO
    UPLOAD -.-> MINIO
    QAPOST -.-> MINIO
    
    %% Database Interactions
    DOWN -.-> POSTGRES
    CLASS -.-> POSTGRES
    SPLIT -.-> POSTGRES
    META -.-> POSTGRES
    METAPOST -.-> POSTGRES
    VALID -.-> POSTGRES
    UPLOAD -.-> POSTGRES
    QABACK -.-> POSTGRES
    QAPOST -.-> POSTGRES
    
    %% Final Destinations
    UPLOAD --> DEST1
    UPLOAD --> DEST2
    
    %% QA Frontend Connection (HTTP)
    QAFRONT -.-> QABACK
    
    %% LLM Server Connection (HTTP)
    META -.-> LLM
    CLASS -.-> LLM
    
    %% Styling
    classDef serviceBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef queueBox fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef storageBox fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef externalBox fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class DOWN,CLASS,SPLIT,META,METAPOST,VALID,UPLOAD,QABACK,QAFRONT,QAPOST,LLM serviceBox
    class Q1,Q2,Q3,Q4,Q5,Q6,QQA1,QQA2,QQA3,QQA4,QSTAT,QSCREEN1,QSCREEN2 queueBox
    class MINIO,POSTGRES storageBox
    class SFTP,SBUS,SQS,DEST1,DEST2 externalBox
    ```


---

*This analysis is based on the current codebase state and deployment configurations. Queue names and routing patterns may vary between environments and tenant configurations.*
