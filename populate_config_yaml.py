import string
import yaml
import random
from pprint import pprint

randomize_fields = ['PGSQL_USERNAME','PGSQL_PASSWORD','PGSQL_DB_NAME','RABBITMQ_USERNAME','RABBITMQ_PASSWORD','SFTP_PASSWORD','SFTP_USER','MINIO_ACCESS_KEY','MINIO_SECRET_KEY']
STRING_LENGTH = 8
# Function to load YAML file
def load_yaml(file_path):
    with open(file_path, 'r') as file:
        data = yaml.safe_load(file)
    return data

# Function to save YAML file
def save_yaml(data, file_path):
    with open(file_path, 'w') as file:
        yaml.safe_dump(data, file)

# Function to randomly modify a list of values
def modify_values(values):
    modified_values = [value * random.uniform(0.5, 1.5) for value in values]
    return modified_values

# Main function
def main(file_path):
    data = load_yaml(file_path)

    # Assuming the list of values is under a specific key in the YAML file
    # Adjust the key according to your YAML structure
    key = 'values'
    for key in data:
        # print(key)
        for subvalue in data[key]:
            # print(subvalue)
            if subvalue in randomize_fields:
                letters_and_digits = string.ascii_letters + string.digits
                data[key][subvalue] = ''.join(random.choice(letters_and_digits) for i in range(STRING_LENGTH))

    pprint(data)
    # save_yaml(data, file_path)
    print(f"Modified values saved to {file_path}")

if __name__ == "__main__":
    file_path = 'config.yml.sample'  # Replace with your YAML file path
    main(file_path)